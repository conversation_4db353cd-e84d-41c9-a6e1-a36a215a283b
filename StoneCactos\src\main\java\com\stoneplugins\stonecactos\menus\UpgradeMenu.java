package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class UpgradeMenu extends BaseMenu {

    private final CactusGenerator generator;

    public UpgradeMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = "§8Evoluir Desenvolvimento";
        int size = 27;

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro
        for (int i = 0; i < inventory.getSize(); i++) {
            if (i != 10 && i != 13 && i != 16 && i != 22) {
                inventory.setItem(i, createGlassPane());
            }
        }

        // Configurar upgrades
        setupCapacityUpgrade();
        setupQuantityUpgrade();
        setupTimeUpgrade();

        // Botão de voltar
        setupBackButton();
    }

    private void setupCapacityUpgrade() {
        String upgradeInfo = plugin.getUpgradeManager().getCapacityUpgradeInfo(generator);

        List<String> lore = new ArrayList<>();
        lore.add("§7Aumenta a capacidade máxima de torres");
        lore.add("§7que o gerador pode suportar.");
        lore.add("");
        lore.addAll(Arrays.asList(upgradeInfo.split("\n")));

        ItemStack item = createItem(Material.CHEST, (short) 0, "§eUpgrade de Capacidade", lore);

        setItem(10, item);
    }

    private void setupQuantityUpgrade() {
        String upgradeInfo = plugin.getUpgradeManager().getQuantityUpgradeInfo(generator);

        List<String> lore2 = new ArrayList<>();
        lore2.add("§7Aumenta a quantidade de torres");
        lore2.add("§7construídas por vez.");
        lore2.add("");
        lore2.addAll(Arrays.asList(upgradeInfo.split("\n")));

        ItemStack item = createItem(Material.valueOf("STAINED_CLAY"), (short) 5, "§eUpgrade de Quantidade", lore2);

        setItem(13, item);
    }

    private void setupTimeUpgrade() {
        String upgradeInfo = plugin.getUpgradeManager().getTimeUpgradeInfo(generator);

        List<String> lore3 = new ArrayList<>();
        lore3.add("§7Reduz o tempo necessário para");
        lore3.add("§7construir novas torres.");
        lore3.add("");
        lore3.addAll(Arrays.asList(upgradeInfo.split("\n")));

        ItemStack item = createItem(Material.WATCH, (short) 0, "§eUpgrade de Velocidade", lore3);

        setItem(16, item);
    }

    @Override
    protected void setupBackButton() {
        ItemStack backButton = createItem(Material.ARROW, "§cVoltar",
                "§7Clique para voltar ao menu principal");
        setItem(22, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        if (slot == 10) {
            handleCapacityUpgrade();
        } else if (slot == 13) {
            handleQuantityUpgrade();
        } else if (slot == 16) {
            handleTimeUpgrade();
        } else if (slot == 22) {
            handleBackClick();
        }
    }

    private void handleCapacityUpgrade() {
        // Verificar permissão
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Tentar fazer upgrade
        if (plugin.getUpgradeManager().upgradeCapacity(player, generator)) {
            update();
        }
    }

    private void handleQuantityUpgrade() {
        // Verificar permissão
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Tentar fazer upgrade
        if (plugin.getUpgradeManager().upgradeQuantityPerConstruction(player, generator)) {
            update();
        }
    }

    private void handleTimeUpgrade() {
        // Verificar permissão
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Tentar fazer upgrade
        if (plugin.getUpgradeManager().upgradeConstructionTime(player, generator)) {
            update();
        }
    }

    @Override
    protected void handleBackClick() {
        // Fechar inventário primeiro
        player.closeInventory();

        // Abrir menu principal após um delay para evitar conflitos
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getMenuManager().openMainMenu(player, generator);
        }, 2L);
    }
}
