package com.stoneplugins.stonetags;

import com.stoneplugins.stonetags.commands.*;
import com.stoneplugins.stonetags.database.DatabaseManager;
import com.stoneplugins.stonetags.listeners.PlayerListener;
import com.stoneplugins.stonetags.managers.ConfigManager;
import com.stoneplugins.stonetags.managers.TagManager;
import com.stoneplugins.stonetags.managers.SuffixManager;
import com.stoneplugins.stonetags.managers.PlayerDataManager;
import com.stoneplugins.stonetags.placeholders.TagPlaceholders;
import com.stoneplugins.stonetags.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

public class StoneTags extends JavaPlugin {

    private static StoneTags instance;

    // Managers
    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private TagManager tagManager;
    private SuffixManager suffixManager;
    private PlayerDataManager playerDataManager;
    private MessageUtils messageUtils;
    private com.stoneplugins.stonetags.animation.TagAnimationManager animationManager;

    // Placeholders
    private TagPlaceholders tagPlaceholders;

    @Override
    public void onEnable() {
        instance = this;

        getLogger().info("");
        getLogger().info("  _____ _______ ____  _   _ ______   _______       _____  _____ ");
        getLogger().info(" / ____|__   __/ __ \\| \\ | |  ____| |__   __|/\\   / ____|/ ____|");
        getLogger().info("| (___    | | | |  | |  \\| | |__       | |  /  \\ | |  __| (___  ");
        getLogger().info(" \\___ \\   | | | |  | | . ` |  __|      | | / /\\ \\| | |_ |\\___ \\ ");
        getLogger().info(" ____) |  | | | |__| | |\\  | |____     | |/ ____ \\ |__| |____) |");
        getLogger().info("|_____/   |_|  \\____/|_| \\_|______|    |_/_/    \\_\\_____|_____/ ");
        getLogger().info("");
        getLogger().info("StoneTags v" + getDescription().getVersion() + " - Stone Plugins");
        getLogger().info("Iniciando plugin...");

        // Inicializar managers
        initializeManagers();

        // Registrar comandos
        registerCommands();

        // Registrar listeners
        registerListeners();

        // Registrar placeholders
        registerPlaceholders();

        getLogger().info("StoneTags v" + getDescription().getVersion() + " habilitado com sucesso!");
    }

    @Override
    public void onDisable() {
        // Parar animações
        if (animationManager != null) {
            animationManager.stopAllAnimations();
        }

        // Salvar dados dos jogadores
        if (playerDataManager != null) {
            playerDataManager.saveAllPlayerData();
        }

        // Fechar conexão com banco de dados
        if (databaseManager != null) {
            databaseManager.closeConnection();
        }

        getLogger().info("StoneTags desabilitado!");
    }

    private void initializeManagers() {
        // Inicializar ConfigManager primeiro
        this.configManager = new ConfigManager(this);

        // Inicializar MessageUtils
        this.messageUtils = new MessageUtils(this);

        // Inicializar DatabaseManager
        this.databaseManager = new DatabaseManager(this);

        // Inicializar TagManager
        this.tagManager = new TagManager(this);

        // Inicializar SuffixManager
        this.suffixManager = new SuffixManager(this);

        // Inicializar PlayerDataManager
        this.playerDataManager = new PlayerDataManager(this);

        // Inicializar AnimationManager (Premium)
        this.animationManager = new com.stoneplugins.stonetags.animation.TagAnimationManager(this);
    }

    private void registerCommands() {
        // Comando /tag (abre menu)
        if (getCommand("tag") != null) {
            getCommand("tag").setExecutor(new TagCommand(this));
        } else {
            getLogger().severe("Erro: Comando 'tag' não encontrado no plugin.yml!");
        }

        // Comando /tagset (admin)
        if (getCommand("tagset") != null) {
            getCommand("tagset").setExecutor(new SetTagCommand(this));
        } else {
            getLogger().severe("Erro: Comando 'tagset' não encontrado no plugin.yml!");
        }

        // Comando /tagremove (admin)
        if (getCommand("tagremove") != null) {
            getCommand("tagremove").setExecutor(new RemoveTagCommand(this));
        } else {
            getLogger().severe("Erro: Comando 'tagremove' não encontrado no plugin.yml!");
        }

        // Comando /stonetags (admin)
        if (getCommand("stonetags") != null) {
            getCommand("stonetags").setExecutor(new StoneTagsCommand(this));
        } else {
            getLogger().severe("Erro: Comando 'stonetags' não encontrado no plugin.yml!");
        }
    }

    private void registerListeners() {
        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new com.stoneplugins.stonetags.listeners.ChatListener(this),
                this);
        getServer().getPluginManager().registerEvents(new com.stoneplugins.stonetags.listeners.MenuListener(this),
                this);
    }

    private void registerPlaceholders() {
        // Verificar se PlaceholderAPI está disponível
        if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
            this.tagPlaceholders = new TagPlaceholders(this);
            tagPlaceholders.register();
            getLogger().info("PlaceholderAPI integração ativada!");
        } else {
            getLogger().warning("PlaceholderAPI não encontrado - placeholders não estarão disponíveis!");
        }
    }

    public void reload() {
        // Recarregar configuração
        configManager.reloadConfig();

        // Recarregar tags
        tagManager.reloadTags();

        // Recarregar sufixos
        suffixManager.reloadSuffixes();

        // Recarregar mensagens
        messageUtils.reloadMessages();

        getLogger().info("Plugin recarregado com sucesso!");
    }

    // Getters
    public static StoneTags getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public TagManager getTagManager() {
        return tagManager;
    }

    public SuffixManager getSuffixManager() {
        return suffixManager;
    }

    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }

    public MessageUtils getMessageUtils() {
        return messageUtils;
    }

    public TagPlaceholders getTagPlaceholders() {
        return tagPlaceholders;
    }

    public com.stoneplugins.stonetags.animation.TagAnimationManager getAnimationManager() {
        return animationManager;
    }
}
