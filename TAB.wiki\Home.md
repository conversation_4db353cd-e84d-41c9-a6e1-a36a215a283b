# About the wiki
The wiki explains the functionality of the plugin as of version **5.2.5**.
If your config looks different or is missing some features, you are using an old version of the plugin.
To get the latest version, check [releases](https://github.com/NEZNAMY/TAB/releases/).  
Wiki for older plugin versions is not available.

If you plan to run your server for longer than 1 week,
it's a good idea to read this wiki as it explains everything about the plugin.
This will give you knowledge to do things you had no idea were possible, giving you an advantage over everyone else.

A lot of examples on the wiki use PlaceholderAPI placeholders without explaining its installation step by step. If you are going to use the examples and aren't familiar with the plugin, check out [Quick PlaceholderAPI startup guide](https://github.com/NEZNAMY/TAB/wiki/Quick-PlaceholderAPI-startup-guide).

# Limitations of plugins
TAB is a plugin (even on modded platforms, it is only a server-sided mod).
It only modifies the server behavior to your liking.
It does not add anything into the game.
Because of that, you'll need
to carefully read the limitations of each feature to know what they are capable of and what they are not capable of.
Some of the undesired behavior can be altered using a resource pack and some using a modified client.
Both of these are out of scope of TAB as a plugin; therefore, no advanced information regarding this is provided.

# Public plugin
TAB is a public plugin designed to satisfy the needs of a majority of servers.

This means it has functions not everyone will need. All you need to do is disable functions you don't want. Disabled features do not consume the CPU at all.

On the other hand, it cannot contain everything everyone can think of.
There must be a balance
between providing as much as possible while making the configuration as short and readable as possible.
This is not an easy task.
The majority of things that are not supported directly can be achieved in different ways.
Adding direct way for everything everyone asks for would not only take a lot of time,
but make the plugin harder to configure for everyone, as well as introduce a lot of duplicate ways to achieve things,
making the plugin overcomplicated.  
If your need is very specific and not available in the plugin in any way,
you should look into private plugin development.
You will end up with a plugin made just for you with everything you want and nothing else.  