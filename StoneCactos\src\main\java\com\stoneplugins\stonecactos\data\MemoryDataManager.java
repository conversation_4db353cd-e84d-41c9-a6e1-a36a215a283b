package com.stoneplugins.stonecactos.data;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Location;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Sistema de dados em memória para substituir temporariamente o banco de dados
 */
public class MemoryDataManager {
    
    private final StoneCactos plugin;
    
    // Dados dos geradores
    private final Map<String, Map<String, Object>> generators = new ConcurrentHashMap<>();
    
    // Dados dos jogadores
    private final Map<UUID, Map<String, Object>> playerData = new ConcurrentHashMap<>();
    
    // Boosters ativos
    private final Map<String, Map<String, Object>> activeBoosters = new ConcurrentHashMap<>();
    
    // Amigos dos geradores
    private final Map<String, Set<UUID>> generatorFriends = new ConcurrentHashMap<>();
    
    public MemoryDataManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    public void initialize() {
        plugin.getLogger().info("Sistema de dados em memória inicializado!");
    }
    
    // === MÉTODOS DE GERADORES ===
    
    public void saveGenerator(String id, UUID owner, Location location, Map<String, Object> data) {
        Map<String, Object> generatorData = new HashMap<>();
        generatorData.put("id", id);
        generatorData.put("owner", owner.toString());
        generatorData.put("world", location.getWorld().getName());
        generatorData.put("x", location.getX());
        generatorData.put("y", location.getY());
        generatorData.put("z", location.getZ());
        generatorData.putAll(data);
        
        generators.put(id, generatorData);
    }
    
    public Map<String, Object> getGenerator(String id) {
        return generators.get(id);
    }
    
    public void deleteGenerator(String id) {
        generators.remove(id);
        generatorFriends.remove(id);
    }
    
    public List<Map<String, Object>> getAllGenerators() {
        return new ArrayList<>(generators.values());
    }
    
    // === MÉTODOS DE JOGADORES ===
    
    public void updatePlayerStats(UUID playerId, int cactusGenerated, int cactusCollected, 
                                 double moneyEarned, int generatorsPlaced, int boostersUsed) {
        Map<String, Object> data = playerData.computeIfAbsent(playerId, k -> new HashMap<>());
        
        data.put("cactusGenerated", (Integer) data.getOrDefault("cactusGenerated", 0) + cactusGenerated);
        data.put("cactusCollected", (Integer) data.getOrDefault("cactusCollected", 0) + cactusCollected);
        data.put("moneyEarned", (Double) data.getOrDefault("moneyEarned", 0.0) + moneyEarned);
        data.put("generatorsPlaced", (Integer) data.getOrDefault("generatorsPlaced", 0) + generatorsPlaced);
        data.put("boostersUsed", (Integer) data.getOrDefault("boostersUsed", 0) + boostersUsed);
    }
    
    public int getTotalCactusGenerated(UUID playerId) {
        Map<String, Object> data = playerData.get(playerId);
        return data != null ? (Integer) data.getOrDefault("cactusGenerated", 0) : 0;
    }
    
    public Map<String, Object> getPlayerData(UUID playerId) {
        return playerData.getOrDefault(playerId, new HashMap<>());
    }
    
    // === MÉTODOS DE BOOSTERS ===
    
    public void saveActiveBooster(String generatorId, String boosterType, double multiplier, long expiryTime) {
        Map<String, Object> boosterData = new HashMap<>();
        boosterData.put("generatorId", generatorId);
        boosterData.put("boosterType", boosterType);
        boosterData.put("multiplier", multiplier);
        boosterData.put("expiryTime", expiryTime);
        
        activeBoosters.put(generatorId, boosterData);
    }
    
    public Map<String, Object> getActiveBooster(String generatorId) {
        return activeBoosters.get(generatorId);
    }
    
    public void removeActiveBooster(String generatorId) {
        activeBoosters.remove(generatorId);
    }
    
    public List<Map<String, Object>> getAllActiveBoosters() {
        return new ArrayList<>(activeBoosters.values());
    }
    
    // === MÉTODOS DE AMIGOS ===
    
    public void addFriend(String generatorId, UUID friendId) {
        generatorFriends.computeIfAbsent(generatorId, k -> new HashSet<>()).add(friendId);
    }
    
    public void removeFriend(String generatorId, UUID friendId) {
        Set<UUID> friends = generatorFriends.get(generatorId);
        if (friends != null) {
            friends.remove(friendId);
        }
    }
    
    public Set<UUID> getFriends(String generatorId) {
        return generatorFriends.getOrDefault(generatorId, new HashSet<>());
    }
    
    // === MÉTODOS DE ECONOMIA ===
    
    public boolean hasBalance(UUID playerId, double amount) {
        // Sistema simplificado - sempre retorna true por enquanto
        return true;
    }
    
    public boolean withdrawMoney(UUID playerId, double amount) {
        // Sistema simplificado - sempre retorna true por enquanto
        return true;
    }
    
    public boolean depositMoney(UUID playerId, double amount) {
        // Sistema simplificado - sempre retorna true por enquanto
        updatePlayerStats(playerId, 0, 0, amount, 0, 0);
        return true;
    }
    
    public String formatMoney(double amount) {
        return String.format("$%.2f", amount);
    }
    
    // === MÉTODOS DE LIMPEZA ===
    
    public void clearExpiredBoosters() {
        long currentTime = System.currentTimeMillis();
        activeBoosters.entrySet().removeIf(entry -> {
            Map<String, Object> booster = entry.getValue();
            long expiryTime = (Long) booster.get("expiryTime");
            return currentTime > expiryTime;
        });
    }
    
    public void closeConnection() {
        // Não há conexão para fechar em sistema de memória
        plugin.getLogger().info("Sistema de dados em memória finalizado!");
    }
}
