package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class TagRemoveCommand implements CommandExecutor {

    private final StoneTags plugin;

    public TagRemoveCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Verificar permissão de admin
        if (!sender.hasPermission("stonetags.admin.tagremove")) {
            plugin.getMessageUtils().sendConfigMessage(sender, "noPermission");
            return true;
        }

        // Verificar argumentos
        if (args.length < 3) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /tagremove <jogador> <tag_atual> <tag_nova>"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Exemplo: /tagremove BonecaAmbalabu staff vip"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Remove a tag_atual e define a tag_nova"));
            return true;
        }

        String playerName = args[0];
        String oldTagId = args[1];
        String newTagId = args[2];

        // Buscar jogador
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cJogador '" + playerName + "' não encontrado ou offline!"));
            return true;
        }

        // Verificar se as tags existem
        Tag oldTag = plugin.getTagManager().getTag(oldTagId);
        if (oldTag == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cTag atual '" + oldTagId + "' não encontrada!"));
            return true;
        }

        Tag newTag = plugin.getTagManager().getTag(newTagId);
        if (newTag == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cTag nova '" + newTagId + "' não encontrada!"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Tags disponíveis: " + 
                String.join(", ", plugin.getTagManager().getTagIds())));
            return true;
        }

        // Verificar se o jogador tem a tag atual
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(target);
        if (currentTag == null || !currentTag.getId().equals(oldTagId)) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cO jogador " + target.getName() + 
                " não está usando a tag '" + oldTagId + "'!"));
            if (currentTag != null) {
                sender.sendMessage(plugin.getMessageUtils().colorize("&7Tag atual: " + currentTag.getId()));
            } else {
                sender.sendMessage(plugin.getMessageUtils().colorize("&7O jogador não está usando nenhuma tag"));
            }
            return true;
        }

        // Remover tag atual e definir nova tag
        plugin.getPlayerDataManager().setSelectedTag(target, newTagId);

        // Mensagens de sucesso
        String oldTagDisplay = plugin.getMessageUtils().colorize(oldTag.getCurrentPrefix());
        String newTagDisplay = plugin.getMessageUtils().colorize(newTag.getCurrentPrefix());
        
        sender.sendMessage(plugin.getMessageUtils().colorize(
            "&aTag " + oldTagDisplay + "&a removida e " + newTagDisplay + "&a definida para &f" + target.getName() + "&a!"
        ));
        
        target.sendMessage(plugin.getMessageUtils().colorize(
            "&cSua tag " + oldTagDisplay + "&c foi removida e substituída por " + newTagDisplay
        ));

        // Log da ação
        plugin.getLogger().info("[ADMIN] " + sender.getName() + " removeu a tag '" + oldTagId + 
                               "' e definiu '" + newTagId + "' para o jogador " + target.getName());

        return true;
    }
}
