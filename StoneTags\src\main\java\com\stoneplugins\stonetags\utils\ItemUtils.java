package com.stoneplugins.stonetags.utils;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;

public class ItemUtils {
    
    /**
     * Cria um item com material e nome
     */
    public static ItemStack createItem(String materialData, String name) {
        Material material = getMaterialFromString(materialData);
        byte data = getDataFromString(materialData);
        
        ItemStack item = new ItemStack(material, 1, data);
        
        if (name != null && !name.isEmpty()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(name);
                item.setItemMeta(meta);
            }
        }
        
        return item;
    }
    
    /**
     * Cria um item com material, nome e lore
     */
    public static ItemStack createItem(String materialData, String name, String... lore) {
        ItemStack item = createItem(materialData, name);
        
        if (lore != null && lore.length > 0) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setLore(Arrays.asList(lore));
                item.setItemMeta(meta);
            }
        }
        
        return item;
    }
    
    /**
     * Obtém o material de uma string (suporta formato MATERIAL:DATA)
     */
    public static Material getMaterialFromString(String materialData) {
        if (materialData == null || materialData.isEmpty()) {
            return Material.STONE;
        }
        
        String materialName = materialData.split(":")[0].toUpperCase();
        
        try {
            return Material.valueOf(materialName);
        } catch (IllegalArgumentException e) {
            // Tentar alguns aliases comuns
            switch (materialName) {
                case "GLASS_PANE":
                    return Material.valueOf("THIN_GLASS");
                case "STAINED_GLASS_PANE":
                    return Material.valueOf("STAINED_GLASS_PANE");
                case "PLAYER_HEAD":
                case "SKULL":
                    return Material.valueOf("SKULL_ITEM");
                case "OAK_PLANKS":
                    return Material.valueOf("WOOD");
                case "COBBLESTONE_STAIRS":
                    return Material.valueOf("COBBLESTONE_STAIRS");
                default:
                    return Material.STONE;
            }
        }
    }
    
    /**
     * Obtém o data value de uma string (formato MATERIAL:DATA)
     */
    public static byte getDataFromString(String materialData) {
        if (materialData == null || !materialData.contains(":")) {
            return 0;
        }
        
        try {
            String[] parts = materialData.split(":");
            if (parts.length > 1) {
                return Byte.parseByte(parts[1]);
            }
        } catch (NumberFormatException e) {
            // Ignorar erro e retornar 0
        }
        
        return 0;
    }
    
    /**
     * Verifica se um item é válido (não é null e não é AIR)
     */
    public static boolean isValidItem(ItemStack item) {
        return item != null && item.getType() != Material.AIR;
    }
    
    /**
     * Verifica se dois items são similares (mesmo material e data)
     */
    public static boolean isSimilar(ItemStack item1, ItemStack item2) {
        if (!isValidItem(item1) || !isValidItem(item2)) {
            return false;
        }
        
        return item1.getType() == item2.getType() && 
               item1.getDurability() == item2.getDurability();
    }
    
    /**
     * Cria um vidro colorido (para 1.8)
     */
    public static ItemStack createGlassPane(int color, String name) {
        Material material;
        
        try {
            // Tentar usar o material da 1.13+
            material = Material.valueOf("GRAY_STAINED_GLASS_PANE");
        } catch (IllegalArgumentException e) {
            // Usar material da 1.8-1.12
            material = Material.valueOf("STAINED_GLASS_PANE");
        }
        
        ItemStack item = new ItemStack(material, 1, (short) color);
        
        if (name != null && !name.isEmpty()) {
            ItemMeta meta = item.getItemMeta();
            if (meta != null) {
                meta.setDisplayName(name);
                item.setItemMeta(meta);
            }
        }
        
        return item;
    }
    
    /**
     * Cria uma cabeça de jogador (skull)
     */
    public static ItemStack createPlayerHead(String playerName, String displayName) {
        Material skullMaterial;
        
        try {
            // Tentar usar material da 1.13+
            skullMaterial = Material.valueOf("PLAYER_HEAD");
        } catch (IllegalArgumentException e) {
            // Usar material da 1.8-1.12
            skullMaterial = Material.valueOf("SKULL_ITEM");
        }
        
        ItemStack skull = new ItemStack(skullMaterial, 1, (short) 3); // 3 = player skull
        
        ItemMeta meta = skull.getItemMeta();
        if (meta != null) {
            if (displayName != null && !displayName.isEmpty()) {
                meta.setDisplayName(displayName);
            }
            
            // Definir owner da skull (funciona apenas em versões antigas)
            try {
                if (meta.getClass().getSimpleName().equals("SkullMeta")) {
                    ((org.bukkit.inventory.meta.SkullMeta) meta).setOwner(playerName);
                }
            } catch (Exception e) {
                // Ignorar erro se não conseguir definir owner
            }
            
            skull.setItemMeta(meta);
        }
        
        return skull;
    }
    
    /**
     * Cria um item com textura customizada (base64)
     */
    public static ItemStack createCustomTextureHead(String texture, String displayName) {
        ItemStack skull = createPlayerHead("", displayName);
        
        // Aplicar textura customizada seria mais complexo e requereria reflection
        // Por simplicidade, retornar skull normal
        return skull;
    }
    
    /**
     * Obtém a quantidade máxima de stack de um material
     */
    public static int getMaxStackSize(Material material) {
        return material.getMaxStackSize();
    }
    
    /**
     * Verifica se um material é um bloco
     */
    public static boolean isBlock(Material material) {
        return material.isBlock();
    }
    
    /**
     * Verifica se um material é um item
     */
    public static boolean isItem(Material material) {
        return !material.isBlock();
    }
}
