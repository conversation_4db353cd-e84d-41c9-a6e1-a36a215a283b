C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\managers\PlayerDataManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\animation\TagAnimationManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\managers\TagManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\listeners\PlayerListener.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\data\PlayerData.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\gui\SuffixMenu.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\animation\RainbowGenerator.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\managers\ConfigManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\TagsCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\data\Tag.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\TagSetCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\placeholders\TagPlaceholders.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\StoneTags.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\utils\MessageUtils.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\SuffixCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\gui\TagsMenu.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\SetTagCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\data\Suffix.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\utils\ItemUtils.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\TagRemoveCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\managers\SuffixManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\StoneTagsCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\TagCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\listeners\MenuListener.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\database\DatabaseManager.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\commands\RemoveTagCommand.java
C:\Users\<USER>\Desktop\plugin minecraft\StoneTags\src\main\java\com\stoneplugins\stonetags\listeners\ChatListener.java
