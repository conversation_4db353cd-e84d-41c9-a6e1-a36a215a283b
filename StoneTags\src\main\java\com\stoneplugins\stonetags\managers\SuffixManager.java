package com.stoneplugins.stonetags.managers;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Suffix;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class SuffixManager {

    private final StoneTags plugin;
    private final Map<String, Suffix> suffixes;
    private File suffixesFile;
    private FileConfiguration suffixesConfig;

    public SuffixManager(StoneTags plugin) {
        this.plugin = plugin;
        this.suffixes = new HashMap<>();
        loadSuffixes();
    }

    public void loadSuffixes() {
        suffixes.clear();

        // Criar arquivo de sufixos se não existir
        createSuffixesFile();

        // Carregar sufixos do arquivo
        ConfigurationSection suffixesSection = suffixesConfig.getConfigurationSection("suffixes");
        if (suffixesSection == null) {
            plugin.getLogger().warning("Nenhum suffix encontrado no arquivo suffixes.yml!");
            return;
        }

        for (String suffixId : suffixesSection.getKeys(false)) {
            try {
                Suffix suffix = loadSuffixFromConfig(suffixId, suffixesSection.getConfigurationSection(suffixId));
                if (suffix != null) {
                    suffixes.put(suffixId, suffix);
                    plugin.getLogger()
                            .info("Suffix carregado: " + suffixId + " (prioridade: " + suffix.getPriority() + ")");
                }
            } catch (Exception e) {
                plugin.getLogger().severe("Erro ao carregar suffix " + suffixId + ": " + e.getMessage());
                e.printStackTrace();
            }
        }

        plugin.getLogger().info("Carregados " + suffixes.size() + " sufixos!");
    }

    private void createSuffixesFile() {
        suffixesFile = new File(plugin.getDataFolder(), "suffixes.yml");

        if (!suffixesFile.exists()) {
            plugin.saveResource("suffixes.yml", false);
        }

        suffixesConfig = YamlConfiguration.loadConfiguration(suffixesFile);
    }

    private Suffix loadSuffixFromConfig(String suffixId, ConfigurationSection section) {
        if (section == null)
            return null;

        String name = section.getString("name", suffixId);
        String displayName = section.getString("displayName", name);
        String chatSuffix = section.getString("chatSuffix", "");
        String tabSuffix = section.getString("tabSuffix", chatSuffix);
        String aboveName = section.getString("aboveName", "");
        boolean requiresPermission = section.getBoolean("requiresPermission", false);
        String permission = section.getString("permission", "stonetags.suffix." + suffixId);
        int priority = section.getInt("priority", 0);
        boolean isInvisible = section.getBoolean("invisible", false);
        boolean isAnimated = section.getBoolean("animated", false);

        String[] animationFrames = null;
        int animationSpeed = 1000;

        if (isAnimated) {
            List<String> framesList = section.getStringList("animationFrames");
            if (!framesList.isEmpty()) {
                animationFrames = framesList.toArray(new String[0]);
            }
            animationSpeed = section.getInt("animationSpeed", 1000);
        }

        return new Suffix(suffixId, name, displayName, chatSuffix, tabSuffix, aboveName,
                requiresPermission, permission, priority, isInvisible,
                isAnimated, animationFrames, animationSpeed);
    }

    public Suffix getSuffix(String suffixId) {
        return suffixes.get(suffixId.toLowerCase());
    }

    public Collection<Suffix> getAllSuffixes() {
        return suffixes.values();
    }

    public List<Suffix> getAvailableSuffixes(Player player) {
        return suffixes.values().stream()
                .filter(suffix -> canUseSuffix(player, suffix))
                .sorted((s1, s2) -> Integer.compare(s2.getPriority(), s1.getPriority()))
                .collect(Collectors.toList());
    }

    public boolean canUseSuffix(Player player, Suffix suffix) {
        if (!suffix.requiresPermission()) {
            return true;
        }

        return player.hasPermission(suffix.getPermission());
    }

    public Suffix getHighestPrioritySuffix(Player player) {
        plugin.getLogger().info("=== DEBUG: getHighestPrioritySuffix para " + player.getName() + " ===");

        List<Suffix> availableSuffixes = getAvailableSuffixes(player);

        plugin.getLogger().info("Sufixos disponíveis: " + availableSuffixes.size());
        for (Suffix suffix : availableSuffixes) {
            plugin.getLogger().info("- " + suffix.getId() + " (prioridade: " + suffix.getPriority() + ")");
        }

        if (availableSuffixes.isEmpty()) {
            plugin.getLogger().info("Nenhum suffix disponível");
            return null;
        }

        Suffix highest = availableSuffixes.get(0);
        plugin.getLogger().info("Suffix de maior prioridade: " + highest.getId());
        return highest;
    }

    public void reloadSuffixes() {
        loadSuffixes();
    }

    public void saveSuffixesConfig() {
        try {
            suffixesConfig.save(suffixesFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erro ao salvar suffixes.yml: " + e.getMessage());
        }
    }

    public boolean suffixExists(String suffixId) {
        return suffixes.containsKey(suffixId.toLowerCase());
    }

    public int getSuffixCount() {
        return suffixes.size();
    }

    public List<String> getSuffixIds() {
        return new ArrayList<>(suffixes.keySet());
    }

    public List<Suffix> getSuffixesByPriority() {
        return suffixes.values().stream()
                .sorted((s1, s2) -> Integer.compare(s2.getPriority(), s1.getPriority()))
                .collect(Collectors.toList());
    }
}
