package com.stoneplugins.stonecactos.database;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusFarm;
import org.bukkit.Bukkit;
import org.bukkit.Location;

import java.io.File;
import java.sql.*;
import java.util.*;

public class DatabaseManager {

    private final StoneCactos plugin;
    private Connection connection;

    public DatabaseManager(StoneCactos plugin) {
        this.plugin = plugin;
    }

    public void initialize() {
        String databaseType = plugin.getConfigManager().getDatabaseType();

        try {
            if (databaseType.equals("MYSQL")) {
                setupMySQL();
            } else {
                setupSQLite();
            }

            createTables();
            plugin.getLogger().info("Banco de dados inicializado com sucesso!");

        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao inicializar banco de dados: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void setupMySQL() throws SQLException {
        String host = plugin.getConfigManager().getConfig().getString("MySQL.host", "localhost");
        String user = plugin.getConfigManager().getConfig().getString("MySQL.user", "root");
        String password = plugin.getConfigManager().getConfig().getString("MySQL.password", "");
        String database = plugin.getConfigManager().getConfig().getString("MySQL.database", "stonecactos");

        String url = "jdbc:mysql://" + host + ":3306/" + database + "?useSSL=false&autoReconnect=true";

        connection = DriverManager.getConnection(url, user, password);
        plugin.getLogger().info("Conectado ao MySQL com sucesso!");
    }

    private void setupSQLite() throws SQLException {
        File dataFolder = plugin.getDataFolder();
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        String fileName = plugin.getConfigManager().getConfig().getString("SQLite.file", "database.db");
        String url = "jdbc:sqlite:" + dataFolder.getAbsolutePath() + "/" + fileName;

        connection = DriverManager.getConnection(url);
        plugin.getLogger().info("Conectado ao SQLite com sucesso!");
    }

    private void createTables() throws SQLException {
        // Tabela de fazendas
        String createFarmsTable = "CREATE TABLE IF NOT EXISTS farms (" +
                "id TEXT PRIMARY KEY," +
                "owner TEXT NOT NULL," +
                "world TEXT NOT NULL," +
                "x DOUBLE NOT NULL," +
                "y DOUBLE NOT NULL," +
                "z DOUBLE NOT NULL," +
                "towers INTEGER DEFAULT 0," +
                "capacity INTEGER DEFAULT 100," +
                "towers_in_development INTEGER DEFAULT 0," +
                "development_queue INTEGER DEFAULT 0," +
                "construction_speed INTEGER DEFAULT 40," +
                "quantity_per_construction INTEGER DEFAULT 2," +
                "stored_cactus INTEGER DEFAULT 0," +
                "battery INTEGER DEFAULT 100," +
                "energy_saving_mode BOOLEAN DEFAULT FALSE," +
                "last_construction BIGINT DEFAULT 0," +
                "friends TEXT DEFAULT ''," +
                "active_booster TEXT DEFAULT NULL," +
                "booster_expiry BIGINT DEFAULT 0," +
                "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP" +
                ")";

        // Tabela de boosters ativos
        String createBoostersTable = "CREATE TABLE IF NOT EXISTS active_boosters (" +
                "id INTEGER PRIMARY KEY AUTOINCREMENT," +
                "farm_id TEXT NOT NULL," +
                "booster_type TEXT NOT NULL," +
                "multiplier DOUBLE NOT NULL," +
                "start_time BIGINT NOT NULL," +
                "duration BIGINT NOT NULL," +
                "FOREIGN KEY (farm_id) REFERENCES farms(id) ON DELETE CASCADE" +
                ")";

        // Tabela de recompensas coletadas
        String createRewardsTable = """
                CREATE TABLE IF NOT EXISTS collected_rewards (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    player_uuid TEXT NOT NULL,
                    reward_id INTEGER NOT NULL,
                    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(player_uuid, reward_id)
                )
                """;

        // Tabela de estatísticas dos jogadores
        String createStatsTable = """
                CREATE TABLE IF NOT EXISTS player_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    player_uuid TEXT UNIQUE NOT NULL,
                    total_cactus_generated INTEGER DEFAULT 0,
                    total_cactus_sold INTEGER DEFAULT 0,
                    total_money_earned DOUBLE DEFAULT 0.0,
                    generators_placed INTEGER DEFAULT 0,
                    boosters_used INTEGER DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
                """;

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createFarmsTable);
            stmt.execute(createBoostersTable);
            stmt.execute(createRewardsTable);
            stmt.execute(createStatsTable);
        }
    }

    public void saveFarm(CactusFarm farm) {
        String sql = """
                    INSERT OR REPLACE INTO farms (
                        id, owner, world, x, y, z, towers, capacity, towers_in_development,
                        development_queue, construction_speed, quantity_per_construction,
                        stored_cactus, battery, energy_saving_mode, last_construction,
                        friends, active_booster, booster_expiry
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, farm.getId());
            stmt.setString(2, farm.getOwner().toString());
            stmt.setString(3, farm.getLocation().getWorld().getName());
            stmt.setDouble(4, farm.getLocation().getX());
            stmt.setDouble(5, farm.getLocation().getY());
            stmt.setDouble(6, farm.getLocation().getZ());
            stmt.setInt(7, farm.getTowers());
            stmt.setInt(8, farm.getCapacity());
            stmt.setInt(9, farm.getTowersInDevelopment());
            stmt.setInt(10, farm.getDevelopmentQueue());
            stmt.setInt(11, farm.getConstructionSpeed());
            stmt.setInt(12, farm.getQuantityPerConstruction());
            stmt.setInt(13, farm.getStoredCactus());
            stmt.setInt(14, farm.getBattery());
            stmt.setBoolean(15, farm.isEnergySavingMode());
            stmt.setLong(16, farm.getLastConstruction());
            stmt.setString(17, String.join(",", farm.getFriends().stream()
                    .map(UUID::toString).toArray(String[]::new)));
            stmt.setString(18, farm.getActiveBooster());
            stmt.setLong(19, farm.getBoosterExpiry());

            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao salvar fazenda: " + e.getMessage());
        }
    }

    public CactusFarm loadFarm(String id) {
        String sql = "SELECT * FROM farms WHERE id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, id);
            ResultSet rs = stmt.executeQuery();

            if (rs.next()) {
                UUID owner = UUID.fromString(rs.getString("owner"));
                String worldName = rs.getString("world");
                double x = rs.getDouble("x");
                double y = rs.getDouble("y");
                double z = rs.getDouble("z");

                Location location = new Location(Bukkit.getWorld(worldName), x, y, z);
                CactusFarm farm = new CactusFarm(id, owner, location);

                farm.setTowers(rs.getInt("towers"));
                farm.setCapacity(rs.getInt("capacity"));
                farm.setTowersInDevelopment(rs.getInt("towers_in_development"));
                farm.setDevelopmentQueue(rs.getInt("development_queue"));
                farm.setConstructionSpeed(rs.getInt("construction_speed"));
                farm.setQuantityPerConstruction(rs.getInt("quantity_per_construction"));
                farm.setStoredCactus(rs.getInt("stored_cactus"));
                farm.setBattery(rs.getInt("battery"));
                farm.setEnergySavingMode(rs.getBoolean("energy_saving_mode"));
                farm.setLastConstruction(rs.getLong("last_construction"));
                farm.setActiveBooster(rs.getString("active_booster"));
                farm.setBoosterExpiry(rs.getLong("booster_expiry"));

                // Carregar amigos
                String friendsStr = rs.getString("friends");
                if (friendsStr != null && !friendsStr.isEmpty()) {
                    String[] friendIds = friendsStr.split(",");
                    for (String friendId : friendIds) {
                        if (!friendId.trim().isEmpty()) {
                            farm.addFriend(UUID.fromString(friendId.trim()));
                        }
                    }
                }

                return farm;
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao carregar fazenda: " + e.getMessage());
        }

        return null;
    }

    public List<CactusFarm> loadAllFarms() {
        List<CactusFarm> farms = new ArrayList<>();
        String sql = "SELECT id FROM farms";

        try (Statement stmt = connection.createStatement();
                ResultSet rs = stmt.executeQuery(sql)) {

            while (rs.next()) {
                String id = rs.getString("id");
                CactusFarm farm = loadFarm(id);
                if (farm != null) {
                    farms.add(farm);
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao carregar fazendas: " + e.getMessage());
        }

        return farms;
    }

    public void deleteFarm(String id) {
        String sql = "DELETE FROM farms WHERE id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, id);
            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao deletar fazenda: " + e.getMessage());
        }
    }

    public List<CactusFarm> getFarmsByOwner(UUID owner) {
        List<CactusFarm> farms = new ArrayList<>();
        String sql = "SELECT id FROM farms WHERE owner = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, owner.toString());
            ResultSet rs = stmt.executeQuery();

            while (rs.next()) {
                String id = rs.getString("id");
                CactusFarm farm = loadFarm(id);
                if (farm != null) {
                    farms.add(farm);
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao buscar fazendas do jogador: " + e.getMessage());
        }

        return farms;
    }

    // Métodos para recompensas
    public boolean hasCollectedReward(UUID playerUuid, int rewardId) {
        String sql = "SELECT COUNT(*) FROM collected_rewards WHERE player_uuid = ? AND reward_id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, playerUuid.toString());
            stmt.setInt(2, rewardId);

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt(1) > 0;
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao verificar recompensa coletada: " + e.getMessage());
        }

        return false;
    }

    public void markRewardAsCollected(UUID playerUuid, int rewardId) {
        String sql = "INSERT INTO collected_rewards (player_uuid, reward_id) VALUES (?, ?)";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, playerUuid.toString());
            stmt.setInt(2, rewardId);
            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao marcar recompensa como coletada: " + e.getMessage());
        }
    }

    // Métodos para estatísticas
    public void updatePlayerStats(UUID playerUuid, int cactusGenerated, int cactusSold,
            double moneyEarned, int generatorsPlaced, int boostersUsed) {
        String sql = """
                INSERT OR REPLACE INTO player_stats
                (player_uuid, total_cactus_generated, total_cactus_sold, total_money_earned,
                generators_placed, boosters_used, updated_at)
                VALUES (?,
                COALESCE((SELECT total_cactus_generated FROM player_stats WHERE player_uuid = ?), 0) + ?,
                COALESCE((SELECT total_cactus_sold FROM player_stats WHERE player_uuid = ?), 0) + ?,
                COALESCE((SELECT total_money_earned FROM player_stats WHERE player_uuid = ?), 0) + ?,
                COALESCE((SELECT generators_placed FROM player_stats WHERE player_uuid = ?), 0) + ?,
                COALESCE((SELECT boosters_used FROM player_stats WHERE player_uuid = ?), 0) + ?,
                CURRENT_TIMESTAMP)
                """;

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, playerUuid.toString());
            stmt.setString(2, playerUuid.toString());
            stmt.setInt(3, cactusGenerated);
            stmt.setString(4, playerUuid.toString());
            stmt.setInt(5, cactusSold);
            stmt.setString(6, playerUuid.toString());
            stmt.setDouble(7, moneyEarned);
            stmt.setString(8, playerUuid.toString());
            stmt.setInt(9, generatorsPlaced);
            stmt.setString(10, playerUuid.toString());
            stmt.setInt(11, boostersUsed);

            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao atualizar estatísticas do jogador: " + e.getMessage());
        }
    }

    public int getTotalCactusGenerated(UUID playerUuid) {
        String sql = "SELECT total_cactus_generated FROM player_stats WHERE player_uuid = ?";

        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, playerUuid.toString());

            ResultSet rs = stmt.executeQuery();
            if (rs.next()) {
                return rs.getInt("total_cactus_generated");
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao buscar cactos gerados: " + e.getMessage());
        }

        return 0;
    }

    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                plugin.getLogger().info("Conexão com banco de dados fechada!");
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao fechar conexão: " + e.getMessage());
        }
    }

    public void close() {
        closeConnection();
    }
}
