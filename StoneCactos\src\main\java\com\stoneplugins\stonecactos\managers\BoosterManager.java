package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.ActiveBooster;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;

public class BoosterManager {

    private final StoneCactos plugin;

    public BoosterManager(StoneCactos plugin) {
        this.plugin = plugin;
    }

    /**
     * Ativa um booster em um gerador
     */
    public boolean activateBooster(Player player, CactusGenerator generator, String boosterId) {
        // Verificar se o booster existe
        if (!plugin.getConfigManager().hasBooster(boosterId)) {
            return false;
        }

        // Verificar se já tem um booster ativo
        if (generator.hasActiveBooster()) {
            player.sendMessage(plugin.getConfigManager().getMessage("boosterAlreadyActived"));
            return false;
        }

        // Obter informações do booster
        double multiplier = plugin.getConfigManager().getBoosterMultiplier(boosterId);
        int timeMinutes = plugin.getConfigManager().getBoosterTime(boosterId);
        long duration = timeMinutes * 60L * 1000L; // Converter para milissegundos

        // Criar booster ativo
        ActiveBooster activeBooster = new ActiveBooster(boosterId, multiplier, duration);
        generator.setActiveBooster(activeBooster);

        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);

        // Atualizar estatísticas do jogador
        // plugin.getDatabaseManager().updatePlayerStats(
        // player.getUniqueId(), 0, 0, 0.0, 0, 1);

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);

        // Enviar mensagem de sucesso
        player.sendMessage(plugin.getConfigManager().getMessage("boosterActived"));

        return true;
    }

    /**
     * Compra e ativa um booster
     */
    public boolean buyAndActivateBooster(Player player, CactusGenerator generator, String boosterId) {
        // Verificar se o booster existe
        if (!plugin.getConfigManager().hasBooster(boosterId)) {
            return false;
        }

        // Verificar se já tem um booster ativo
        if (generator.hasActiveBooster()) {
            player.sendMessage(plugin.getConfigManager().getMessage("boosterAlreadyActived"));
            return false;
        }

        // Tentar comprar o booster
        // if (!plugin.getEconomyManager().buyBooster(player, boosterId)) {
        // return false;
        // }

        // Ativar o booster
        return activateBooster(player, generator, boosterId);
    }

    /**
     * Remove um booster ativo de um gerador
     */
    public void removeBooster(CactusGenerator generator) {
        generator.setActiveBooster(null);

        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);
    }

    /**
     * Verifica e remove boosters expirados de todos os geradores
     */
    public void checkExpiredBoosters() {
        for (CactusGenerator generator : plugin.getGeneratorManager().getAllGenerators()) {
            if (generator.hasActiveBooster() && !generator.getActiveBooster().isActive()) {
                removeBooster(generator);
            }
        }
    }

    /**
     * Obtém todos os boosters disponíveis
     */
    public List<String> getAvailableBoosters() {
        List<String> boosters = new ArrayList<>();

        // Verificar boosters na configuração
        for (String key : plugin.getConfigManager().getConfig().getConfigurationSection("Boosters").getKeys(false)) {
            boosters.add(key);
        }

        return boosters;
    }

    /**
     * Obtém informações de um booster
     */
    public String getBoosterInfo(String boosterId) {
        if (!plugin.getConfigManager().hasBooster(boosterId)) {
            return "§cBooster não encontrado";
        }

        String name = plugin.getConfigManager().getBoosterName(boosterId);
        double multiplier = plugin.getConfigManager().getBoosterMultiplier(boosterId);
        int time = plugin.getConfigManager().getBoosterTime(boosterId);
        double price = plugin.getConfigManager().getBoosterPrice(boosterId);
        String economy = plugin.getConfigManager().getBoosterEconomy(boosterId);

        return String.format("§e%s\n§fMultiplicador: §a%.1fx\n§fDuração: §b%dm\n§fPreço: §6%s\n§fEconomia: §7%s",
                name, multiplier, time, String.format("$%.2f", price), economy); // plugin.getEconomyManager().formatMoney(price)
    }

    /**
     * Obtém o tempo formatado de um booster
     */
    public String getFormattedTime(int minutes) {
        if (minutes >= 60) {
            int hours = minutes / 60;
            int remainingMinutes = minutes % 60;
            if (remainingMinutes > 0) {
                return hours + "h " + remainingMinutes + "m";
            } else {
                return hours + "h";
            }
        } else {
            return minutes + "m";
        }
    }

    /**
     * Verifica se um jogador pode usar um booster
     */
    public boolean canUseBooster(Player player, String boosterId) {
        if (!plugin.getConfigManager().hasBooster(boosterId)) {
            return false;
        }

        double price = plugin.getConfigManager().getBoosterPrice(boosterId);
        // return plugin.getEconomyManager().hasBalance(player, price);
        return true; // Temporário: sempre permitir compra
    }

    /**
     * Obtém estatísticas dos boosters
     */
    public String getBoosterStatistics() {
        int totalActiveBoosters = 0;
        int totalGenerators = plugin.getGeneratorManager().getAllGenerators().size();

        for (CactusGenerator generator : plugin.getGeneratorManager().getAllGenerators()) {
            if (generator.hasActiveBooster()) {
                totalActiveBoosters++;
            }
        }

        return String.format("§eBoosters Ativos: §f%d/%d", totalActiveBoosters, totalGenerators);
    }
}
