package com.atlasplugins.pescaria.managers;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.models.Mission;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class MissionManager {

    private final Pescaria plugin;
    private final List<Mission> missions;

    public MissionManager(Pescaria plugin) {
        this.plugin = plugin;
        this.missions = new ArrayList<>();
        loadMissions();
    }

    private void loadMissions() {
        ConfigurationSection missionsSection = plugin.getConfigManager().getMissionsConfig().getConfigurationSection("Missoes");
        if (missionsSection == null) {
            plugin.getLogger().warning("Nenhuma missão encontrada no arquivo missoes.yml!");
            return;
        }

        for (String key : missionsSection.getKeys(false)) {
            ConfigurationSection missionSection = missionsSection.getConfigurationSection(key);
            if (missionSection == null) continue;

            int ordem = missionSection.getInt("Ordem");
            String name = missionSection.getString("Name", "Missão");
            int peixesToComplete = missionSection.getInt("PeixesToComplete", 10);
            boolean recompensasAtivar = missionSection.getBoolean("RecompensasAtivar", true);

            Map<String, String> recompensas = new HashMap<>();
            ConfigurationSection recompensasSection = missionSection.getConfigurationSection("Recompensas");
            if (recompensasSection != null) {
                for (String recompensaKey : recompensasSection.getKeys(false)) {
                    ConfigurationSection recompensaSection = recompensasSection.getConfigurationSection(recompensaKey);
                    if (recompensaSection == null) continue;

                    String nome = recompensaSection.getString("Nome", "Recompensa");
                    String command = recompensaSection.getString("Command", "");
                    recompensas.put(nome, command);
                }
            }

            Mission mission = new Mission(key, ordem, name, peixesToComplete, recompensasAtivar, recompensas);
            missions.add(mission);
        }

        // Ordenar missões por ordem
        missions.sort(Comparator.comparingInt(Mission::getOrdem));
    }

    public Mission getMissionByOrder(int ordem) {
        for (Mission mission : missions) {
            if (mission.getOrdem() == ordem) {
                return mission;
            }
        }
        return null;
    }

    public Mission getNextMission(int currentOrder) {
        for (Mission mission : missions) {
            if (mission.getOrdem() > currentOrder) {
                return mission;
            }
        }
        return null; // Não há próxima missão
    }

    public void completeMission(Player player, Mission mission) {
        if (mission == null || !mission.isRecompensasAtivar()) {
            return;
        }

        // Dar recompensas ao jogador
        for (Map.Entry<String, String> entry : mission.getRecompensas().entrySet()) {
            String command = entry.getValue().replace("@player", player.getName());
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        }

        // Atualizar missão do jogador
        PlayerManager playerManager = plugin.getPlayerManager();
        playerManager.setPlayerMission(player.getUniqueId(), mission.getOrdem() + 1);
        playerManager.savePlayer(player.getUniqueId());

        // Atualizar scoreboard
        plugin.getScoreboardManager().updateScoreboard(player);

        // Mensagem de conclusão
        player.sendMessage("§a§lPESCARIA §fVocê completou a missão §e" + mission.getName() + "§f!");
        player.sendMessage("§a§lPESCARIA §fRecompensas:");
        for (String recompensaNome : mission.getRecompensas().keySet()) {
            player.sendMessage("§a§lPESCARIA §f- §e" + recompensaNome);
        }
    }

    public List<Mission> getMissions() {
        return missions;
    }

    public int getTotalMissions() {
        return missions.size();
    }
}