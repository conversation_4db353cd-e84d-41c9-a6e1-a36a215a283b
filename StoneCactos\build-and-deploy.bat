@echo off
setlocal enabledelayedexpansion

echo ========================================
echo    StoneCactos - Build e Deploy
echo ========================================
echo.

REM Verificar se o servidor está rodando
echo [1/4] Verificando status do servidor...
tasklist /FI "IMAGENAME eq java.exe" 2>NUL | find /I /N "java.exe">NUL
set "SERVER_RUNNING=0"
if "%ERRORLEVEL%"=="0" (
    set "SERVER_RUNNING=1"
    echo AVISO: Servidor Minecraft detectado em execucao!
    echo.
    echo Opcoes:
    echo 1. Continuar (plugin sera compilado, mas nao atualizado)
    echo 2. Parar servidor automaticamente e atualizar
    echo 3. Cancelar
    echo.
    choice /C 123 /M "Escolha uma opcao"
    
    if errorlevel 3 (
        echo Operacao cancelada.
        pause
        exit /b 1
    )
    
    if errorlevel 2 (
        echo Tentando parar o servidor...
        powershell -Command "Get-Process | Where-Object {$_.ProcessName -like '*java*'} | Stop-Process -Force" 2>nul
        timeout /t 3 /nobreak >nul
        set "SERVER_RUNNING=0"
        echo Servidor parado.
    )
) else (
    echo Servidor nao esta rodando.
)

echo.
echo [2/4] Compilando plugin...

REM Tentar compilacao com Maven primeiro
where mvn >nul 2>&1
if %errorlevel% equ 0 (
    echo Usando Maven...
    mvn clean package -q
    if %errorlevel% equ 0 (
        echo Compilacao Maven concluida!
        goto :deploy
    )
)

REM Fallback para compilacao manual
echo Usando compilacao manual...
call compile_fixed.bat
if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao!
    pause
    exit /b 1
)

:deploy
echo.
echo [3/4] Preparando deploy...

set "TARGET_JAR=target\stonecactos-1.0.jar"
set "PLUGINS_DIR=..\servidor\plugins"
set "SERVER_JAR=%PLUGINS_DIR%\stonecactos-1.0.jar"
set "NEW_JAR=%PLUGINS_DIR%\stonecactos-1.0-new.jar"

if not exist "%TARGET_JAR%" (
    echo ERRO: Arquivo JAR nao foi gerado!
    pause
    exit /b 1
)

REM Fazer backup se plugin existir
if exist "%SERVER_JAR%" (
    set "BACKUP_NAME=stonecactos-1.0-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.jar"
    set "BACKUP_NAME=!BACKUP_NAME: =0!"
    copy "%SERVER_JAR%" "%PLUGINS_DIR%\!BACKUP_NAME!" >nul
    echo Backup criado: !BACKUP_NAME!
)

if "%SERVER_RUNNING%"=="0" (
    REM Servidor parado - copiar diretamente
    echo Copiando plugin diretamente...
    copy "%TARGET_JAR%" "%SERVER_JAR%" >nul
    if exist "%SERVER_JAR%" (
        echo Plugin atualizado com sucesso!
        set "DEPLOY_SUCCESS=1"
    ) else (
        echo Falha ao copiar plugin.
        set "DEPLOY_SUCCESS=0"
    )
) else (
    REM Servidor rodando - copiar com nome temporario
    echo Copiando plugin como arquivo temporario...
    copy "%TARGET_JAR%" "%NEW_JAR%" >nul
    if exist "%NEW_JAR%" (
        echo Plugin copiado como stonecactos-1.0-new.jar
        echo INSTRUCOES: Pare o servidor e execute update-server-plugin.bat
        set "DEPLOY_SUCCESS=0"
    ) else (
        echo Falha ao copiar plugin.
        set "DEPLOY_SUCCESS=0"
    )
)

echo.
echo [4/4] Finalizando...

if "%DEPLOY_SUCCESS%"=="1" (
    echo ========================================
    echo    BUILD E DEPLOY CONCLUIDOS!
    echo ========================================
    echo Plugin StoneCactos atualizado com sucesso.
    echo.
    choice /C SN /M "Deseja iniciar o servidor agora (S/N)"
    if errorlevel 1 if not errorlevel 2 (
        echo Iniciando servidor...
        cd /d "..\servidor"
        if exist "start_server.bat" (
            start "" "start_server.bat"
            echo Servidor iniciado!
        ) else if exist "start.bat" (
            start "" "start.bat"
            echo Servidor iniciado!
        ) else (
            echo Script de inicio nao encontrado. Inicie manualmente.
        )
    )
) else (
    echo ========================================
    echo    BUILD CONCLUIDO - DEPLOY PENDENTE
    echo ========================================
    echo Plugin compilado com sucesso.
    if exist "%NEW_JAR%" (
        echo Para completar a atualizacao:
        echo 1. Pare o servidor
        echo 2. Execute: update-server-plugin.bat
        echo 3. Inicie o servidor
    ) else (
        echo Plugin disponivel em: %TARGET_JAR%
        echo Copie manualmente para: %SERVER_JAR%
    )
)

echo.
echo Scripts disponiveis:
echo - build-and-deploy.bat     : Este script (build + deploy)
echo - compile-and-deploy.ps1   : Script PowerShell avancado
echo - update-server-plugin.bat : Atualizar plugin no servidor
echo - compile_fixed.bat        : Compilacao manual apenas

pause
