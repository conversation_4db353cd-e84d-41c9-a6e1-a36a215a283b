# ========================================
#           STONE TAGS - CONFIG
# ========================================

# Configurações do banco de dados
Database:
  type: SQLITE # SQLITE ou MYSQL
  mysql:
    host: "localhost"
    port: 3306
    database: "stonetags"
    username: "root"
    password: ""
  sqlite:
    file: "stonetags.db"

# Configurações gerais
General:
  # Usar menu ou chat para selecionar tags
  useMenu: true
  
  # Distância máxima para ver tags AboveName
  aboveNameDistance: 10
  
  # Atualizar tags automaticamente quando permissões mudarem
  autoUpdateOnPermissionChange: false
  
  # Usar sistema de animação (requer mais recursos)
  enableAnimations: true
  
  # Intervalo de animação em ticks (20 ticks = 1 segundo)
  animationInterval: 20

# Configurações do menu
Menu:
  title: "&8Tags Disponíveis"
  size: 54
  
  # Item de decoração
  decoration:
    material: "STAINED_GLASS_PANE:7"
    name: " "
  
  # Slots onde as tags aparecerão
  tagSlots: "10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34"
  
  # Item de voltar página
  previousPage:
    slot: 45
    material: "ARROW"
    name: "&aPágina Anterior"
    
  # Item de próxima página  
  nextPage:
    slot: 53
    material: "ARROW"
    name: "&aPróxima Página"
    
  # Item de remover tag
  removeTag:
    slot: 49
    material: "BARRIER"
    name: "&cRemover Tag"
    lore:
      - "&7Clique para remover sua tag atual"

# Configurações de integração
Integrations:
  # TAB Plugin
  tab:
    enabled: true
    updateTabOnTagChange: true
    
  # PermissionsEx/LuckPerms
  permissions:
    enabled: true
    updateTagOnRankChange: true
    
  # PlaceholderAPI
  placeholderapi:
    enabled: true

# Configurações de chat
Chat:
  enabled: true
  format: "%tag_prefix%%player%%suffix_display%&f: %message%"

# Mensagens do plugin
Messages:
  prefix: "&8[&6Stone&eTags&8] "
  
  # Comandos
  noPermission: "&cVocê não tem permissão para usar este comando!"
  playerOnly: "&cApenas jogadores podem usar este comando!"
  playerNotFound: "&cJogador não encontrado!"
  
  # Tags
  tagSelected: "&aTag &f{tag} &aselecionada com sucesso!"
  tagRemoved: "&cTag removida com sucesso!"
  tagNotFound: "&cTag não encontrada!"
  tagNoPermission: "&cVocê não tem permissão para usar esta tag!"
  tagAlreadySelected: "&eVocê já está usando esta tag!"
  
  # Admin
  configReloaded: "&aConfiguração recarregada com sucesso!"
  tagGiven: "&aTag &f{tag} &adada para &f{player}&a!"
  tagTaken: "&cTag &f{tag} &cremovida de &f{player}&c!"
  
  # Menu
  menuTitle: "&8Selecionar Tag"
  noTagsAvailable: "&cNenhuma tag disponível!"
  
  # Hover
  clickToSelect: "&eClique para selecionar"
  clickToRemove: "&cClique para remover"

# ========================================
#              TAGS CONFIG
# ========================================

Tags:
  # Tag VIP
  vip:
    name: "&6VIP"
    prefix: "&8[&6VIP&8] "
    suffix: ""
    color: "&6"
    icon: "DIAMOND"
    permission: "stonetags.vip"
    position: 100
    description:
      - "&6Tag VIP"
      - "&7Para jogadores VIP"
    preview: "&8[&6VIP&8] &6{player}"
    tabPrefix: "&6[VIP] "
    tabSuffix: ""
    aboveName: "&6✦ VIP ✦"
    invisible: false
    animated: false

  # Tag MVP
  mvp:
    name: "&bMVP"
    prefix: "&8[&bMVP&8] "
    suffix: ""
    color: "&b"
    icon: "EMERALD"
    permission: "stonetags.mvp"
    position: 200
    description:
      - "&bTag MVP"
      - "&7Para jogadores MVP"
    preview: "&8[&bMVP&8] &b{player}"
    tabPrefix: "&b[MVP] "
    tabSuffix: ""
    aboveName: "&b★ MVP ★"
    invisible: false
    animated: false

  # Tag Staff
  staff:
    name: "&cSTAFF"
    prefix: "&8[&cSTAFF&8] "
    suffix: ""
    color: "&c"
    icon: "REDSTONE"
    permission: "stonetags.staff"
    position: 999
    description:
      - "&cTag Staff"
      - "&7Para membros da equipe"
    preview: "&8[&cSTAFF&8] &c{player}"
    tabPrefix: "&c[STAFF] "
    tabSuffix: ""
    aboveName: "&c⚡ STAFF ⚡"
    invisible: false
    animated: true
    animationFrames:
      - "&8[&cSTAFF&8] "
      - "&8[&4STAFF&8] "
      - "&8[&cSTAFF&8] "
      - "&8[&6STAFF&8] "

  # Tag Invisível
  invisible:
    name: "&7Invisível"
    prefix: ""
    suffix: ""
    color: "&7"
    icon: "GLASS"
    permission: "stonetags.invisible"
    position: 1
    description:
      - "&7Tag Invisível"
      - "&7Oculta seu nome"
    preview: "&7[Invisível]"
    tabPrefix: ""
    tabSuffix: ""
    aboveName: ""
    invisible: true
    animated: false

  # Tag Rainbow (Animada)
  rainbow:
    name: "&cRainbow"
    prefix: "&8[&cRAINBOW&8] "
    suffix: ""
    color: "&c"
    icon: "NETHER_STAR"
    permission: "stonetags.rainbow"
    position: 800
    description:
      - "&cTag Rainbow"
      - "&7Tag animada colorida"
    preview: "&8[&cRAINBOW&8] &c{player}"
    tabPrefix: "&c[RAINBOW] "
    tabSuffix: ""
    aboveName: "&c✦ RAINBOW ✦"
    invisible: false
    animated: true
    animationFrames:
      - "&8[&cR&6A&eI&aN&bB&9O&dW&8] "
      - "&8[&6R&eA&aI&bN&9B&dO&cW&8] "
      - "&8[&eR&aA&bI&9N&dB&cO&6W&8] "
      - "&8[&aR&bA&9I&dN&cB&6O&eW&8] "
      - "&8[&bR&9A&dI&cN&6B&eO&aW&8] "
      - "&8[&9R&dA&cI&6N&eB&aO&bW&8] "
    animationSpeed: 500

  # Tag VIP+ (Animada)
  vipplus:
    name: "&aVIP+"
    prefix: "&8[&aVIP+&8] "
    suffix: ""
    color: "&a"
    icon: "EMERALD_BLOCK"
    permission: "stonetags.vipplus"
    position: 350
    description:
      - "&aTag VIP+"
      - "&7VIP com animação"
    preview: "&8[&aVIP+&8] &a{player}"
    tabPrefix: "&a[VIP+] "
    tabSuffix: ""
    aboveName: "&a✦ VIP+ ✦"
    invisible: false
    animated: true
    animationFrames:
      - "&8[&aVIP&2+&8] "
      - "&8[&2VIP&a+&8] "
    animationSpeed: 1000

  # Tag Premium (Animada)
  premium:
    name: "&6Premium"
    prefix: "&8[&6PREMIUM&8] "
    suffix: ""
    color: "&6"
    icon: "GOLD_BLOCK"
    permission: "stonetags.premium"
    position: 600
    description:
      - "&6Tag Premium"
      - "&7Tag premium animada"
    preview: "&8[&6PREMIUM&8] &6{player}"
    tabPrefix: "&6[PREMIUM] "
    tabSuffix: ""
    aboveName: "&6✦ PREMIUM ✦"
    invisible: false
    animated: true
    animationFrames:
      - "&8[&6PREMIUM&8] "
      - "&8[&ePREMIUM&8] "
      - "&8[&6PREMIUM&8] "
      - "&8[&ePREMIUM&8] "
    animationSpeed: 800
