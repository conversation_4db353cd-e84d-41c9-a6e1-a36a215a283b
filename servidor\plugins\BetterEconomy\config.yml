handler-type: file
currency:
  symbol: $
  thousands-separator: ','
  singular: $
  format-fractional-digits: 2
  use-thousands-separator: true
  plural: $
  decimal-point: .
balance:
  top-update-period: 100
  file-save-period: 200
  min-amount: 0.0
  start-amount: 0.0
database:
  mysql:
    username: root
    password: ''
    dbname: ''
    port: '3306'
    host: localhost
  common:
    client-settings: {}
    driver-settings: {}
  sqlite:
    dbname: balances
