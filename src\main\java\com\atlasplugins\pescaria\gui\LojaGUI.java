package com.atlasplugins.pescaria.gui;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.UUID;

public class LojaGUI {

    private final Pescaria plugin;

    public LojaGUI(Pescaria plugin) {
        this.plugin = plugin;
    }

    public void openLojaMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§a§lLoja de Pesca");

        UUID uuid = player.getUniqueId();
        int playerFishes = plugin.getPlayerManager().getPlayerFishes(uuid);

        // Item de informações do jogador (slot 4)
        ItemStack infoItem = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        org.bukkit.inventory.meta.SkullMeta skullMeta = (org.bukkit.inventory.meta.SkullMeta) infoItem.getItemMeta();
        skullMeta.setOwner(player.getName());
        skullMeta.setDisplayName("§e§lMeu perfil §7(§e#" + (Math.abs(uuid.hashCode()) % 10000) + "§7)");
        skullMeta.setLore(Arrays.asList(
                "",
                "§f▸ §7Meus peixes: §e" + playerFishes + "K",
                "",
                "§7Venda seus peixes por coins ou cash",
                "§7usando os botões abaixo."));
        infoItem.setItemMeta(skullMeta);
        gui.setItem(4, infoItem);

        // Item para vender por coins (slot 11)
        ItemStack coinsItem = new ItemStack(Material.IRON_INGOT);
        ItemMeta coinsMeta = coinsItem.getItemMeta();
        coinsMeta.setDisplayName("§a§lCoins §7(§a#" + (Math.abs(uuid.hashCode()) % 10000) + "§7)");
        
        double pricePerFishCoins = plugin.getConfigManager().getFishPriceCoins();
        int totalValueCoins = (int) (playerFishes * pricePerFishCoins);
        int value64Coins = (int) (Math.min(64, playerFishes) * pricePerFishCoins);
        
        coinsMeta.setLore(Arrays.asList(
                "§7Venda seus peixes por coins.",
                "",
                "§f▸ §7Clique com direito para vender tudo",
                "§f▸ §7Clique com esquerdo para vender x64",
                "",
                "§f▸ §7Seus peixes: §e" + playerFishes + "K",
                "§f▸ §7Valor de venda: §a" + totalValueCoins + " coins",
                "§f▸ §7Valor de venda x64: §a" + value64Coins + " coins"));
        coinsItem.setItemMeta(coinsMeta);
        gui.setItem(11, coinsItem);

        // Item para vender por cash (slot 15)
        ItemStack cashItem = new ItemStack(Material.GOLD_INGOT);
        ItemMeta cashMeta = cashItem.getItemMeta();
        cashMeta.setDisplayName("§6§lCash §7(§6#" + (Math.abs(uuid.hashCode()) % 10000) + "§7)");
        
        double pricePerFishCash = plugin.getConfigManager().getFishPriceCash();
        int totalValueCash = (int) (playerFishes * pricePerFishCash);
        int value64Cash = (int) (Math.min(64, playerFishes) * pricePerFishCash);
        
        cashMeta.setLore(Arrays.asList(
                "§7Venda seus peixes por cash",
                "",
                "§f▸ §7Clique com direito para vender tudo",
                "§f▸ §7Clique com esquerdo para vender x64",
                "",
                "§f▸ §7Seus peixes: §e" + playerFishes + "K",
                "§f▸ §7Valor de venda: §6" + totalValueCash + " de cash",
                "§f▸ §7Valor de venda x64: §6" + value64Cash + " de cash"));
        cashItem.setItemMeta(cashMeta);
        gui.setItem(15, cashItem);

        // Item especial - Diamante (slot 13)
        ItemStack diamondItem = new ItemStack(Material.DIAMOND);
        ItemMeta diamondMeta = diamondItem.getItemMeta();
        diamondMeta.setDisplayName("§b§lDiamante §7(§b#" + (Math.abs(uuid.hashCode()) % 10000) + "§7)");
        diamondMeta.setLore(Arrays.asList(
                "§7Diamante muito bonito",
                "",
                "§f▸ §7Preço: §b18K",
                "",
                "§7Um diamante raro e valioso",
                "§7para sua coleção."));
        diamondItem.setItemMeta(diamondMeta);
        gui.setItem(13, diamondItem);

        // Item para voltar (slot 22)
        ItemStack backItem = new ItemStack(Material.ARROW);
        ItemMeta backMeta = backItem.getItemMeta();
        backMeta.setDisplayName("§cVoltar");
        backMeta.setLore(Arrays.asList("§7Clique para voltar ao menu principal"));
        backItem.setItemMeta(backMeta);
        gui.setItem(22, backItem);

        player.openInventory(gui);
    }
}
