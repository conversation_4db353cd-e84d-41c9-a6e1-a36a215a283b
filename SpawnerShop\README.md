# SpawnerShop Plugin

Plugin profissional de loja de spawners para Minecraft com sistema completo de compras, limites, descontos e ranking.

## Funcionalidades

### 🛒 Sistema de Compras
- Compra de spawners através de GUI intuitiva
- Compra via comandos de chat
- Sistema de confirmação de compras
- Suporte a múltiplos plugins de economia

### 📊 Sistema de Limites
- Limite de compras configurável por jogador
- Itens especiais para aumentar limite
- Sistema de bypass por permissões
- Controle total sobre quantidades

### 💰 Sistema de Descontos
- Descontos baseados em permissões
- Múltiplos grupos de desconto
- Cálculo automático de preços
- Exibição clara dos descontos ativos

### 🏆 Sistema de Ranking
- Top jogadores com mais compras
- Interface gráfica do ranking
- Histórico completo de compras
- Estatísticas detalhadas

### 🔒 Sistema de Liberação
- Spawners liberados por data/hora
- Requisitos de rank (StormRankUP)
- Requisitos de permissão
- Sistema de bypass para admins

### 🎨 Interface Gráfica
- Menus totalmente customizáveis
- Suporte a texturas de cabeças
- Navegação por páginas
- Itens com glow e efeitos

### 📝 Placeholders
- `%stormspawnershop_limite%` - Limite de compras do jogador
- `%stormspawnershop_compras%` - Quantidade de compras realizadas
- `%stormspawnershop_restante%` - Compras restantes
- `%stormspawnershop_desconto%` - Desconto ativo do jogador
- `%stormspawnershop_grupos%` - Grupos de desconto do jogador
- `%stormspawnershop_saldo%` - Saldo formatado do jogador

## Dependências

### Obrigatórias
- **Vault** - Sistema de economia e permissões

### Opcionais
- **PlaceholderAPI** - Para usar placeholders
- **LuckPerms** ou **PermissionsEx** - Sistema de permissões
- **yPoints** - Sistema de pontos alternativo
- **PlayerPoints** - Sistema de pontos alternativo
- **AtlasEconomiaSecundaria** - Economia secundária
- **StormEconomiaSecundaria** - Economia secundária
- **StormRankUP** - Sistema de ranks

## Instalação

1. Baixe o arquivo `.jar` do plugin
2. Coloque na pasta `plugins` do seu servidor
3. Instale as dependências necessárias
4. Reinicie o servidor
5. Configure o arquivo `config.yml`
6. Execute `/spawnershop reload`

## Comandos

### Jogadores
- `/spawners` - Abrir loja de spawners
- `/spawners top` - Ver ranking de compradores
- `/spawners info` - Ver suas informações
- `/spawnercomprar <spawner> <quantidade>` - Comprar spawner via chat

### Administradores
- `/spawners reload` - Recarregar plugin
- `/spawners limite <quantidade>` - Aumentar limite de compras
- `/spawnerlimite <jogador> <quantidade>` - Dar item de limite

## Permissões

### Básicas
- `spawnershop.use` - Usar o plugin (padrão: true)
- `spawnershop.top` - Ver ranking (padrão: true)

### Administrativas
- `spawnershop.admin` - Acesso total (padrão: op)
- `spawnershop.reload` - Recarregar plugin (padrão: op)

### Bypass
- `spawnershop.bypass.limit` - Ignorar limites de compra
- `spawnershop.bypass.rank` - Ignorar requisitos de rank
- `spawnershop.bypass.permission` - Ignorar requisitos de permissão
- `spawnershop.bypass.date` - Ignorar requisitos de data

## Configuração

### Banco de Dados
O plugin suporta SQLite e MySQL. Configure na seção `General.sqlType`.

### Economia
Suporte para múltiplos plugins de economia:
- VAULT (padrão)
- YPOINTS
- PLAYERPOINTS
- ATLASECONOMIASECUNDARIA
- STORMECONOMIASECUNDARIA

### Spawners
Configure spawners na seção `Spawners`:

```yaml
Spawners:
  cow:
    title: "&eVaca"
    price: 30000
    skull: "5d6c6eda942f7f5f71c3161c7306f4aed307d82895f9d2b07ab4525718edc5"
    command: "spawneradmin give {player} cow {quantidade}"
    permission: "" # Opcional
    liberaAs: "" # Data de liberação (dd/MM/yyyy HH:mm)
    rankNecessario: "" # Rank necessário (StormRankUP)
```

### Descontos
Configure descontos por permissão:

```yaml
Descontos:
  - 'vip.gold:10.0:&6Ouro'
  - 'vip.diamond:20.0:&bDiamante'
```

### Mensagens
90% das mensagens são configuráveis na seção `Mensagens`.

### Menus
Interface totalmente customizável na seção `Menus`.

## Suporte

Para suporte, dúvidas ou sugestões:
- Discord: [Seu Discord]
- Email: [Seu Email]

## Licença

Este plugin é proprietário. Todos os direitos reservados.

---

**SpawnerShop v1.0** - Plugin desenvolvido por StonePlugins
