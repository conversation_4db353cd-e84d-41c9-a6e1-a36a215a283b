package com.stoneplugins.spawnershop.data;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class Spawner {

    private String id;
    private String title;
    private double price;
    private String skull;
    private String command;
    private String permission;
    private String releaseDate;
    private String requiredRank;
    private int order;

    public Spawner(String id, String title, double price, String skull, String command, String permission,
            String releaseDate, String requiredRank, int order) {
        this.id = id;
        this.title = title;
        this.price = price;
        this.skull = skull;
        this.command = command;
        this.permission = permission;
        this.releaseDate = releaseDate;
        this.requiredRank = requiredRank;
        this.order = order;
    }

    // Getters
    public String getId() {
        return id;
    }

    public String getTitle() {
        return title;
    }

    public double getPrice() {
        return price;
    }

    public String getSkull() {
        return skull;
    }

    public String getCommand() {
        return command;
    }

    public String getPermission() {
        return permission;
    }

    public String getReleaseDate() {
        return releaseDate;
    }

    public String getRequiredRank() {
        return requiredRank;
    }

    public int getOrder() {
        return order;
    }

    // Setters
    public void setId(String id) {
        this.id = id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public void setPrice(double price) {
        this.price = price;
    }

    public void setSkull(String skull) {
        this.skull = skull;
    }

    public void setCommand(String command) {
        this.command = command;
    }

    public void setPermission(String permission) {
        this.permission = permission;
    }

    public void setReleaseDate(String releaseDate) {
        this.releaseDate = releaseDate;
    }

    public void setRequiredRank(String requiredRank) {
        this.requiredRank = requiredRank;
    }

    // Utility methods
    public boolean hasPermissionRequirement() {
        return permission != null && !permission.isEmpty();
    }

    public boolean hasReleaseDate() {
        return releaseDate != null && !releaseDate.isEmpty();
    }

    public boolean hasRequiredRank() {
        return requiredRank != null && !requiredRank.isEmpty();
    }

    public boolean isReleased() {
        if (!hasReleaseDate()) {
            return true;
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            Date releaseDateTime = sdf.parse(releaseDate);
            Date now = new Date();
            return now.after(releaseDateTime);
        } catch (ParseException e) {
            return true; // Se não conseguir parsear, considera como liberado
        }
    }

    public String getTimeUntilRelease() {
        if (!hasReleaseDate() || isReleased()) {
            return "";
        }

        try {
            SimpleDateFormat sdf = new SimpleDateFormat("dd/MM/yyyy HH:mm");
            Date releaseDateTime = sdf.parse(releaseDate);
            Date now = new Date();

            long diff = releaseDateTime.getTime() - now.getTime();

            long days = diff / (24 * 60 * 60 * 1000);
            diff %= (24 * 60 * 60 * 1000);
            long hours = diff / (60 * 60 * 1000);
            diff %= (60 * 60 * 1000);
            long minutes = diff / (60 * 1000);
            diff %= (60 * 1000);
            long seconds = diff / 1000;

            StringBuilder timeString = new StringBuilder();
            if (days > 0) {
                timeString.append(days).append(" dias ");
            }
            if (hours > 0) {
                timeString.append(hours).append(" horas ");
            }
            if (minutes > 0) {
                timeString.append(minutes).append(" minutos ");
            }
            if (seconds > 0) {
                timeString.append(seconds).append(" segundos");
            }

            return timeString.toString().trim();
        } catch (ParseException e) {
            return "";
        }
    }

    public double getPriceWithDiscount(double discountPercent) {
        if (discountPercent <= 0) {
            return price;
        }

        double discount = price * (discountPercent / 100.0);
        return price - discount;
    }

    public String getFormattedCommand(String playerName, int quantity) {
        return command
                .replace("{player}", playerName)
                .replace("{quantidade}", String.valueOf(quantity));
    }

    @Override
    public String toString() {
        return "Spawner{" +
                "id='" + id + '\'' +
                ", title='" + title + '\'' +
                ", price=" + price +
                ", skull='" + skull + '\'' +
                ", command='" + command + '\'' +
                ", permission='" + permission + '\'' +
                ", releaseDate='" + releaseDate + '\'' +
                ", requiredRank='" + requiredRank + '\'' +
                '}';
    }
}
