<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.stoneplugins</groupId>
    <artifactId>stonecactos</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <name>StoneCactos</name>
    <description>Plugin de gerador de cactos para Minecraft</description>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <repositories>
        <!-- Spigot Repository -->
        <repository>
            <id>spigot-repo</id>
            <url>https://hub.spigotmc.org/nexus/content/repositories/snapshots/</url>
        </repository>
        
        <!-- PlotSquared Repository -->
        <repository>
            <id>papermc</id>
            <url>https://repo.papermc.io/repository/maven-public/</url>
        </repository>
        
        <!-- Vault Repository -->
        <repository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </repository>
        
        <!-- HolographicDisplays Repository -->
        <repository>
            <id>codemc-repo</id>
            <url>https://repo.codemc.org/repository/maven-public/</url>
        </repository>
    </repositories>

    <dependencies>
        <!-- Spigot API -->
        <dependency>
            <groupId>org.spigotmc</groupId>
            <artifactId>spigot-api</artifactId>
            <version>1.8.8-R0.1-SNAPSHOT</version>
            <scope>provided</scope>
        </dependency>

        <!-- Dependências comentadas temporariamente para compilação
        <dependency>
            <groupId>com.intellectualsites.plotsquared</groupId>
            <artifactId>plotsquared-core</artifactId>
            <version>7.5.4</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.intellectualsites.plotsquared</groupId>
            <artifactId>plotsquared-bukkit</artifactId>
            <version>7.5.4</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.github.MilkBowl</groupId>
            <artifactId>VaultAPI</artifactId>
            <version>1.7</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>me.filoghost.holographicdisplays</groupId>
            <artifactId>holographicdisplays-api</artifactId>
            <version>3.0.0</version>
            <scope>provided</scope>
        </dependency>
        -->

        <!-- Dependências de banco comentadas temporariamente
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>********</version>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.33</version>
        </dependency>
        -->
    </dependencies>

    <build>
        <finalName>stonecactos-${project.version}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.2.4</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <relocations>
                                <relocation>
                                    <pattern>org.sqlite</pattern>
                                    <shadedPattern>com.stoneplugins.stonecactos.libs.sqlite</shadedPattern>
                                </relocation>
                                <relocation>
                                    <pattern>com.mysql</pattern>
                                    <shadedPattern>com.stoneplugins.stonecactos.libs.mysql</shadedPattern>
                                </relocation>
                            </relocations>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- Plugin para copiar automaticamente para o servidor -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <echo message="Copiando plugin para o servidor..." />
                                <delete file="../servidor/plugins/stonecactos-${project.version}.jar" failonerror="false" />
                                <delete dir="../servidor/plugins/StoneCactos" failonerror="false" />
                                <copy file="${project.build.directory}/${project.build.finalName}.jar"
                                      tofile="../servidor/plugins/stonecactos-${project.version}.jar"
                                      failonerror="false" />
                                <echo message="Plugin copiado para: ../servidor/plugins/stonecactos-${project.version}.jar" />
                                <echo message="Pasta de configuracao removida para forcar atualizacao" />
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
