package com.stoneplugins.spawnershop;

import com.stoneplugins.spawnershop.commands.SpawnerShopCommand;
import com.stoneplugins.spawnershop.commands.LimiteCommand;
import com.stoneplugins.spawnershop.database.DatabaseManager;
import com.stoneplugins.spawnershop.economy.EconomyManager;
import com.stoneplugins.spawnershop.listeners.InventoryListener;
import com.stoneplugins.spawnershop.listeners.PlayerChatListener;
import com.stoneplugins.spawnershop.listeners.PlayerInteractListener;
import com.stoneplugins.spawnershop.managers.ConfigManager;
import com.stoneplugins.spawnershop.managers.PlayerDataManager;
import com.stoneplugins.spawnershop.managers.SpawnerManager;

import com.stoneplugins.spawnershop.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

public class SpawnerShop extends JavaPlugin {

    private static SpawnerShop instance;

    private ConfigManager configManager;
    private DatabaseManager databaseManager;
    private EconomyManager economyManager;
    private PlayerDataManager playerDataManager;
    private SpawnerManager spawnerManager;
    private MessageUtils messageUtils;
    private PlayerChatListener playerChatListener;
    private InventoryListener inventoryListener;

    @Override
    public void onEnable() {
        instance = this;

        // ASCII Art personalizado
        getLogger().info(" ");
        getLogger().info("  _____ _______ ____  _   _ ______   _____  _      _    _  _____ _____ _   _  _____ ");
        getLogger().info(" / ____|__   __/ __ \\| \\ | |  ____| |  __ \\| |    | |  | |/ ____|_   _| \\ | |/ ____|");
        getLogger().info("| (___    | | | |  | |  \\| | |__    | |__) | |    | |  | | |  __  | | |  \\| | (___  ");
        getLogger().info(" \\___ \\   | | | |  | | . ` |  __|   |  ___/| |    | |  | | | |_ | | | | . ` |\\___ \\ ");
        getLogger().info(" ____) |  | | | |__| | |\\  | |____  | |    | |____| |__| | |__| |_| |_| |\\  |____) |");
        getLogger().info("|_____/   |_|  \\____/|_| \\_|______| |_|    |______|\\____/ \\_____|_____|_| \\_|_____/ ");
        getLogger().info(" ");
        getLogger().info("SpawnerShop v2.0 - Stone Plugins");
        getLogger().info("Iniciando plugin...");

        // Inicializar managers
        this.configManager = new ConfigManager(this);
        this.messageUtils = new MessageUtils(this);

        // Verificar dependências
        if (!setupDependencies()) {
            getLogger().severe("Dependências necessárias não encontradas! Desabilitando plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Inicializar database
        this.databaseManager = new DatabaseManager(this);
        if (!databaseManager.initialize()) {
            getLogger().severe("Erro ao conectar com o banco de dados! Desabilitando plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Inicializar outros managers
        this.economyManager = new EconomyManager(this);
        this.playerDataManager = new PlayerDataManager(this);
        this.spawnerManager = new SpawnerManager(this);

        // Registrar comandos
        registerCommands();

        // Registrar listeners
        registerListeners();

        // Registrar placeholders
        registerPlaceholders();

        getLogger().info("SpawnerShop v" + getDescription().getVersion() + " habilitado com sucesso!");
    }

    @Override
    public void onDisable() {
        if (databaseManager != null) {
            databaseManager.close();
        }

        getLogger().info("SpawnerShop desabilitado!");
    }

    private boolean setupDependencies() {
        // Verificar Vault
        if (getServer().getPluginManager().getPlugin("Vault") == null) {
            getLogger().severe("Vault não encontrado! Este plugin é obrigatório.");
            return false;
        }

        return true;
    }

    private void registerCommands() {
        // Registrar comando principal
        if (getCommand("spawners") != null) {
            getCommand("spawners").setExecutor(new SpawnerShopCommand(this));
            getLogger().info("Comando /spawners registrado com sucesso!");
        } else {
            getLogger().severe("Erro: Comando 'spawners' não encontrado no plugin.yml!");
        }

        // Registrar comando de administração de limite
        if (getCommand("limite") != null) {
            getCommand("limite").setExecutor(new LimiteCommand(this));
            getLogger().info("Comando /limite registrado com sucesso!");
        } else {
            getLogger().severe("Erro: Comando 'limite' não encontrado no plugin.yml!");
        }
    }

    private void registerListeners() {
        this.inventoryListener = new InventoryListener(this);
        getServer().getPluginManager().registerEvents(inventoryListener, this);
        this.playerChatListener = new PlayerChatListener(this);
        getServer().getPluginManager().registerEvents(playerChatListener, this);
        getServer().getPluginManager().registerEvents(new PlayerInteractListener(this), this);
    }

    private void registerPlaceholders() {
        // PlaceholderAPI será implementado em versão futura
        getLogger().info("Sistema de placeholders será implementado em versão futura.");
    }

    public void reload() {
        configManager.reloadConfig();
        spawnerManager.loadSpawners();
        getLogger().info("Plugin recarregado com sucesso!");
    }

    // Getters
    public static SpawnerShop getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public EconomyManager getEconomyManager() {
        return economyManager;
    }

    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }

    public SpawnerManager getSpawnerManager() {
        return spawnerManager;
    }

    public MessageUtils getMessageUtils() {
        return messageUtils;
    }

    public PlayerChatListener getPlayerChatListener() {
        return playerChatListener;
    }

    public InventoryListener getInventoryListener() {
        return inventoryListener;
    }
}
