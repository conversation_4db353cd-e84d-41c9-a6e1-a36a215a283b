@echo off
echo ========================================
echo    Compilando StoneCactos Plugin v1.0
echo ========================================

REM Mudar para o diretório usando nome curto
cd /d "C:\Users\<USER>\Desktop\plugin~1\StoneCactos"

REM Limpar diretório target
if exist "target" rmdir /s /q "target"
mkdir "target"
mkdir "target\classes"

echo [1/3] Copiando recursos...
if exist "src\main\resources" (
    xcopy /s /y "src\main\resources\*" "target\classes\" >nul
    echo Recursos copiados com sucesso!
) else (
    echo Nenhum recurso encontrado.
)

echo [2/3] Compilando classes Java...
javac -cp "lib\spigot-api.jar" -d "target\classes" -encoding UTF-8 -sourcepath "src\main\java" "src\main\java\com\stoneplugins\stonecactos\StoneCactos.java"

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao das classes Java!
    pause
    exit /b 1
)

echo Classes compiladas com sucesso!

echo [3/3] Criando arquivo JAR...
cd "target\classes"
jar cf "..\stonecactos-1.0.jar" .
cd "..\..\"

if exist "target\stonecactos-1.0.jar" (
    echo ========================================
    echo    COMPILACAO CONCLUIDA COM SUCESSO!
    echo ========================================
    echo Arquivo gerado: target\stonecactos-1.0.jar
    for %%I in (target\stonecactos-1.0.jar) do echo Tamanho: %%~zI bytes

    echo [4/4] Copiando para o servidor...
    set "SERVER_DIR=..\servidor\plugins"

    REM Tentar copiar com diferentes estratégias
    echo Tentando copiar plugin para o servidor...

    REM Estratégia 1: Copiar com novo nome temporário
    copy "target\stonecactos-1.0.jar" "%SERVER_DIR%\stonecactos-1.0-new.jar" >nul 2>&1

    if exist "%SERVER_DIR%\stonecactos-1.0-new.jar" (
        echo Plugin copiado como stonecactos-1.0-new.jar
        echo INSTRUCOES:
        echo 1. Pare o servidor se estiver rodando
        echo 2. Remova o arquivo antigo: %SERVER_DIR%\stonecactos-1.0.jar
        echo 3. Renomeie stonecactos-1.0-new.jar para stonecactos-1.0.jar
        echo 4. Inicie o servidor novamente
    ) else (
        REM Estratégia 2: Tentar copiar diretamente (pode falhar se servidor estiver rodando)
        copy "target\stonecactos-1.0.jar" "%SERVER_DIR%\stonecactos-1.0.jar" >nul 2>&1

        if exist "%SERVER_DIR%\stonecactos-1.0.jar" (
            echo Plugin copiado para o servidor com sucesso!
            echo Localização: %SERVER_DIR%\stonecactos-1.0.jar
        ) else (
            echo AVISO: Falha ao copiar para o servidor.
            echo O servidor pode estar rodando e bloqueando o arquivo.
            echo Plugin compilado disponível em: target\stonecactos-1.0.jar
            echo Copie manualmente quando o servidor estiver parado.
        )
    )

    echo ========================================
) else (
    echo ERRO: Falha ao criar o arquivo JAR!
    exit /b 1
)

pause
