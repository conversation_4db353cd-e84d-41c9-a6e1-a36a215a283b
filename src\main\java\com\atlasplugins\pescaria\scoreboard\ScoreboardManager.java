package com.atlasplugins.pescaria.scoreboard;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.models.Mission;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.scoreboard.DisplaySlot;
import org.bukkit.scoreboard.Objective;
import org.bukkit.scoreboard.Score;
import org.bukkit.scoreboard.Scoreboard;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ScoreboardManager {

    private final Pescaria plugin;

    public ScoreboardManager(Pescaria plugin) {
        this.plugin = plugin;
    }

    public void updateScoreboard(Player player) {
        if (player == null || !player.isOnline()) {
            return;
        }

        // Verificar se está no mundo de pesca
        String currentWorld = player.getWorld().getName();
        String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();

        // Debug logs
        plugin.getLogger().info("=== SCOREBOARD DEBUG ===");
        plugin.getLogger().info("Player: " + player.getName());
        plugin.getLogger().info("Current World: " + currentWorld);
        plugin.getLogger().info("Pescaria World: " + pescariaWorld);
        plugin.getLogger().info("Is Fishing World: " + currentWorld.equalsIgnoreCase(pescariaWorld));
        plugin.getLogger().info("=======================");

        if (!currentWorld.equalsIgnoreCase(pescariaWorld)) {
            plugin.getLogger().info("Removing scoreboard - not in fishing world");
            removeScoreboard(player);
            return;
        }

        plugin.getLogger().info("Creating fishing scoreboard");

        // SEMPRE criar um novo scoreboard para evitar conflitos
        Scoreboard scoreboard = Bukkit.getScoreboardManager().getNewScoreboard();

        // Criar objective
        Objective objective = scoreboard.registerNewObjective("pescaria", "dummy");
        objective.setDisplaySlot(DisplaySlot.SIDEBAR);
        objective.setDisplayName("§b§lÁREA DE PESCA");

        // Obter dados do jogador
        UUID uuid = player.getUniqueId();
        int fishes = plugin.getPlayerManager().getPlayerFishes(uuid);
        int completedMissions = plugin.getPlayerManager().getCompletedMissions(uuid);

        // Obter saldo
        double balance = plugin.getEconomy() != null ? plugin.getEconomy().getBalance(player) : 0;
        String formattedBalance = String.format("%.0f", balance);

        // Obter cash com debug
        String cashBalance = "0";
        plugin.getLogger().info("=== CASH DEBUG ===");
        plugin.getLogger().info("Player: " + player.getName());

        if (plugin.getSCashIntegration() != null && plugin.getSCashIntegration().isEnabled()) {
            double sCashBalance = plugin.getSCashIntegration().getBalance(player);
            cashBalance = String.format("%.0f", sCashBalance);
            plugin.getLogger().info("sCash encontrado - Balance: " + sCashBalance);
        } else if (plugin.getBetterEconomyIntegration() != null && plugin.getBetterEconomyIntegration().isEnabled()) {
            double betterEcoBalance = plugin.getBetterEconomyIntegration().getBalance(player);
            cashBalance = String.format("%.0f", betterEcoBalance);
            plugin.getLogger().info("BetterEconomy encontrado - Balance: " + betterEcoBalance);
        } else {
            plugin.getLogger().info("Nenhuma integração de cash encontrada");
            // Tentar usar valor padrão ou placeholder manual
            plugin.getLogger().info("Tentando obter cash de outras fontes...");
            cashBalance = "0"; // Valor padrão
        }

        plugin.getLogger().info("Cash final: " + cashBalance);
        plugin.getLogger().info("================");

        // Obter dados da vara
        int xp = plugin.getConfigManager().getDataConfig().getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
        int level = calculateLevel(xp);
        int nextLevelXP = getXPForNextLevel(level);

        // Criar linhas do scoreboard
        String[] lines = {
                "§1",
                "§f Missões: §7" + completedMissions,
                "§f Bônus: §aInativo",
                "§2",
                "§e§l ▸ Vara:",
                "§b§l  ▎ §fNv: §7" + level,
                "§b§l  ▎ §fXP: §7" + xp + "/" + nextLevelXP,
                "§3",
                "§f $: §a" + formattedBalance,
                "§f Cash: §6✪" + cashBalance,
                "§f Peixes: §e✧" + fishes,
                "§4",
                "§bStonePlugins.com"
        };

        // Adicionar linhas
        for (int i = 0; i < lines.length; i++) {
            String line = lines[i];
            if (line.length() > 40) {
                line = line.substring(0, 37) + "...";
            }
            Score score = objective.getScore(line);
            score.setScore(lines.length - i);
        }

        // FORÇAR aplicação do scoreboard com delay para evitar conflitos
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.setScoreboard(scoreboard);
            plugin.getLogger().info("Scoreboard FORÇADO aplicado para " + player.getName());

            // Verificar se grudou após 1 segundo
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                Scoreboard currentBoard = player.getScoreboard();
                if (currentBoard == null || currentBoard == Bukkit.getScoreboardManager().getMainScoreboard()
                        || currentBoard.getObjective("pescaria") == null) {
                    plugin.getLogger().info("PROBLEMA: Scoreboard não grudou! Tentando novamente...");
                    player.setScoreboard(scoreboard);

                    // Última tentativa após mais 1 segundo
                    plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                        player.setScoreboard(scoreboard);
                        plugin.getLogger().info("ÚLTIMA TENTATIVA de aplicar scoreboard para " + player.getName());
                    }, 20L);
                } else {
                    plugin.getLogger().info("SUCCESS: Scoreboard grudou para " + player.getName());
                }
            }, 20L);
        }, 5L); // Delay inicial de 5 ticks
    }

    public void removeScoreboard(Player player) {
        if (player != null && player.isOnline()) {
            player.setScoreboard(Bukkit.getScoreboardManager().getNewScoreboard());
        }
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10;
    }

    private int getXPForNextLevel(int currentLevel) {
        switch (currentLevel) {
            case 1:
                return 250;
            case 2:
                return 500;
            case 3:
                return 1000;
            case 4:
                return 2000;
            case 5:
                return 4000;
            case 6:
                return 8000;
            case 7:
                return 16000;
            case 8:
                return 32000;
            case 9:
                return 64000;
            default:
                return 64000;
        }
    }
}