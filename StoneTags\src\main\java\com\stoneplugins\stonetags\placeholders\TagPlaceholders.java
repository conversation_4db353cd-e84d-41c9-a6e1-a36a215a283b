package com.stoneplugins.stonetags.placeholders;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.entity.Player;
import org.jetbrains.annotations.NotNull;

public class TagPlaceholders extends PlaceholderExpansion {

    private final StoneTags plugin;

    public TagPlaceholders(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    @NotNull
    public String getIdentifier() {
        return "stonetags";
    }

    @Override
    @NotNull
    public String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    @Override
    @NotNull
    public String getVersion() {
        return plugin.getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true;
    }

    @Override
    public String onPlaceholderRequest(Player player, @NotNull String params) {
        if (player == null) {
            return "";
        }

        // %stonetags_tag_<informação>% - Informações da tag do jogador
        if (params.startsWith("tag_")) {
            String info = params.substring(4);
            return plugin.getPlayerDataManager().getPlayerTagInfo(player, info);
        }

        // %stonetags_maxtag_<informação>% - Informações da tag de maior prioridade
        if (params.startsWith("maxtag_")) {
            String info = params.substring(7);
            Tag highestTag = plugin.getTagManager().getHighestPriorityTag(player);
            if (highestTag == null)
                return "";
            return plugin.getTagManager().getTagInfo(highestTag.getId(), info);
        }

        // %stonetags_tags_<id>_<informação>% - Informações de uma tag específica
        if (params.startsWith("tags_")) {
            String[] parts = params.split("_", 3);
            if (parts.length >= 3) {
                String tagId = parts[1];
                String info = parts[2];
                return plugin.getTagManager().getTagInfo(tagId, info);
            }
        }

        // %stonetags_suffix_<informação>% - Informações do sufixo customizado
        if (params.startsWith("suffix_")) {
            String info = params.substring(7);
            return plugin.getPlayerDataManager().getPlayerSuffixInfo(player, info);
        }

        // Placeholders simples
        switch (params.toLowerCase()) {
            case "has_tag":
                return String.valueOf(plugin.getPlayerDataManager().hasSelectedTag(player));

            case "tag_count":
                return String.valueOf(plugin.getTagManager().getAvailableTags(player).size());

            case "current_tag_id":
                Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTag != null ? currentTag.getId() : "";

            case "current_tag_name":
                Tag currentTagName = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTagName != null ? currentTagName.getName() : "";

            case "current_tag_prefix":
                Tag currentTagPrefix = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTagPrefix != null ? currentTagPrefix.getCurrentPrefix(player) : "";

            case "current_tag_suffix":
                Tag currentTagSuffix = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTagSuffix != null ? currentTagSuffix.getSuffix() : "";

            case "current_tag_color":
                Tag currentTagColor = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTagColor != null ? currentTagColor.getColor() : "";

            case "current_tag_position":
                Tag currentTagPos = plugin.getPlayerDataManager().getSelectedTag(player);
                return currentTagPos != null ? String.valueOf(currentTagPos.getPosition()) : "0";

            case "highest_tag_id":
                Tag highestTag = plugin.getTagManager().getHighestPriorityTag(player);
                return highestTag != null ? highestTag.getId() : "";

            case "highest_tag_name":
                Tag highestTagName = plugin.getTagManager().getHighestPriorityTag(player);
                return highestTagName != null ? highestTagName.getName() : "";

            case "highest_tag_position":
                Tag highestTagPos = plugin.getTagManager().getHighestPriorityTag(player);
                return highestTagPos != null ? String.valueOf(highestTagPos.getPosition()) : "0";

            case "custom_suffix":
                String customSuffix = plugin.getPlayerDataManager().getCustomSuffix(player);
                return customSuffix != null ? customSuffix : "";

            case "custom_name":
                String customName = plugin.getPlayerDataManager().getCustomName(player);
                return customName != null ? customName : "";

            // NOVOS PLACEHOLDERS PARA SUFIXOS
            case "current_suffix_id":
                com.stoneplugins.stonetags.data.Suffix currentSuffix = plugin.getPlayerDataManager()
                        .getSelectedSuffix(player);
                return currentSuffix != null ? currentSuffix.getId() : "";

            case "current_suffix_name":
                com.stoneplugins.stonetags.data.Suffix currentSuffixName = plugin.getPlayerDataManager()
                        .getSelectedSuffix(player);
                return currentSuffixName != null ? currentSuffixName.getName() : "";

            case "current_suffix_display":
                com.stoneplugins.stonetags.data.Suffix currentSuffixDisplay = plugin.getPlayerDataManager()
                        .getSelectedSuffix(player);
                return currentSuffixDisplay != null ? currentSuffixDisplay.getDisplaySuffix() : "";

            case "has_suffix":
                return String.valueOf(plugin.getPlayerDataManager().hasSelectedSuffix(player));

            case "display_name":
                return player.getDisplayName();

            case "formatted_name":
                // Retorna o nome formatado com tag e suffix
                Tag tag = plugin.getPlayerDataManager().getSelectedTag(player);
                com.stoneplugins.stonetags.data.Suffix suffix = plugin.getPlayerDataManager().getSelectedSuffix(player);

                String prefix = "";
                String tagSuffix = "";
                String suffixDisplay = "";

                if (tag != null && !tag.isInvisible()) {
                    prefix = plugin.getMessageUtils().colorize(tag.getCurrentPrefix(player));
                    tagSuffix = plugin.getMessageUtils().colorize(tag.getSuffix());
                }

                if (suffix != null) {
                    suffixDisplay = " " + plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
                }

                return prefix + player.getName() + tagSuffix + suffixDisplay;

            default:
                return null;
        }
    }
}
