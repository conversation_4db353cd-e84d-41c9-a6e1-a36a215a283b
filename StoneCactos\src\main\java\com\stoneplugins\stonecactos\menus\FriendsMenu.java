package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.data.FriendPermissions;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

public class FriendsMenu extends BaseMenu {

    private final CactusGenerator generator;
    private int currentPage = 0;
    private final int itemsPerPage = 7; // Slots configurados no menu

    public FriendsMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("friends"));
        int size = plugin.getConfigManager().getMenuSize("friends");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro (se configurado)
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.friends.glassPaneSlots");
        if (glassSlots != null) {
            fillGlassSlots(glassSlots);
        }

        // Configurar amigos
        setupFriends();

        // Configurar navegação
        setupNavigation();

        // Botão de voltar
        setupBackButton();

        // Botão de adicionar amigo
        setupAddFriendButton();
    }

    private void setupFriends() {
        Set<UUID> friends = plugin.getFriendsManager().getFriends(generator);
        List<UUID> friendsList = new ArrayList<>(friends);

        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.friends.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        int startIndex = currentPage * itemsPerPage;

        for (int i = 0; i < slots.length && i < itemsPerPage; i++) {
            int friendIndex = startIndex + i;

            if (friendIndex >= friendsList.size()) {
                break;
            }

            UUID friendUuid = friendsList.get(friendIndex);
            int slot = Integer.parseInt(slots[i].trim());

            // Criar item do amigo
            ItemStack friendItem = createFriendItem(friendUuid);
            setItem(slot, friendItem);
        }
    }

    private ItemStack createFriendItem(UUID friendUuid) {
        String friendName = plugin.getFriendsManager().getPlayerName(friendUuid);
        FriendPermissions perms = plugin.getFriendsManager().getFriendPermissions(generator, friendUuid);

        // Criar lore com permissões
        List<String> lore = new ArrayList<>();
        lore.add("§7Permissões:");
        lore.add("§f• Adicionar Torres: " + (perms.canAddTowers() ? "§aPermitido" : "§cNegado"));
        lore.add("§f• Remover Torres: " + (perms.canRemoveTowers() ? "§aPermitido" : "§cNegado"));
        lore.add("§f• Vender Cactos: " + (perms.canSellCactus() ? "§aPermitido" : "§cNegado"));
        lore.add("");

        if (generator.isOwner(player)) {
            lore.add("§eClique para gerenciar permissões");
            lore.add("§cShift+Clique para remover amigo");
        } else {
            lore.add("§7Você não pode gerenciar este amigo");
        }

        // Usar cabeça do jogador
        ItemStack item = plugin.getItemManager().createPlayerHead(friendName);
        item.getItemMeta().setDisplayName("§a" + friendName);
        item.getItemMeta().setLore(lore);

        return item;
    }

    private void setupAddFriendButton() {
        if (generator.isOwner(player)) {
            List<String> addLore = Arrays.asList(
                    "§7Clique para adicionar um novo amigo",
                    "§7Digite o nome do jogador no chat");
            ItemStack addButton = createItem(Material.valueOf("SKULL_ITEM"), (short) 3,
                    "§aAdicionar Amigo", addLore);
            setItem(22, addButton); // Slot central
        }
    }

    private void setupNavigation() {
        Set<UUID> friends = plugin.getFriendsManager().getFriends(generator);
        int totalPages = (int) Math.ceil((double) friends.size() / itemsPerPage);

        // Botão página anterior
        if (currentPage > 0) {
            int prevSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.previusPage", 27);
            ItemStack prevButton = createItem(Material.ARROW, "§ePágina Anterior",
                    "§7Clique para ir para a página " + currentPage);
            setItem(prevSlot, prevButton);
        }

        // Botão próxima página
        if (currentPage < totalPages - 1) {
            int nextSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.nextPage", 35);
            ItemStack nextButton = createItem(Material.ARROW, "§ePróxima Página",
                    "§7Clique para ir para a página " + (currentPage + 2));
            setItem(nextSlot, nextButton);
        }
    }

    @Override
    protected void setupBackButton() {
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.backToMainInventory.slot", 31);
        ItemStack backButton = createItem(Material.ARROW, "§cVoltar", "§7Clique para voltar ao menu principal");
        setItem(backSlot, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        // Slots de navegação
        int prevSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.previusPage", 27);
        int nextSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.nextPage", 35);
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.friends.backToMainInventory.slot", 31);
        int addSlot = 22;

        if (slot == prevSlot) {
            handlePreviousPage();
        } else if (slot == nextSlot) {
            handleNextPage();
        } else if (slot == backSlot) {
            handleBackClick();
        } else if (slot == addSlot) {
            handleAddFriendClick();
        } else {
            // Verificar se é um slot de amigo
            handleFriendClick(slot, shiftClick);
        }
    }

    private void handlePreviousPage() {
        if (currentPage > 0) {
            currentPage--;
            update();
        }
    }

    private void handleNextPage() {
        Set<UUID> friends = plugin.getFriendsManager().getFriends(generator);
        int totalPages = (int) Math.ceil((double) friends.size() / itemsPerPage);

        if (currentPage < totalPages - 1) {
            currentPage++;
            update();
        }
    }

    @Override
    protected void handleBackClick() {
        // Fechar inventário primeiro
        player.closeInventory();

        // Abrir menu principal após um delay para evitar conflitos
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getMenuManager().openMainMenu(player, generator);
        }, 2L);
    }

    private void handleAddFriendClick() {
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Fechar menu e pedir nome do jogador
        close();
        List<String> addFriendMessages = plugin.getConfigManager().getMessageList("addFriend");
        for (String message : addFriendMessages) {
            player.sendMessage(message);
        }

        // TODO: Implementar sistema de input de chat
    }

    private void handleFriendClick(int slot, boolean shiftClick) {
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Verificar se o slot corresponde a um amigo
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.friends.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        int friendIndex = -1;

        for (int i = 0; i < slots.length; i++) {
            if (Integer.parseInt(slots[i].trim()) == slot) {
                friendIndex = currentPage * itemsPerPage + i;
                break;
            }
        }

        if (friendIndex == -1) {
            return;
        }

        Set<UUID> friends = plugin.getFriendsManager().getFriends(generator);
        List<UUID> friendsList = new ArrayList<>(friends);

        if (friendIndex >= friendsList.size()) {
            return;
        }

        UUID friendUuid = friendsList.get(friendIndex);

        if (shiftClick) {
            // Remover amigo
            plugin.getFriendsManager().removeFriend(generator, friendUuid);
            String friendName = plugin.getFriendsManager().getPlayerName(friendUuid);
            player.sendMessage("§eAmigo " + friendName + " removido com sucesso!");
            update();
        } else {
            // Abrir menu de permissões
            new FriendPermissionMenu(plugin, player, generator, friendUuid).open();
        }
    }

    /**
     * Define a página atual
     */
    public void setPage(int page) {
        this.currentPage = Math.max(0, page);
    }

    /**
     * Obtém a página atual
     */
    public int getCurrentPage() {
        return currentPage;
    }
}
