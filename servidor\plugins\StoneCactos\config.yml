# Configuração do StoneCactos
# Plugin de gerador de cactos para Minecraft

# Configurações do banco de dados
MySQL:
  host: "localhost"
  user: "root"
  password: "1234"
  database: "db"

SQLite:
  file: "database.db"

# Configurações gerais
Geral:
  sqlType: SQLITE # SQLITE OU MYSQL
  cashPlugin: STORMECONOMIASECUNDARIA # STORMECONOMIASECUNDARIA, ATLASECONOMIASECUNDARIA, YECONOMIAS, YPOINTS, PLAYERPOINTS
  plotWorldName: "plots" # Coloque o nome do mundo onde estão as plots
  unitCactusValue: 100 # Preço da unidade do cacto
  cactusEconomy: VAULT # CASO ESTIVER USANDO O YECONOMIAS, COLOQUE O NOME DA ECONOMIA, CASO CONTRARIO ESCOLHA ENTRE VAULT E CASH
  standardBatteryConsumption:
    cactus: 10 # A CADA 10 CACTOS GERADOS
    consume: 1.0 # IRA CONSUMIR 1% DE BATERIA
  cactusHeight: 2.3
  cactusHologram:
    - "&a&lGERADOR DE CACTOS"
    - "&fDono: &7%owner%"
    - "&fTotal de torres: &e%towers%"
    - "&fCapacidade: &a%capacity%"
  
  # Configurações de permissões para amigos
  friendPermissions:
    canAccessMainMenu: false # Se amigos podem acessar o menu principal
    canAccessWarehouse: true # Se amigos podem acessar o armazém
    canAccessDeposit: false # Se amigos podem acessar o depósito
    canAccessUpgrade: false # Se amigos podem acessar upgrades
    canAccessBoosters: false # Se amigos podem acessar boosters
    canAccessFriends: false # Se amigos podem gerenciar outros amigos

# Configuração do item gerador de cactos
cactus:
  # Textura de cabeça personalizada de um cacto bonito
  item: "eyJ0ZXh0dXJlcyI6eyJTS0lOIjp7InVybCI6Imh0dHA6Ly90ZXh0dXJlcy5taW5lY3JhZnQubmV0L3RleHR1cmUvNmQ5MGNkNjllNzVhZjc0ODQ5YWYxYTFmNmE3ZmY0ZmJhZmFhYjZlYmU1OTNkZmQxOTk2MWU3ODY4OGVjZjA5MyJ9fX0="
  name: "&a&lGERADOR DE CACTOS"
  lore:
    - "&7Veja abaixo as informações do gerador."
    - ""
    - "&fDono: &7%owner%"
    - "&fTotal de torres: &e%towers%"
    - "&fCapacidade: &a%capacity%"
    - ""
    - "&7Clique com &fdireito&7, para colocar o"
    - "&7gerador em seu plot."

# Configuração do item booster
booster:
  item: "EXP_BOTTLE:0"
  name: "&aBooster de Cactos"
  lore:
    - '&f Multiplicação: &72.0x'
    - '&f Duração: &71 hora.'
    - ''
    - "&7Clique para ativar"

# Status das recompensas
rewardStatus:
  notCollected: "§7Clique para desbloquear"
  collected: "§aRecompensa ja coletada."

# Configuração dos boosters
Boosters:
  BOOSTER1:
    # Métodos de uso:
    # MATERIAL:DATA
    # TEXTURE DE UMA HEAD OU %jogador% PARA A CABEÇA DO JOGADOR
    item: "EXP_BOTTLE:0"
    name: "&aBooster de Cactos"
    lore:
      - ''
      - '&f Multiplicação: &72.0x'
      - '&f Duração: &71 hora.'
      - '&f Preço: &7%price%'
      - ''
    time: 60 # Coloque a quantidade em minutos
    multiplier: 2.0
    price: 50000
    economy: VAULT # VAULT OR CASH

# Configuração das recompensas
Rewards:
  REWARD1:
    id: 0
    item: "DIAMOND_CHESTPLATE:0"
    name: "§ePeitoral de Diamante"
    necessaryCactus: 10
    lore:
      - "§f Cactos necessários: §7%cactus%"
      - ""
      - "%status%"
    rewardCommands:
      - 'giveitem capacetedediamante %player% 1'
  REWARD2:
    id: 1
    item: "DIAMOND_HELMET:0"
    name: "§eCapacete de Diamante"
    necessaryCactus: 10
    lore:
      - "§f Cactos necessários: §7%cactus%"
      - ""
      - "%status%"
    rewardCommands:
      - 'giveitem capacetedediamante %player% 1'

# Configuração dos itens de bateria/combustível
BatteryItems:
  COAL:
    material: "COAL:0"
    name: "&8Carvão"
    batteryValue: 10.0
    lore:
      - "&7Combustível básico"
      - "&f+10% de bateria"
      - "&eClique para usar"
  CHARCOAL:
    material: "COAL:1"
    name: "&8Carvão Vegetal"
    batteryValue: 15.0
    lore:
      - "&7Combustível melhorado"
      - "&f+15% de bateria"
      - "&eClique para usar"
  BLAZE_ROD:
    material: "BLAZE_ROD:0"
    name: "&6Vara de Blaze"
    batteryValue: 25.0
    lore:
      - "&7Combustível avançado"
      - "&f+25% de bateria"
      - "&eClique para usar"
  LAVA_BUCKET:
    material: "LAVA_BUCKET:0"
    name: "&cBalde de Lava"
    batteryValue: 50.0
    lore:
      - "&7Combustível premium"
      - "&f+50% de bateria"
      - "&eClique para usar"

# Configuração dos upgrades
EnvolveDevelopment:
  capacityTowers:
    0:
      price: 10000
      economy: VAULT # VAULT OR CASH
      capacity: 100
    1:
      price: 20000
      economy: VAULT
      capacity: 200
    2:
      price: 30000
      economy: VAULT
      capacity: 300
    3:
      price: 40000
      economy: VAULT
      capacity: 400
  quantityPerConstruction:
    0:
      price: 10000
      economy: VAULT
      quantity: 2
    1:
      price: 20000
      economy: VAULT
      quantity: 5
    2:
      price: 30000
      economy: VAULT
      quantity: 10
    3:
      price: 40000
      economy: VAULT
      quantity: 15
  constructionTime:
    0:
      price: 10000
      economy: VAULT
      time: 40000
    1:
      price: 20000
      economy: VAULT
      time: 30000
    2:
      price: 30000
      economy: VAULT
      time: 20000
    3:
      price: 40000
      economy: VAULT
      time: 10000
