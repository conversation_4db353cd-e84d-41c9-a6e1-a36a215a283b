## Main Config (https://github.com/r4g3baby/SimpleScore/blob/main/src/main/resources/config.yml)
# This is where you can customize some plugin settings and the default world scoreboards.

# Config version (don't change this value)
version: 3

# Plugin language (supported: en, fr, pt, zh_CN)
# To use custom messages read the plugin documentation page
language: en

# Check for updates when the plugin starts
checkForUpdates: true

# Whether to apply placeholders asynchronously or not (disabling this will reduce performance)
asyncPlaceholders: true

# Changes packet level compatibility mode (only works with ProtocolLib enabled)
#   disable: Disables SimpleScore when it detects another plugin scoreboard is being set
#   block: Blocks other plugins from overriding the SimpleScore scoreboard
#   none: Disables compatibility mode
compatibilityMode: disable

# Forces multiple version support, only enable this if your server supports multiple versions and players on 1.12 and
#  older are experiencing scoreboard inconsistencies
forceMultiVersion: false

# Enable this if you have other plugins that are overriding the scoreboard (requires server restart)
forceLegacy: false

# Any changes done to the storage configuration require a server restart
storage:
  # Supported database drivers:
  #  Local: H2, SQLite
  #  Remote: PostgreSQL, MariaDB, MySQL
  # Set to "none" to disable player data saving.
  driver: h2
  # Prefix used for SimpleScore tables
  tablePrefix: simplescore_

  # All the settings bellow this point only apply to remote databases
  address: 127.0.0.1:3306
  database: minecraft
  username: simplescore
  password: '|D/-\55\^/0|2|)'

  # HikariCP's connection pool settings for remote databases
  # The default provided settings should work fine for most server setups
  # Do not change these settings unless you know what you're doing!
  pool:
    # The maximum number of connections in the pool
    maximumPoolSize: 8
    # The minimum number of idle connections in the pool to maintain
    minimumIdle: 8
    # The maximum connection lifetime in milliseconds
    maxLifetime: 1800000
    # The interval in which connections will be tested for aliveness, thus keeping them alive by
    #  the act of checking. Value is in milliseconds
    keepaliveTime: 0
    # The connection timeout in milliseconds
    connectionTimeout: 5000
    # Extra properties to pass on to the data source driver
    extraProperties:
      useUnicode: true
      characterEncoding: utf8

# The format here is "<world name>: [ <list of scoreboards> ]"
# World Names:
# - Can use regex to match multiple worlds at once.
# - All world names are case-insensitive.
# - Worlds match from top to bottom, stopping at the first match.
# Scoreboards:
# - Scoreboards are defined in the "scoreboards.yml" file.
# - Scoreboard name is the same as defined in the scoreboards configuration node name.
# - The leftmost scoreboard in the list always takes priority.
worlds:
  # If player doesn't meet the conditions to see the conditional scoreboard then it will display the simple scoreboard
  world_the_end: [ 'conditional', 'simple' ]
  # This will match all worlds starting with "world_" (e.g. world_nether, world_the_end)
  # "world_the_end" will never reach this line since it already matched above
  world_\w*: [ 'customTiming' ]
  # This will match all worlds that haven't been defined above
  '[\w\- ]*': [ 'simple' ]
  # Adding anything here will not work as it was already matched by the regex specified above
