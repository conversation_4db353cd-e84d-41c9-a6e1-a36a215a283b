package com.stoneplugins.stonecactos.data;

public class FriendPermissions {
    
    private boolean canAddTowers;
    private boolean canRemoveTowers;
    private boolean canSellCactus;
    
    public FriendPermissions(boolean canAddTowers, boolean canRemoveTowers, boolean canSellCactus) {
        this.canAddTowers = canAddTowers;
        this.canRemoveTowers = canRemoveTowers;
        this.canSellCactus = canSellCactus;
    }
    
    // Getters e Setters
    public boolean canAddTowers() {
        return canAddTowers;
    }
    
    public void setCanAddTowers(boolean canAddTowers) {
        this.canAddTowers = canAddTowers;
    }
    
    public boolean canRemoveTowers() {
        return canRemoveTowers;
    }
    
    public void setCanRemoveTowers(boolean canRemoveTowers) {
        this.canRemoveTowers = canRemoveTowers;
    }
    
    public boolean canSellCactus() {
        return canSellCactus;
    }
    
    public void setCanSellCactus(boolean canSellCactus) {
        this.canSellCactus = canSellCactus;
    }
    
    @Override
    public String toString() {
        return "FriendPermissions{" +
                "canAddTowers=" + canAddTowers +
                ", canRemoveTowers=" + canRemoveTowers +
                ", canSellCactus=" + canSellCactus +
                '}';
    }
}
