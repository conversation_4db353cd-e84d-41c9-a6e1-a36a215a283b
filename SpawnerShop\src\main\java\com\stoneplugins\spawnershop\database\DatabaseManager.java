package com.stoneplugins.spawnershop.database;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;

import java.io.File;
import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class DatabaseManager {

    private final SpawnerShop plugin;
    private Connection connection;

    public DatabaseManager(SpawnerShop plugin) {
        this.plugin = plugin;
    }

    public boolean initialize() {
        try {
            if (plugin.getConfigManager().getDatabaseType().equalsIgnoreCase("MYSQL")) {
                setupMySQL();
            } else {
                setupSQLite();
            }

            createTables();
            return true;
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao conectar com o banco de dados: " + e.getMessage());
            return false;
        }
    }

    private void setupMySQL() throws SQLException {
        String host = plugin.getConfigManager().getMySQLHost();
        String user = plugin.getConfigManager().getMySQLUser();
        String password = plugin.getConfigManager().getMySQLPassword();
        String database = plugin.getConfigManager().getMySQLDatabase();

        String url = "jdbc:mysql://" + host + ":3306/" + database + "?useSSL=false&autoReconnect=true";

        connection = DriverManager.getConnection(url, user, password);
        plugin.getLogger().info("Conectado ao MySQL com sucesso!");
    }

    private void setupSQLite() throws SQLException {
        File dataFolder = plugin.getDataFolder();
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        String fileName = plugin.getConfigManager().getSQLiteFile();
        File databaseFile = new File(dataFolder, fileName);

        String url = "jdbc:sqlite:" + databaseFile.getAbsolutePath();
        connection = DriverManager.getConnection(url);

        plugin.getLogger().info("Conectado ao SQLite com sucesso!");
    }

    private void createTables() throws SQLException {
        // Tabela de dados dos jogadores
        String createPlayerDataTable = "CREATE TABLE IF NOT EXISTS player_data (" +
                "uuid VARCHAR(36) PRIMARY KEY," +
                "name VARCHAR(16) NOT NULL," +
                "purchases INT DEFAULT 0," +
                "purchase_limit INT DEFAULT " + plugin.getConfigManager().getDefaultLimit() + "," +
                "last_purchase BIGINT DEFAULT 0," +
                "multiplicador INT DEFAULT 1" +
                ")";

        // Adicionar coluna multiplicador se não existir (para bancos existentes)
        String addMultiplicadorColumn = "ALTER TABLE player_data ADD COLUMN multiplicador INT DEFAULT 1";

        try {
            executeUpdate(createPlayerDataTable);
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao criar tabela player_data: " + e.getMessage());
        }

        // Tentar adicionar coluna multiplicador (ignorar erro se já existir)
        try {
            executeUpdate(addMultiplicadorColumn);
            plugin.getLogger().info("Coluna multiplicador adicionada com sucesso!");
        } catch (SQLException e) {
            // Ignorar erro se coluna já existir
            plugin.getLogger().info("Coluna multiplicador já existe ou erro ao adicionar: " + e.getMessage());
        }

        // Tabela de histórico de compras
        String createPurchaseHistoryTable = "CREATE TABLE IF NOT EXISTS purchase_history (" +
                "id INTEGER PRIMARY KEY "
                + (plugin.getConfigManager().getDatabaseType().equalsIgnoreCase("MYSQL") ? "AUTO_INCREMENT"
                        : "AUTOINCREMENT")
                + "," +
                "uuid VARCHAR(36) NOT NULL," +
                "spawner_type VARCHAR(50) NOT NULL," +
                "quantity INT NOT NULL," +
                "price DOUBLE NOT NULL," +
                "purchase_date BIGINT NOT NULL" +
                ")";

        try (Statement stmt = connection.createStatement()) {
            stmt.execute(createPlayerDataTable);
            stmt.execute(createPurchaseHistoryTable);
        }

        plugin.getLogger().info("Tabelas do banco de dados criadas/verificadas com sucesso!");
    }

    private void executeUpdate(String sql) throws SQLException {
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.executeUpdate();
        }
    }

    public PlayerData getPlayerData(UUID uuid) {
        String query = "SELECT * FROM player_data WHERE uuid = ?";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, uuid.toString());

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return new PlayerData(
                            UUID.fromString(rs.getString("uuid")),
                            rs.getString("name"),
                            rs.getInt("purchases"),
                            rs.getInt("purchase_limit"),
                            rs.getLong("last_purchase"),
                            rs.getInt("multiplicador"));
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao buscar dados do jogador: " + e.getMessage());
        }

        return null;
    }

    public void savePlayerData(PlayerData playerData) {
        String query = "INSERT OR REPLACE INTO player_data (uuid, name, purchases, purchase_limit, last_purchase, multiplicador) VALUES (?, ?, ?, ?, ?, ?)";

        if (plugin.getConfigManager().getDatabaseType().equalsIgnoreCase("MYSQL")) {
            query = "INSERT INTO player_data (uuid, name, purchases, purchase_limit, last_purchase, multiplicador) VALUES (?, ?, ?, ?, ?, ?) "
                    +
                    "ON DUPLICATE KEY UPDATE name = VALUES(name), purchases = VALUES(purchases), " +
                    "purchase_limit = VALUES(purchase_limit), last_purchase = VALUES(last_purchase), multiplicador = VALUES(multiplicador)";
        }

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, playerData.getUuid().toString());
            stmt.setString(2, playerData.getName());
            stmt.setInt(3, playerData.getPurchases());
            stmt.setInt(4, playerData.getPurchaseLimit());
            stmt.setLong(5, playerData.getLastPurchase());
            stmt.setInt(6, playerData.getMultiplicador());

            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao salvar dados do jogador: " + e.getMessage());
        }
    }

    public void addPurchaseHistory(UUID uuid, String spawnerType, int quantity, double price) {
        String query = "INSERT INTO purchase_history (uuid, spawner_type, quantity, price, purchase_date) VALUES (?, ?, ?, ?, ?)";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, uuid.toString());
            stmt.setString(2, spawnerType);
            stmt.setInt(3, quantity);
            stmt.setDouble(4, price);
            stmt.setLong(5, System.currentTimeMillis());

            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao adicionar histórico de compra: " + e.getMessage());
        }
    }

    public List<PlayerData> getTopPlayers(int limit) {
        return getTopPlayers(limit, false);
    }

    public List<PlayerData> getTopPlayers(int limit, boolean orderByLimit) {
        List<PlayerData> topPlayers = new ArrayList<>();
        String orderBy = orderByLimit ? "purchase_limit" : "purchases";
        String query = "SELECT * FROM player_data ORDER BY " + orderBy + " DESC LIMIT ?";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setInt(1, limit);

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    topPlayers.add(new PlayerData(
                            UUID.fromString(rs.getString("uuid")),
                            rs.getString("name"),
                            rs.getInt("purchases"),
                            rs.getInt("purchase_limit"),
                            rs.getLong("last_purchase"),
                            rs.getInt("multiplicador")));
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao buscar top jogadores: " + e.getMessage());
        }

        return topPlayers;
    }

    public void close() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                plugin.getLogger().info("Conexão com o banco de dados fechada!");
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao fechar conexão com o banco de dados: " + e.getMessage());
        }
    }

    public Connection getConnection() {
        return connection;
    }
}
