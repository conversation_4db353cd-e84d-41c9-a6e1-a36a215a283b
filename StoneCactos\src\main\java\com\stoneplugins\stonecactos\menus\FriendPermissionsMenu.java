package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.data.FriendPermissions;
import com.stoneplugins.stonecactos.managers.FriendsManager;
import com.stoneplugins.stonecactos.utils.ItemBuilder;
import com.stoneplugins.stonecactos.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.UUID;

public class FriendPermissionsMenu {

    private final StoneCactos plugin;
    private final FriendsManager friendsManager;
    private final MessageUtils messageUtils;

    public FriendPermissionsMenu(StoneCactos plugin) {
        this.plugin = plugin;
        this.friendsManager = plugin.getFriendsManager();
        this.messageUtils = plugin.getMessageUtils();
    }

    public void openMenu(Player player, UUID friendUuid) {
        CactusGenerator generator = plugin.getGeneratorManager().getGenerator(player.getUniqueId());
        if (generator == null) {
            messageUtils.sendMessage(player, "&cVocê não possui um gerador.");
            return;
        }

        FriendPermissions permissions = friendsManager.getFriendPermissions(generator, friendUuid);
        if (permissions == null) {
            messageUtils.sendMessage(player, "&cEste jogador não é seu amigo.");
            return;
        }

        Inventory menu = Bukkit.createInventory(null, 27, "Permissões de Amigo");

        // Adicionar itens ao menu
        menu.setItem(11,
                createPermissionItem(Material.DIAMOND_PICKAXE, "Adicionar Torres", permissions.canAddTowers()));
        menu.setItem(13, createPermissionItem(Material.TNT, "Remover Torres", permissions.canRemoveTowers()));
        menu.setItem(15, createPermissionItem(Material.EMERALD, "Vender Cactos", permissions.canSellCactus()));

        player.openInventory(menu);
    }

    private ItemStack createPermissionItem(Material material, String name, boolean enabled) {
        ItemBuilder builder = new ItemBuilder(material);
        builder.setDisplayName("&e" + name);
        builder.addLoreLine("&7Status: " + (enabled ? "&aAtivado" : "&cDesativado"));
        builder.addLoreLine("&7Clique para alterar.");
        return builder.build();
    }
}
