package com.stoneplugins.spawnershop;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class SpawnerShopBasic extends JavaPlugin implements Listener {
    
    private Map<String, Integer> spawnerPrices;
    private Map<String, Integer> playerPurchases;
    
    @Override
    public void onEnable() {
        getServer().getPluginManager().registerEvents(this, this);
        
        spawnerPrices = new HashMap<>();
        playerPurchases = new HashMap<>();
        
        // Configurar preços dos spawners
        spawnerPrices.put("cow", 30000);
        spawnerPrices.put("pig", 25000);
        spawnerPrices.put("chicken", 20000);
        spawnerPrices.put("sheep", 22000);
        spawnerPrices.put("zombie", 60000);
        spawnerPrices.put("skeleton", 65000);
        
        getLogger().info("SpawnerShop habilitado com sucesso!");
        getLogger().info("Use /spawners para abrir a loja!");
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Este comando só pode ser usado por jogadores!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (command.getName().equalsIgnoreCase("spawners")) {
            if (args.length == 0) {
                openSpawnerShop(player);
                return true;
            }
            
            if (args[0].equalsIgnoreCase("info")) {
                int purchases = playerPurchases.getOrDefault(player.getName(), 0);
                player.sendMessage(ChatColor.YELLOW + "=== SpawnerShop Info ===");
                player.sendMessage(ChatColor.WHITE + "Compras realizadas: " + ChatColor.GREEN + purchases);
                player.sendMessage(ChatColor.WHITE + "Limite de compras: " + ChatColor.GREEN + "64");
                return true;
            }
        }
        
        return false;
    }
    
    private void openSpawnerShop(Player player) {
        Inventory inv = Bukkit.createInventory(null, 54, ChatColor.translateAlternateColorCodes('&', "&eCategoria: Compra de Spawners"));
        
        // Preencher com vidro cinza
        ItemStack glass = new ItemStack(Material.STAINED_GLASS_PANE, 1, (short) 7);
        ItemMeta glassMeta = glass.getItemMeta();
        glassMeta.setDisplayName(" ");
        glass.setItemMeta(glassMeta);
        
        for (int i = 0; i < 54; i++) {
            inv.setItem(i, glass);
        }
        
        // Adicionar spawners
        int[] slots = {19, 20, 21, 22, 23, 24};
        int slotIndex = 0;
        
        for (Map.Entry<String, Integer> entry : spawnerPrices.entrySet()) {
            if (slotIndex >= slots.length) break;
            
            String spawnerType = entry.getKey();
            int price = entry.getValue();
            
            ItemStack item = new ItemStack(Material.MOB_SPAWNER);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(ChatColor.YELLOW + "Spawner de " + spawnerType.substring(0, 1).toUpperCase() + spawnerType.substring(1));
            meta.setLore(Arrays.asList(
                "",
                ChatColor.WHITE + " ➪ Preço: " + ChatColor.GRAY + formatMoney(price),
                ChatColor.WHITE + " ➪ Você tem o grupo: " + ChatColor.AQUA + "Padrão",
                ChatColor.WHITE + " ➪ Com isso você tem " + ChatColor.AQUA + "+0%" + ChatColor.WHITE + " de desconto.",
                "",
                ChatColor.GREEN + "Clique aqui para finalizar a compra."
            ));
            item.setItemMeta(meta);
            
            inv.setItem(slots[slotIndex], item);
            slotIndex++;
        }
        
        // Adicionar item de perfil
        ItemStack profile = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        ItemMeta profileMeta = profile.getItemMeta();
        profileMeta.setDisplayName(ChatColor.YELLOW + "Seu usuário");
        int purchases = playerPurchases.getOrDefault(player.getName(), 0);
        profileMeta.setLore(Arrays.asList(
            ChatColor.WHITE + "➪ Seu limite de compra: " + ChatColor.GRAY + "64",
            ChatColor.WHITE + "➪ Quantidade de compras: " + ChatColor.GRAY + purchases,
            ChatColor.WHITE + "➪ Descontos ativos: " + ChatColor.GRAY + "0%",
            "",
            ChatColor.AQUA + "Informações:",
            ChatColor.GRAY + "Após adquirir seu spawner desejado você",
            ChatColor.GRAY + "pode ir até sua plot-me, e começar a farmar",
            ChatColor.GRAY + "com isso você consegue ganhar dinheiro",
            ChatColor.GRAY + "no servidor e se tornar o mais rico."
        ));
        profile.setItemMeta(profileMeta);
        inv.setItem(3, profile);
        
        player.openInventory(inv);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();
        
        if (title.equals(ChatColor.translateAlternateColorCodes('&', "&eCategoria: Compra de Spawners"))) {
            event.setCancelled(true);
            
            if (event.getCurrentItem() == null || event.getCurrentItem().getType() == Material.STAINED_GLASS_PANE) {
                return;
            }
            
            if (event.getCurrentItem().getType() == Material.MOB_SPAWNER) {
                // Simular compra de spawner
                String spawnerName = ChatColor.stripColor(event.getCurrentItem().getItemMeta().getDisplayName());
                
                player.sendMessage(ChatColor.GREEN + "GG! Sua compra feita com sucesso.");
                player.sendMessage(ChatColor.GREEN + spawnerName + " adicionado ao seu inventário!");
                
                // Dar o spawner
                ItemStack spawner = new ItemStack(Material.MOB_SPAWNER);
                ItemMeta spawnerMeta = spawner.getItemMeta();
                spawnerMeta.setDisplayName(ChatColor.YELLOW + spawnerName);
                spawner.setItemMeta(spawnerMeta);
                
                if (player.getInventory().firstEmpty() != -1) {
                    player.getInventory().addItem(spawner);
                } else {
                    player.getWorld().dropItem(player.getLocation(), spawner);
                    player.sendMessage(ChatColor.YELLOW + "Seu inventário estava cheio! O spawner foi dropado no chão.");
                }
                
                // Registrar compra
                int currentPurchases = playerPurchases.getOrDefault(player.getName(), 0);
                playerPurchases.put(player.getName(), currentPurchases + 1);
                
                player.closeInventory();
            }
        }
    }
    
    private String formatMoney(double amount) {
        if (amount >= 1000000) {
            return String.format("%.1fM", amount / 1000000);
        } else if (amount >= 1000) {
            return String.format("%.1fK", amount / 1000);
        } else {
            return String.format("%.0f", amount);
        }
    }
}
