confirm:
  expired_confirm: $2Confirmation has expired, please run the command again!
  failed_confirm: $2You have no pending actions to confirm!
  requires_confirm: '$2Are you sure you wish to execute: $1%s$2?&-$2This cannot be
    undone! If you are sure: $1/plot confirm'
move:
  move_success: $4Successfully moved plot.
  copy_success: $4Successfully copied plot.
  requires_unowned: $2The location specified is already occupied.
set:
  set_attribute: $4Successfully set %s0 set to %s1
web:
  generating_link: $1Processing plot...
  generating_link_failed: $2Failed to generate download link!
  save_failed: $2Failed to save
  load_null: $2Please use $4/plot load $2to get a list of schematics
  load_failed: $2Failed to load schematic
  load_list: '$2To load a schematic, use $1/plot load #'
  save_success: $1Successfully saved!
compass:
  compass_target: $4Successfully targeted plot with compass
cluster:
  cluster_available_args: '$1The following sub commands are available: $4list$2, $4create$2,
    $4delete$2, $4resize$2, $4invite$2, $4kick$2, $4leave$2, $4members$2, $4info$2,
    $4tp$2, $4sethome'
  cluster_list_heading: $2There are $1%s$2 clusters in this world
  cluster_list_element: $2 - $1%s&-
  cluster_intersection: '$2The proposed area overlaps with: %s0'
  cluster_outside: '$2The proposed area is outside the plot area: %s0'
  cluster_added: $4Successfully created the cluster.
  cluster_deleted: $4Successfully deleted the cluster.
  cluster_resized: $4Successfully resized the cluster.
  cluster_added_user: $4Successfully added user to the cluster.
  cannot_kick_player: $2You cannot kick that player
  cluster_invited: '$1You have been invited to the following cluster: $2%s'
  cluster_removed: '$1You have been removed from cluster: $2%s'
  cluster_kicked_user: $4Successfully kicked the user
  invalid_cluster: '$1Invalid cluster name: $2%s'
  cluster_not_added: $2That player was not added to the plot cluster
  cluster_cannot_leave: $1You must delete or transfer ownership before leaving
  cluster_added_helper: $4Successfully added a helper to the cluster
  cluster_removed_helper: $4Successfully removed a helper from the cluster
  cluster_regenerated: $4Successfully started cluster regeneration
  cluster_teleporting: $4Teleporting...
  cluster_info: '$1Current cluster: $2%id%&-$1Name: $2%name%&-$1Owner: $2%owner%&-$1Size:
    $2%size%&-$1Rights: $2%rights%'
border:
  border: $2You are outside the current map border
unclaim:
  unclaim_success: $4You successfully unclaimed the plot.
  unclaim_failed: $2Could not unclaim the plot
worldedit masks:
  worldedit_delayed: $2Please wait while we process your WorldEdit action...
  worldedit_run: '$2Apologies for the delay. Now executing: %s'
  require_selection_in_mask: $2%s of your selection is not within your plot mask. You can only make edits within your plot.
  worldedit_volume: $2You cannot select a volume of %current%. The maximum volume you can modify is %max%.
  worldedit_iterations: $2You cannot iterate %current% times. The maximum number of iterations allowed is %max%.
  worldedit_unsafe: $2Access to that command has been blocked
  worldedit_bypass: $2&oTo bypass your restrictions use $4/plot wea
  worldedit_bypassed: $2Currently bypassing WorldEdit restriction.
  worldedit_unmasked: $1Your WorldEdit is now unrestricted.
  worldedit_restricted: $1Your WorldEdit is now restricted.
gamemode:
  gamemode_was_bypassed: $1You bypassed the GameMode ($2{gamemode}$1) $1set for $2{plot}
height limit:
  height_limit: $1This plot area has a height limit of $2{limit}
records:
  record_play: $2%player $2started playing record $1%name
  notify_enter: $2%player $2entered your plot ($1%plot$2)
  notify_leave: $2%player $2left your plot ($1%plot$2)
swap:
  swap_overlap: $2The proposed areas are not allowed to overlap
  swap_dimensions: $2The proposed areas must have comparable dimensions
  swap_syntax: $2/plot swap <id>
  swap_success: $4Successfully swapped plots
  started_swap: $2Started plot swap task. You will be notified when it finishes
comment:
  inbox_notification: '%s unread messages. Use /plot inbox'
  not_valid_inbox_index: $2No comment at index %s
  inbox_item: $2 - $4%s
  comment_syntax: $2Use /plot comment [X;Z] <%s> <comment>
  invalid_inbox: '$2That is not a valid inbox.&-$1Accepted values: %s'
  no_perm_inbox: $2You do not have permission for that inbox
  no_perm_inbox_modify: $2You do not have permission to modify that inbox
  no_plot_inbox: $2You must stand in or supply a plot argument
  comment_removed: $4Successfully deleted comment/s:n$2 - '$3%s$2'
  comment_added: $4A comment has been left
  comment_header: $2&m---------&r $1Comments $2&m---------&r
  inbox_empty: $2No comments
console:
  not_console: $2For safety reasons, this command can only be executed by console.
  is_console: $2This command can only be executed by a player.
inventory:
  inventory_usage: '&cUsage: &6{usage}'
  inventory_desc: '&cDescription: &6{desc}'
  inventory_category: '&cCategory: &6{category}'
clipboard:
  clipboard_set: $2The current plot is now copied to your clipboard, use $1/plot paste$2 to paste it
  pasted: $4The plot selection was successfully pasted. It has been cleared from your clipboard.
  paste_failed: '$2Failed to paste the selection. Reason: $2%s'
  no_clipboard: $2You don't have a selection in your clipboard
  clipboard_info: '$2Current Selection - Plot ID: $1%id$2, Width: $1%width$2, Total
    Blocks: $1%total$2'
toggle:
  toggle_enabled: '$2Enabled setting: %s'
  toggle_disabled: '$2Disabled setting: %s'
blocked command:
  command_blocked: $2That command is not allowed in this plot
done:
  done_already_done: $2This plot is already marked as done
  done_not_done: $2This plot is not marked as done.
  done_insufficient_complexity: $2This plot is too simple. Please add more detail before using this command.
  done_success: $1Successfully marked this plot as done.
  done_removed: $1You may now continue building in this plot.
ratings:
  ratings_purged: $2Purged ratings for this plot
  rating_not_valid: $2You need to specify a number between 1 and 10
  rating_already_exists: $2You have already rated plot $2%s
  rating_applied: $4You successfully rated plot $2%s
  rating_not_your_own: $2You cannot rate your own plot
  rating_not_done: $2You can only rate finished plots.
  rating_not_owned: $2You cannot rate a plot that is not claimed by anyone
tutorial:
  rate_this: $2Rate this plot!
  comment_this: '$2Leave some feedback on this plot: %s'
economy:
  econ_disabled: $2Economy is not enabled
  cannot_afford_plot: $2You cannot afford to buy this plot. It costs $1%s
  not_for_sale: $2This plot is not for sale
  cannot_buy_own: $2You cannot buy your own plot
  plot_sold: $4Your plot; $1%s0$4, has been sold to $1%s1$4 for $1$%s2
  cannot_afford_merge: $2You cannot afford to merge the plots. It costs $1%s
  added_balance: $1%s $2has been added to your balance
  removed_balance: $1%s $2has been taken from your balance
  removed_granted_plot: $2You used %s plot grant(s), you've got $1%s $2left
setup:
  setup_init: '$1Usage: $2/plot setup <value>'
  setup_step: '$3[$1Step %s0$3] $1%s1 $2- $1Expecting: $2%s2 $1Default: $2%s3'
  setup_invalid_arg: '$2%s0 is not a valid argument for step %s1. To cancel setup
    use: $1/plot setup cancel'
  setup_valid_arg: $2Value $1%s0 $2set to %s1
  setup_finished: $4You should have been teleported to the created world. Otherwise you will need to set the generator manually using the bukkit.yml or your chosen world management plugin.
  setup_world_taken: $2%s is already a world
  setup_missing_world: $2You need to specify a world name ($1/plot setup &l<world>$1 <generator>$2)&-$1Additional commands:&-$2 - $1/plot setup <value>&-$2 - $1/plot setup back&-$2 - $1/plot setup cancel
  setup_missing_generator: $2You need to specify a generator ($1/plot setup <world> &l<generator>&r$2)&-$1Additional commands:&-$2 - $1/plot setup <value>&-$2 - $1/plot setup back&-$2 - $1/plot setup cancel
  setup_invalid_generator: '$2Invalid generator. Possible options: %s'
schematics:
  schematic_too_large: $2The plot is too large for this action!
  schematic_missing_arg: '$2You need to specify an argument. Possible values: $1test
    <name>$2 , $1save$2 , $1paste $2, $1exportall'
  schematic_invalid: '$2That is not a valid schematic. Reason: $2%s'
  schematic_valid: $2That is a valid schematic
  schematic_paste_failed: $2Failed to paste the schematic
  schematic_paste_success: $4The schematic pasted successfully
titles:
  title_entered_plot: '$1Plot: %world%;%x%;%z%'
  title_entered_plot_sub: $4Owned by %s
  prefix_greeting: '$1%id%$2> '
  prefix_farewell: '$1%id%$2> '
core:
  task_start: Starting task...
  prefix: $3[$1P2$3] $2
  enabled: $1%s0 is now enabled
reload:
  reloaded_configs: $1Translations and world settings have been reloaded
  reload_failed: $2Failed to reload file configurations
desc:
  desc_set: $2Plot description set
  desc_unset: $2Plot description unset
  missing_desc: $2You need to specify a description
alias:
  alias_set_to: $2Plot alias set to $1%alias%
  alias_removed: $2Plot alias removed
  missing_alias: $2You need to specify an alias
  alias_too_long: $2The alias must be < 50 characters in length
  alias_is_taken: $2That alias is already taken
position:
  missing_position: '$2You need to specify a position. Possible values: $1none'
  position_set: $1Home position set to your current location
  position_unset: $1Home position reset to the default location
  home_argument: $2Use /plot set home [none]
  invalid_position: $2That is not a valid position value
cap:
  entity_cap: $2You are not allowed to spawn more mobs
time:
  time_format: $1%hours%, %min%, %sec%
permission:
  no_schematic_permission: $2You don't have the permission required to use schematic $1%s
  no_permission: '$2You are lacking the permission node: $1%s'
  no_permission_event: '$2You are lacking the permission node: $1%s'
  no_plot_perms: $2You must be the plot owner to perform this action
  cant_claim_more_plots: $2You can't claim more plots.
  cant_claim_more_clusters: $2You can't claim more clusters.
  cant_transfer_more_plots: $2You can't send more plots to that user
  cant_claim_more_plots_num: $2You can't claim more than $1%s $2plots at once
  you_be_denied: $2You are not allowed to enter this plot
  merge_request_confirm: Merge request from %s
merge:
  merge_not_valid: $2This merge request is no longer valid.
  merge_accepted: $2The merge request has been accepted
  success_merge: $2Plots have been merged!
  merge_requested: $2Successfully sent a merge request
  no_perm_merge: '$2You are not the owner of the plot: $1%plot%'
  no_available_automerge: $2You do not own any adjacent plots in the specified direction or are not allowed to merge to the required size.
  unlink_required: $2An unlink is required to do this.
  unlink_impossible: $2You can only unlink a mega-plot
  unlink_success: $2Successfully unlinked plots.
commandconfig:
  not_valid_subcommand: $2That is not a valid subcommand
  did_you_mean: '$2Did you mean: $1%s'
  name_little: $2%s0 name is too short, $1%s1$2<$1%s3
  no_commands: $2I'm sorry, but you're not permitted to use any subcommands.
  subcommand_set_options_header: '$2Possible Values: '
  command_syntax: '$1Usage: $2%s'
  flag_tutorial_usage: '$1Have an admin set the flag: $2%s'
errors:
  invalid_player_wait: '$2Player not found: $1%s$2, fetching it. Try again soon.'
  invalid_player: '$2Player not found: $1%s$2.'
  invalid_player_offline: '$2The player must be online: $1%s.'
  invalid_command_flag: '$2Invalid command flag: %s0'
  error: '$2An error occurred: %s'
  command_went_wrong: $2Something went wrong when executing that command...
  no_free_plots: $2There are no free plots available
  not_in_plot: $2You're not in a plot
  not_loaded: $2The plot could not be loaded
  not_in_cluster: $2You must be within a plot cluster to perform that action
  not_in_plot_world: $2You're not in a plot area
  plotworld_incompatible: $2The two worlds must be compatible
  not_valid_world: $2That is not a valid world (case sensitive)
  not_valid_plot_world: $2That is not a valid plot area (case sensitive)
  no_plots: $2You don't have any plots
  wait_for_timer: $2A setblock timer is bound to either the current plot or you. Please wait for it to finish
paste:
  debug_report_created: '$1Uploaded a full debug to: $1%url%'
purge:
  purge_success: $4Successfully purged %s plots
trim:
  trim_in_progress: A world trim task is already in progress!
  not_valid_hybrid_plot_world: The hybrid plot manager is required to perform this action
block list:
  block_list_separater: '$1,$2 '
biome:
  need_biome: $2You need to specify a valid biome.
  biome_set_to: $2Plot biome set to $2
teleport:
  teleported_to_plot: $1You have been teleported
  teleported_to_road: $2You got teleported to the road
  teleport_in_seconds: $1Teleporting in %s seconds. Do not move...
  teleport_failed: $2Teleportation cancelled due to movement or damage
set block:
  set_block_action_finished: $1The last setblock action is now finished.
unsafe:
  debugallowunsafe_on: $2Unsafe actions allowed
  debugallowunsafe_off: $2Unsafe actions disabled
debug:
  debug_header: $1Debug Information&-
  debug_section: $2>> $1&l%val%
  debug_line: $2>> $1%var%$2:$1 %val%&-
  requires_unmerged: $2The plot cannot be merged
invalid:
  not_valid_data: $2That's not a valid data id.
  not_valid_block: '$2That''s not a valid block: %s'
  not_allowed_block: '$2That block is not allowed: %s'
  not_valid_number: '$2That''s not a valid number within the range: %s'
  not_valid_plot_id: $2That's not a valid plot id.
  plot_id_form: '$2The plot id must be in the form: $1X;Y $2e.g. $1-5;7'
  not_your_plot: $2That is not your plot.
  no_such_plot: $2There is no such plot
  player_has_not_been_on: $2That player hasn't been in the plotworld
  found_no_plots: $2Found no plots with your search query
  found_no_plots_for_player: '$2No plots found for player: %s'
camera:
  camera_started: $2You have entered camera mode for plot $1%s
  camera_stopped: $2You are no longer in camera mode
need:
  need_plot_number: $2You've got to specify a plot number or alias
  need_block: $2You've got to specify a block
  need_plot_id: $2You've got to specify a plot id.
  need_plot_world: $2You've got to specify a plot area.
  need_user: $2You need to specify a username
near:
  plot_near: '$1Players: %s0'
info:
  none: None
  now: Now
  never: Never
  unknown: Unknown
  everyone: Everyone
  plot_unowned: $2The current plot must have an owner to perform this action
  plot_info_unclaimed: $2Plot $1%s$2 is not yet claimed
  plot_info_header: $3&m---------&r $1INFO $3&m---------
  plot_info: '$1ID: $2%id%$1&-$1Alias: $2%alias%$1&-$1Owner: $2%owner%$1&-$1Biome:
    $2%biome%$1&-$1Can Build: $2%build%$1&-$1Rating: $2%rating%&-$1Seen: $2%seen%&-$1Trusted:
    $2%trusted%$1&-$1Members: $2%members%$1&-$1Denied: $2%denied%$1&-$1Flags: $2%flags%'
  plot_info_footer: $3&m---------&r $1INFO $3&m---------
  plot_info_trusted: $1Trusted:$2 %trusted%
  plot_info_members: $1Members:$2 %members%
  plot_info_denied: $1Denied:$2 %denied%
  plot_info_flags: $1Flags:$2 %flags%
  plot_info_biome: $1Biome:$2 %biome%
  plot_info_rating: $1Rating:$2 %rating%
  plot_info_owner: $1Owner:$2 %owner%
  plot_info_id: $1ID:$2 %id%
  plot_info_alias: $1Alias:$2 %alias%
  plot_info_size: $1Size:$2 %size%
  plot_info_seen: $1Seen:$2 %seen%
  plot_user_list: ' $1%user%$2,'
  plot_flag_list: $1%s0:%s1$2
  info_syntax_console: $2/plot info X;Y
working:
  generating_component: $1Started generating component from your settings
  clearing_plot: $2Clearing plot async.
  clearing_done: $4Clear completed! Took %sms.
  deleting_done: $4Delete completed! Took %sms.
  plot_not_claimed: $2Plot not claimed
  plot_is_claimed: $2This plot is already claimed
  claimed: $4You successfully claimed the plot
list:
  comment_list_header_paged: $2(Page $1%cur$2/$1%max$2) $1List of %amount% comments
  clickable: ' (interactive)'
  area_list_header_paged: $2(Page $1%cur$2/$1%max$2) $1List of %amount% areas
  plot_list_header_paged: $2(Page $1%cur$2/$1%max$2) $1List of %amount% plots
  plot_list_header: $1List of %word% plots
  plot_list_item: $2>> $1%id$2:$1%world $2- $1%owner
  plot_list_item_ordered: $2[$1%in$2] >> $1%id$2:$1%world $2- $1%owner
  plot_list_footer: $2>> $1%word% a total of $2%num% $1claimed %plot%.
left:
  left_plot: $2You left a plot
chat:
  plot_chat_spy_format: '$2[$1Plot Spy$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
  plot_chat_format: '$2[$1Plot Chat$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
  plot_chat_forced: $2This world forces everyone to use plot chat.
  plot_chat_on: $4Plot chat enabled.
  plot_chat_off: $4Plot chat disabled.
deny:
  denied_removed: $4You successfully undenied the player from this plot
  denied_added: $4You successfully denied the player from this plot
  denied_need_argument: $2Arguments are missing. $1/plot denied add <name> $2or $1/plot denied remove <name>
  was_not_denied: $2That player was not denied on this plot
  you_got_denied: $4You are denied from the plot you were previously on, and got teleported to spawn
kick:
  you_got_kicked: $4You got kicked!
rain:
  need_on_off: '$2You need to specify a value. Possible values: $1on$2, $1off'
  setting_updated: $4You successfully updated the setting
flag:
  flag_key: '$2Key: %s'
  flag_type: '$2Type: %s'
  flag_desc: '$2Desc: %s'
  not_valid_flag: $2That is not a valid flag
  not_valid_flag_suggested: '$2That is not a valid flag. Did you mean: $1%s'
  not_valid_value: $2Flag values must be alphanumerical
  flag_not_in_plot: $2The plot does not have that flag
  flag_not_removed: $2The flag could not be removed
  flag_not_added: $2The flag could not be added
  flag_removed: $4Successfully removed flag
  flag_added: $4Successfully added flag
trusted:
  trusted_added: $4You successfully trusted a user to the plot
  trusted_removed: $4You successfully removed a trusted user from the plot
  was_not_added: $2That player was not trusted on this plot
  plot_removed_user: $1Plot %s of which you were added to has been deleted due to owner inactivity
member:
  removed_players: $2Removed %s players from this plot.
  already_owner: '$2That user is already the plot owner: %s0'
  already_added: '$2That user is already added to that category: %s0'
  member_added: $4That user can now build while the plot owner is online
  member_removed: $1You successfully removed a user from the plot
  member_was_not_added: $2That player was not added as a user on this plot
  plot_max_members: $2You are not allowed to add any more players to this plot
owner:
  set_owner: $4You successfully set the plot owner
  set_owner_cancelled: $2The setowner action was cancelled
  now_owner: $4You are now owner of plot %s
signs:
  owner_sign_line_1: '$1ID: $1%id%'
  owner_sign_line_2: '$1Owner:'
  owner_sign_line_3: $2%plr%
  owner_sign_line_4: $3Claimed
help:
  help_header: $3&m---------&r $1Plot² Help $3&m---------
  help_page_header: '$1Category: $2%category%$2,$1 Page: $2%current%$3/$2%max%$2'
  help_footer: $3&m---------&r $1Plot² Help $3&m---------
  help_info_item: $1/plot help %category% $3- $2%category_desc%
  help_item: $1%usage% [%alias%]&- $3- $2%desc%&-
  direction: '$1Current direction: %dir%'
grants:
  granted_plots: '$1Result: $2%s $1grants left'
  granted_plot: $1You granted %s0 plot to $2%s1
  granted_plot_failed: '$1Grant failed: $2%s'
'-':
  custom_string: '-'
