package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.List;

public class BoostersMenu extends BaseMenu {

    private final CactusGenerator generator;
    private int currentPage = 0;
    private final int itemsPerPage = 7; // Slots configurados no menu

    public BoostersMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("boosters"));
        int size = plugin.getConfigManager().getMenuSize("boosters");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro (se configurado)
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.boosters.glassPaneSlots");
        if (glassSlots != null) {
            fillGlassSlots(glassSlots);
        }

        // Configurar boosters
        setupBoosters();

        // Configurar navegação
        setupNavigation();

        // Botão de voltar
        setupBackButton();

        // Mostrar booster ativo
        setupActiveBooster();
    }

    private void setupBoosters() {
        List<String> availableBoosters = plugin.getBoosterManager().getAvailableBoosters();
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.boosters.slots");

        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        int startIndex = currentPage * itemsPerPage;

        for (int i = 0; i < slots.length && i < itemsPerPage; i++) {
            int boosterIndex = startIndex + i;

            if (boosterIndex >= availableBoosters.size()) {
                break;
            }

            String boosterId = availableBoosters.get(boosterIndex);
            int slot = Integer.parseInt(slots[i].trim());

            // Criar item do booster
            ItemStack boosterItem = createBoosterItem(boosterId);
            setItem(slot, boosterItem);
        }
    }

    private ItemStack createBoosterItem(String boosterId) {
        // Obter informações do booster
        String name = plugin.getConfigManager().getBoosterName(boosterId);
        List<String> lore = plugin.getConfigManager().getBoosterLore(boosterId);
        String materialConfig = plugin.getConfigManager().getBoosterItem(boosterId);
        double price = plugin.getConfigManager().getBoosterPrice(boosterId);
        double multiplier = plugin.getConfigManager().getBoosterMultiplier(boosterId);
        int time = plugin.getConfigManager().getBoosterTime(boosterId);

        // Substituir placeholders
        lore = replacePlaceholders(lore,
                "%price%", formatMoney(price),
                "%multiplier%", String.valueOf(multiplier),
                "%time%", plugin.getBoosterManager().getFormattedTime(time));

        // Verificar se o jogador pode comprar
        // boolean canAfford = plugin.getEconomyManager().hasBalance(player, price);
        boolean canAfford = true; // Temporário: sempre permitir compra
        boolean hasActiveBooster = generator.hasActiveBooster();

        // Adicionar informações de status
        if (hasActiveBooster) {
            lore.add("");
            lore.add("§cVocê já tem um booster ativo!");
        } else if (!canAfford) {
            lore.add("");
            lore.add("§cVocê não tem dinheiro suficiente!");
        } else {
            lore.add("");
            lore.add("§eClique para comprar e ativar!");
        }

        // Parse material
        Material material;
        short data = 0;

        if (materialConfig.contains(":")) {
            String[] parts = materialConfig.split(":");
            material = Material.valueOf(parts[0]);
            data = Short.parseShort(parts[1]);
        } else {
            material = Material.valueOf(materialConfig);
        }

        ItemStack item = createItem(material, data, name, lore);

        // Se não pode comprar, deixar acinzentado
        if (!canAfford || hasActiveBooster) {
            // Criar versão acinzentada
            item = createItem(Material.valueOf("STAINED_GLASS_PANE"), (short) 8, name, lore);
        }

        return item;
    }

    private void setupActiveBooster() {
        if (generator.hasActiveBooster()) {
            // Mostrar booster ativo no slot 4 (centro superior)
            String boosterType = generator.getActiveBooster().getBoosterType();
            double multiplier = generator.getActiveBooster().getMultiplier();
            String timeRemaining = generator.getActiveBooster().getTimeRemainingFormatted();

            ItemStack activeItem = createItem(Material.valueOf("ENCHANTED_BOOK"),
                    "§aBooster Ativo",
                    "§fTipo: §e" + boosterType,
                    "§fMultiplicador: §a" + multiplier + "x",
                    "§fTempo restante: §b" + timeRemaining,
                    "",
                    "§7Este booster está ativo no gerador");

            setItem(4, activeItem);
        }
    }

    private void setupNavigation() {
        List<String> availableBoosters = plugin.getBoosterManager().getAvailableBoosters();
        int totalPages = (int) Math.ceil((double) availableBoosters.size() / itemsPerPage);

        // Botão página anterior
        if (currentPage > 0) {
            int prevSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.previusPage", 27);
            ItemStack prevButton = createItem(Material.ARROW, "§ePágina Anterior",
                    "§7Clique para ir para a página " + currentPage);
            setItem(prevSlot, prevButton);
        }

        // Botão próxima página
        if (currentPage < totalPages - 1) {
            int nextSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.nextPage", 35);
            ItemStack nextButton = createItem(Material.ARROW, "§ePróxima Página",
                    "§7Clique para ir para a página " + (currentPage + 2));
            setItem(nextSlot, nextButton);
        }
    }

    @Override
    protected void setupBackButton() {
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.backToMainInventory.slot", 31);
        ItemStack backButton = createItem(Material.ARROW, "§cVoltar", "§7Clique para voltar ao menu principal");
        setItem(backSlot, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        // Slots de navegação
        int prevSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.previusPage", 27);
        int nextSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.nextPage", 35);
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.boosters.backToMainInventory.slot", 31);

        if (slot == prevSlot) {
            handlePreviousPage();
        } else if (slot == nextSlot) {
            handleNextPage();
        } else if (slot == backSlot) {
            handleBackClick();
        } else {
            // Verificar se é um slot de booster
            handleBoosterClick(slot);
        }
    }

    private void handlePreviousPage() {
        if (currentPage > 0) {
            currentPage--;
            update();
        }
    }

    private void handleNextPage() {
        List<String> availableBoosters = plugin.getBoosterManager().getAvailableBoosters();
        int totalPages = (int) Math.ceil((double) availableBoosters.size() / itemsPerPage);

        if (currentPage < totalPages - 1) {
            currentPage++;
            update();
        }
    }

    @Override
    protected void handleBackClick() {
        // Fechar inventário primeiro
        player.closeInventory();

        // Abrir menu principal após um delay para evitar conflitos
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getMenuManager().openMainMenu(player, generator);
        }, 2L);
    }

    private void handleBoosterClick(int slot) {
        // Verificar se o slot corresponde a um booster
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.boosters.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        int boosterIndex = -1;

        for (int i = 0; i < slots.length; i++) {
            if (Integer.parseInt(slots[i].trim()) == slot) {
                boosterIndex = currentPage * itemsPerPage + i;
                break;
            }
        }

        if (boosterIndex == -1) {
            return;
        }

        List<String> availableBoosters = plugin.getBoosterManager().getAvailableBoosters();
        if (boosterIndex >= availableBoosters.size()) {
            return;
        }

        String boosterId = availableBoosters.get(boosterIndex);

        // Verificar permissão
        if (!generator.isOwner(player) && !generator.hasPermission(player.getUniqueId(), "manage")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Tentar comprar e ativar o booster
        if (plugin.getBoosterManager().buyAndActivateBooster(player, generator, boosterId)) {
            // Atualizar menu
            update();
        }
    }

    /**
     * Define a página atual
     */
    public void setPage(int page) {
        this.currentPage = Math.max(0, page);
    }

    /**
     * Obtém a página atual
     */
    public int getCurrentPage() {
        return currentPage;
    }
}
