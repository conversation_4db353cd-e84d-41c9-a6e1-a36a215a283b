name: StoneTags
version: '1.0'
description: <PERSON>ste<PERSON> de tags avançado similar ao Hypixel
author: Stone Plugins
main: com.stoneplugins.stonetags.StoneTags
api-version: 1.8
softdepend: [PlaceholderAPI, Vault, PermissionsEx, LuckPerms, TAB]

commands:
  tag:
    description: Abre o menu de tags e sufixos
    usage: /tag
    aliases: []

  tagset:
    description: Comando administrativo para definir tag de jogador
    usage: /tagset <jogador> <tag>
    permission: stonetags.admin.tagset

  tagremove:
    description: Comando administrativo para trocar tag de jogador
    usage: /tagremove <jogador> <tag_atual> <tag_nova>
    permission: stonetags.admin.tagremove

  stonetags:
    description: Comando administrativo principal
    usage: /stonetags <reload|debug|help>
    permission: stonetags.admin

permissions:
  # Permissões administrativas
  stonetags.admin:
    description: Permissão de administrador completa
    default: op
    children:
      - stonetags.admin.tagset
      - stonetags.admin.tagremove
      - stonetags.admin.reload
      - stonetags.admin.debug

  stonetags.admin.tagset:
    description: Permissão para definir tags de outros jogadores
    default: op

  stonetags.admin.tagremove:
    description: Permissão para remover e trocar tags de outros jogadores
    default: op

  stonetags.admin.reload:
    description: Permissão para recarregar o plugin
    default: op

  stonetags.admin.debug:
    description: Permissão para usar comandos de debug
    default: op

  # Permissões de uso
  stonetags.use:
    description: Permissão para usar tags
    default: true

  stonetags.suffix.use:
    description: Permissão para usar sufixos
    default: true

  stonetags.bypass.date:
    description: Bypass para datas de liberação
    default: op

  stonetags.invisible:
    description: Permissão para usar tags invisíveis
    default: false
