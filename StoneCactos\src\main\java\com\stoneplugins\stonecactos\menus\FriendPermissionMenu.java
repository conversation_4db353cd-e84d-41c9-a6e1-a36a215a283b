package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.data.FriendPermissions;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.UUID;

public class FriendPermissionMenu extends BaseMenu {

    private final CactusGenerator generator;
    private final UUID friendUuid;

    public FriendPermissionMenu(StoneCactos plugin, Player player, CactusGenerator generator, UUID friendUuid) {
        super(plugin, player);
        this.generator = generator;
        this.friendUuid = friendUuid;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("friendPermission"));
        int size = plugin.getConfigManager().getMenuSize("friendPermission");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro (se configurado)
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.friendPermission.glassPaneSlots");
        if (glassSlots != null) {
            fillGlassSlots(glassSlots);
        }

        // Configurar permissões
        setupPermissionItems();

        // Botão de voltar
        setupBackButton();

        // Informações do amigo
        setupFriendInfo();
    }

    private void setupPermissionItems() {
        FriendPermissions perms = plugin.getFriendsManager().getFriendPermissions(generator, friendUuid);

        // Permissão de adicionar torres
        setupAddTowerPermission(perms.canAddTowers());

        // Permissão de remover torres
        setupRemoveTowerPermission(perms.canRemoveTowers());

        // Permissão de vender cactos
        setupSellCactusPermission(perms.canSellCactus());
    }

    private void setupAddTowerPermission(boolean allowed) {
        int slot = getConfigSlot("friendPermission", "addTower");
        String name = plugin.getConfigManager().getMenuItemName("friendPermission", "addTower");

        String status = allowed ? "§aPermitido" : "§cNegado";
        String action = allowed ? "§cClique para negar" : "§aClique para permitir";

        ItemStack item = createItem(Material.CACTUS, name,
                "§fStatus: " + status,
                "",
                "§7Permite que o amigo adicione",
                "§7torres ao gerador de cactos.",
                "",
                action);

        setItem(slot, item);
    }

    private void setupRemoveTowerPermission(boolean allowed) {
        int slot = getConfigSlot("friendPermission", "removeTower");
        String name = plugin.getConfigManager().getMenuItemName("friendPermission", "removeTower");

        String status = allowed ? "§aPermitido" : "§cNegado";
        String action = allowed ? "§cClique para negar" : "§aClique para permitir";

        ItemStack item = createItem(Material.valueOf("REDSTONE"), name,
                "§fStatus: " + status,
                "",
                "§7Permite que o amigo remova",
                "§7torres do gerador de cactos.",
                "",
                action);

        setItem(slot, item);
    }

    private void setupSellCactusPermission(boolean allowed) {
        int slot = getConfigSlot("friendPermission", "sellTower");
        String name = plugin.getConfigManager().getMenuItemName("friendPermission", "sellTower");

        String status = allowed ? "§aPermitido" : "§cNegado";
        String action = allowed ? "§cClique para negar" : "§aClique para permitir";

        ItemStack item = createItem(Material.GOLD_INGOT, name,
                "§fStatus: " + status,
                "",
                "§7Permite que o amigo venda",
                "§7os cactos armazenados.",
                "",
                action);

        setItem(slot, item);
    }

    private void setupFriendInfo() {
        String friendName = plugin.getFriendsManager().getPlayerName(friendUuid);

        ItemStack friendInfo = plugin.getItemManager().createPlayerHead(friendName);
        friendInfo.getItemMeta().setDisplayName("§e" + friendName);
        friendInfo.getItemMeta().setLore(java.util.Arrays.asList(
                "§7Gerenciando permissões de:",
                "§f" + friendName,
                "",
                "§7Clique nos itens abaixo para",
                "§7alterar as permissões."));

        setItem(4, friendInfo); // Slot central superior
    }

    @Override
    protected void setupBackButton() {
        ItemStack backButton = createItem(Material.ARROW, "§cVoltar",
                "§7Clique para voltar ao menu de amigos");
        setItem(31, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        int addTowerSlot = getConfigSlot("friendPermission", "addTower");
        int removeTowerSlot = getConfigSlot("friendPermission", "removeTower");
        int sellCactusSlot = getConfigSlot("friendPermission", "sellTower");
        int backSlot = 31;

        if (slot == addTowerSlot) {
            handleAddTowerPermissionClick();
        } else if (slot == removeTowerSlot) {
            handleRemoveTowerPermissionClick();
        } else if (slot == sellCactusSlot) {
            handleSellCactusPermissionClick();
        } else if (slot == backSlot) {
            handleBackClick();
        }
    }

    private void handleAddTowerPermissionClick() {
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        boolean success = plugin.getFriendsManager().toggleFriendPermission(generator, friendUuid, "add_towers");
        if (success) {
            player.sendMessage(plugin.getConfigManager().getMessage("friendPermissionChanged"));
            update();
        }
    }

    private void handleRemoveTowerPermissionClick() {
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        boolean success = plugin.getFriendsManager().toggleFriendPermission(generator, friendUuid, "remove_towers");
        if (success) {
            player.sendMessage(plugin.getConfigManager().getMessage("friendPermissionChanged"));
            update();
        }
    }

    private void handleSellCactusPermissionClick() {
        if (!generator.isOwner(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        boolean success = plugin.getFriendsManager().toggleFriendPermission(generator, friendUuid, "sell_cactus");
        if (success) {
            player.sendMessage(plugin.getConfigManager().getMessage("friendPermissionChanged"));
            update();
        }
    }

    @Override
    protected void handleBackClick() {
        plugin.getMenuManager().openFriendsMenu(player, generator);
    }
}
