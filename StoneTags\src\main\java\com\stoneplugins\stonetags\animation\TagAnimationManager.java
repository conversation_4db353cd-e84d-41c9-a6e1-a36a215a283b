package com.stoneplugins.stonetags.animation;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import com.stoneplugins.stonetags.animation.RainbowGenerator;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Gerenciador de animações de tags
 * Funcionalidade Premium - Tags Animadas
 */
public class TagAnimationManager {

    private final StoneTags plugin;
    private final Map<UUID, Integer> animationFrames = new ConcurrentHashMap<>();
    private final Map<UUID, Tag> animatedTags = new ConcurrentHashMap<>();
    private final Map<UUID, List<String>> rainbowFrames = new ConcurrentHashMap<>();
    private BukkitRunnable animationTask;

    public TagAnimationManager(StoneTags plugin) {
        this.plugin = plugin;
        startAnimationTask();
    }

    /**
     * Inicia a task de animação
     */
    private void startAnimationTask() {
        if (animationTask != null) {
            animationTask.cancel();
        }

        animationTask = new BukkitRunnable() {
            @Override
            public void run() {
                updateAnimations();
            }
        };

        // Executar a cada 3 ticks para animação RGB suave (15 FPS)
        animationTask.runTaskTimer(plugin, 0L, 3L);
    }

    /**
     * Atualiza todas as animações ativas
     */
    private void updateAnimations() {
        for (Map.Entry<UUID, Tag> entry : animatedTags.entrySet()) {
            UUID playerId = entry.getKey();
            Tag tag = entry.getValue();

            Player player = plugin.getServer().getPlayer(playerId);
            if (player == null || !player.isOnline()) {
                removePlayerAnimation(playerId);
                continue;
            }

            // Verificar se a tag ainda é animada
            if (!tag.isAnimated()) {
                removePlayerAnimation(playerId);
                continue;
            }

            // Atualizar frame da animação
            List<String> frames = rainbowFrames.get(playerId);
            if (frames == null || frames.isEmpty()) {
                // Gerar frames rainbow se não existirem
                generateRainbowFramesForPlayer(playerId, tag);
                frames = rainbowFrames.get(playerId);
            }

            if (frames != null && !frames.isEmpty()) {
                int currentFrame = animationFrames.getOrDefault(playerId, 0);
                int nextFrame = (currentFrame + 1) % frames.size();
                animationFrames.put(playerId, nextFrame);

                // Atualizar display do jogador
                plugin.getPlayerDataManager().updatePlayerDisplay(player);
            }
        }
    }

    /**
     * Adiciona um jogador à animação
     */
    public void addPlayerAnimation(Player player, Tag tag) {
        if (tag.isAnimated()) {
            UUID playerId = player.getUniqueId();
            animatedTags.put(playerId, tag);
            animationFrames.put(playerId, 0);
            generateRainbowFramesForPlayer(playerId, tag);
        }
    }

    /**
     * Remove um jogador da animação
     */
    public void removePlayerAnimation(Player player) {
        removePlayerAnimation(player.getUniqueId());
    }

    /**
     * Remove um jogador da animação por UUID
     */
    public void removePlayerAnimation(UUID playerId) {
        animatedTags.remove(playerId);
        animationFrames.remove(playerId);
        rainbowFrames.remove(playerId);
    }

    /**
     * Obtém o frame atual da animação para um jogador
     */
    public int getCurrentFrame(Player player) {
        return animationFrames.getOrDefault(player.getUniqueId(), 0);
    }

    /**
     * Obtém o texto do frame atual para um jogador
     */
    public String getCurrentFrameText(Player player) {
        UUID playerId = player.getUniqueId();

        // Primeiro, tentar frames RGB gerados
        List<String> frames = rainbowFrames.get(playerId);
        if (frames != null && !frames.isEmpty()) {
            int currentFrame = animationFrames.getOrDefault(playerId, 0);
            if (currentFrame < frames.size()) {
                return frames.get(currentFrame);
            }
            return frames.get(0);
        }

        // Fallback para frames estáticos da configuração
        Tag tag = animatedTags.get(playerId);
        if (tag != null && tag.getAnimationFrames() != null && !tag.getAnimationFrames().isEmpty()) {
            List<String> staticFrames = tag.getAnimationFrames();
            long currentTime = System.currentTimeMillis() / 300; // 300ms por frame
            int frameIndex = (int) (currentTime % staticFrames.size());
            plugin.getLogger().info("DEBUG: Usando frame estático " + frameIndex + "/" + staticFrames.size() + ": "
                    + staticFrames.get(frameIndex));
            return staticFrames.get(frameIndex);
        }

        return null;
    }

    /**
     * Verifica se um jogador tem animação ativa
     */
    public boolean hasAnimation(Player player) {
        return animatedTags.containsKey(player.getUniqueId());
    }

    /**
     * Gera frames rainbow para um jogador específico
     */
    private void generateRainbowFramesForPlayer(UUID playerId, Tag tag) {
        String tagName = tag.getName().replaceAll("&[0-9a-fk-or]", ""); // Remove códigos de cor

        // Gerar frames rainbow usando o RainbowGenerator
        List<String> frames = RainbowGenerator.generateRainbowTag("&8[", tagName, "&8] ", 30);
        rainbowFrames.put(playerId, frames);
    }

    /**
     * Para todas as animações
     */
    public void stopAllAnimations() {
        if (animationTask != null) {
            animationTask.cancel();
        }
        animatedTags.clear();
        animationFrames.clear();
        rainbowFrames.clear();
    }

    /**
     * Reinicia o sistema de animação
     */
    public void restart() {
        stopAllAnimations();
        startAnimationTask();
    }
}
