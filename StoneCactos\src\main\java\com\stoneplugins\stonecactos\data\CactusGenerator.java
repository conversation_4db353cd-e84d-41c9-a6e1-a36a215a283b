package com.stoneplugins.stonecactos.data;

import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.*;

public class CactusGenerator {

    private int id;
    private UUID ownerUuid;
    private Location location;
    private int towers;
    private int capacity;
    private double battery;
    private int storedCactus;
    private int constructionQueue;
    private long constructionTime;
    private int quantityPerConstruction;
    private boolean energySaving;
    private long lastConstruction;
    private long lastProduction;

    // Sistema de amigos
    private Map<UUID, FriendPermissions> friends;

    // Sistema de boosters
    private ActiveBooster activeBooster;

    // Construtor
    public CactusGenerator(int id, UUID ownerUuid, Location location) {
        this.id = id;
        this.ownerUuid = ownerUuid;
        this.location = location;
        this.towers = 0;
        this.capacity = 100;
        this.battery = 100.0;
        this.storedCactus = 0;
        this.constructionQueue = 0;
        this.constructionTime = 40000; // 40 segundos em milissegundos
        this.quantityPerConstruction = 2;
        this.energySaving = false;
        this.lastConstruction = System.currentTimeMillis();
        this.lastProduction = System.currentTimeMillis();
        this.friends = new HashMap<>();
        this.activeBooster = null;
    }

    // Getters e Setters
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public UUID getOwnerUuid() {
        return ownerUuid;
    }

    public void setOwnerUuid(UUID ownerUuid) {
        this.ownerUuid = ownerUuid;
    }

    public Location getLocation() {
        return location;
    }

    public void setLocation(Location location) {
        this.location = location;
    }

    public int getTowers() {
        return towers;
    }

    public void setTowers(int towers) {
        this.towers = towers;
    }

    public int getCapacity() {
        return capacity;
    }

    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }

    public double getBattery() {
        return battery;
    }

    public void setBattery(double battery) {
        this.battery = Math.max(0, Math.min(100, battery));
    }

    public int getStoredCactus() {
        return storedCactus;
    }

    public void setStoredCactus(int storedCactus) {
        this.storedCactus = Math.max(0, storedCactus);
    }

    public int getConstructionQueue() {
        return constructionQueue;
    }

    public void setConstructionQueue(int constructionQueue) {
        this.constructionQueue = Math.max(0, constructionQueue);
    }

    public long getConstructionTime() {
        return constructionTime;
    }

    public void setConstructionTime(long constructionTime) {
        this.constructionTime = constructionTime;
    }

    public int getQuantityPerConstruction() {
        return quantityPerConstruction;
    }

    public void setQuantityPerConstruction(int quantityPerConstruction) {
        this.quantityPerConstruction = quantityPerConstruction;
    }

    public boolean isEnergySaving() {
        return energySaving;
    }

    public void setEnergySaving(boolean energySaving) {
        this.energySaving = energySaving;
    }

    // Métodos alternativos para economia de energia
    public boolean isEconomyMode() {
        return energySaving;
    }

    public void setEconomyMode(boolean economyMode) {
        this.energySaving = economyMode;
    }

    public long getLastConstruction() {
        return lastConstruction;
    }

    public void setLastConstruction(long lastConstruction) {
        this.lastConstruction = lastConstruction;
    }

    public long getLastProduction() {
        return lastProduction;
    }

    public void setLastProduction(long lastProduction) {
        this.lastProduction = lastProduction;
    }

    public Map<UUID, FriendPermissions> getFriends() {
        return friends;
    }

    public void setFriends(Map<UUID, FriendPermissions> friends) {
        this.friends = friends;
    }

    public ActiveBooster getActiveBooster() {
        return activeBooster;
    }

    public void setActiveBooster(ActiveBooster activeBooster) {
        this.activeBooster = activeBooster;
    }

    // Métodos utilitários
    public boolean canConstruct() {
        return constructionQueue > 0 &&
                towers + quantityPerConstruction <= capacity &&
                battery > 0 &&
                System.currentTimeMillis() - lastConstruction >= constructionTime;
    }

    public boolean canProduce() {
        return towers > 0 &&
                battery > 0 &&
                System.currentTimeMillis() - lastProduction >= getProductionInterval();
    }

    public long getProductionInterval() {
        long baseInterval = 5000; // 5 segundos base

        if (energySaving && battery < 15) {
            baseInterval *= 2; // Dobra o tempo em modo economia
        }

        if (activeBooster != null && activeBooster.isActive()) {
            baseInterval = (long) (baseInterval / activeBooster.getMultiplier());
        }

        return baseInterval;
    }

    public void addTowers(int amount) {
        if (constructionQueue >= amount) {
            constructionQueue -= amount;
            towers += amount;
            lastConstruction = System.currentTimeMillis();
        }
    }

    public void addCactus(int amount) {
        storedCactus += amount;
        lastProduction = System.currentTimeMillis();

        // Consumir bateria
        double consumption = (amount / 10.0) * 1.0; // 1% por 10 cactos
        if (energySaving && battery < 15) {
            consumption /= 2; // Metade do consumo em modo economia
        }
        setBattery(battery - consumption);
    }

    public void addToQueue(int amount) {
        constructionQueue += amount;
    }

    public boolean hasPermission(UUID playerUuid, String permission) {
        if (ownerUuid.equals(playerUuid)) {
            return true;
        }

        FriendPermissions perms = friends.get(playerUuid);
        if (perms == null) {
            return false;
        }

        switch (permission.toLowerCase()) {
            case "add_towers":
                return perms.canAddTowers();
            case "remove_towers":
                return perms.canRemoveTowers();
            case "sell_cactus":
                return perms.canSellCactus();
            default:
                return false;
        }
    }

    public void addFriend(UUID friendUuid, boolean canAddTowers, boolean canRemoveTowers, boolean canSellCactus) {
        friends.put(friendUuid, new FriendPermissions(canAddTowers, canRemoveTowers, canSellCactus));
    }

    public void removeFriend(UUID friendUuid) {
        friends.remove(friendUuid);
    }

    public boolean hasBattery() {
        return battery > 0;
    }

    public boolean isOwner(Player player) {
        return ownerUuid.equals(player.getUniqueId());
    }

    public boolean isFriend(Player player) {
        return friends.containsKey(player.getUniqueId());
    }

    public double getBatteryPercentage() {
        return Math.max(0, Math.min(100, battery));
    }

    public String getBatteryDisplay() {
        return String.format("%.1f%%", getBatteryPercentage());
    }

    public boolean needsEnergyMode() {
        return battery < 15;
    }

    public int getAvailableCapacity() {
        return capacity - towers;
    }

    public boolean hasActiveBooster() {
        return activeBooster != null && activeBooster.isActive();
    }

    public void expireBooster() {
        if (activeBooster != null && !activeBooster.isActive()) {
            activeBooster = null;
        }
    }
}
