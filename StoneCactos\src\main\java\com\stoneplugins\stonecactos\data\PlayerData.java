package com.stoneplugins.stonecactos.data;

import java.util.*;

public class PlayerData {
    
    private UUID playerUuid;
    private int totalCactusGenerated;
    private int totalCactusSold;
    private double totalMoneyEarned;
    private int generatorsPlaced;
    private int boostersUsed;
    private Set<Integer> collectedRewards;
    private long createdAt;
    private long updatedAt;
    
    public PlayerData(UUID playerUuid) {
        this.playerUuid = playerUuid;
        this.totalCactusGenerated = 0;
        this.totalCactusSold = 0;
        this.totalMoneyEarned = 0.0;
        this.generatorsPlaced = 0;
        this.boostersUsed = 0;
        this.collectedRewards = new HashSet<>();
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public PlayerData(UUID playerUuid, int totalCactusGenerated, int totalCactusSold, 
                     double totalMoneyEarned, int generatorsPlaced, int boostersUsed,
                     Set<Integer> collectedRewards, long createdAt, long updatedAt) {
        this.playerUuid = playerUuid;
        this.totalCactusGenerated = totalCactusGenerated;
        this.totalCactusSold = totalCactusSold;
        this.totalMoneyEarned = totalMoneyEarned;
        this.generatorsPlaced = generatorsPlaced;
        this.boostersUsed = boostersUsed;
        this.collectedRewards = collectedRewards != null ? collectedRewards : new HashSet<>();
        this.createdAt = createdAt;
        this.updatedAt = updatedAt;
    }
    
    // Getters e Setters
    public UUID getPlayerUuid() {
        return playerUuid;
    }
    
    public void setPlayerUuid(UUID playerUuid) {
        this.playerUuid = playerUuid;
    }
    
    public int getTotalCactusGenerated() {
        return totalCactusGenerated;
    }
    
    public void setTotalCactusGenerated(int totalCactusGenerated) {
        this.totalCactusGenerated = totalCactusGenerated;
    }
    
    public int getTotalCactusSold() {
        return totalCactusSold;
    }
    
    public void setTotalCactusSold(int totalCactusSold) {
        this.totalCactusSold = totalCactusSold;
    }
    
    public double getTotalMoneyEarned() {
        return totalMoneyEarned;
    }
    
    public void setTotalMoneyEarned(double totalMoneyEarned) {
        this.totalMoneyEarned = totalMoneyEarned;
    }
    
    public int getGeneratorsPlaced() {
        return generatorsPlaced;
    }
    
    public void setGeneratorsPlaced(int generatorsPlaced) {
        this.generatorsPlaced = generatorsPlaced;
    }
    
    public int getBoostersUsed() {
        return boostersUsed;
    }
    
    public void setBoostersUsed(int boostersUsed) {
        this.boostersUsed = boostersUsed;
    }
    
    public Set<Integer> getCollectedRewards() {
        return collectedRewards;
    }
    
    public void setCollectedRewards(Set<Integer> collectedRewards) {
        this.collectedRewards = collectedRewards;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Métodos utilitários
    public void addCactusGenerated(int amount) {
        this.totalCactusGenerated += amount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public void addCactusSold(int amount) {
        this.totalCactusSold += amount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public void addMoneyEarned(double amount) {
        this.totalMoneyEarned += amount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public void incrementGeneratorsPlaced() {
        this.generatorsPlaced++;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public void incrementBoostersUsed() {
        this.boostersUsed++;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public void collectReward(int rewardId) {
        this.collectedRewards.add(rewardId);
        this.updatedAt = System.currentTimeMillis();
    }
    
    public boolean hasCollectedReward(int rewardId) {
        return collectedRewards.contains(rewardId);
    }
    
    public int getCollectedRewardsCount() {
        return collectedRewards.size();
    }
    
    public double getAverageCactusPerGenerator() {
        if (generatorsPlaced == 0) {
            return 0.0;
        }
        return (double) totalCactusGenerated / generatorsPlaced;
    }
    
    public double getAverageMoneyPerCactus() {
        if (totalCactusSold == 0) {
            return 0.0;
        }
        return totalMoneyEarned / totalCactusSold;
    }
    
    public String getFormattedTotalMoney() {
        if (totalMoneyEarned >= 1000000) {
            return String.format("%.1fM", totalMoneyEarned / 1000000);
        } else if (totalMoneyEarned >= 1000) {
            return String.format("%.1fK", totalMoneyEarned / 1000);
        } else {
            return String.format("%.2f", totalMoneyEarned);
        }
    }
    
    public String getFormattedCactusGenerated() {
        if (totalCactusGenerated >= 1000000) {
            return String.format("%.1fM", totalCactusGenerated / 1000000.0);
        } else if (totalCactusGenerated >= 1000) {
            return String.format("%.1fK", totalCactusGenerated / 1000.0);
        } else {
            return String.valueOf(totalCactusGenerated);
        }
    }
    
    public String getFormattedCactusSold() {
        if (totalCactusSold >= 1000000) {
            return String.format("%.1fM", totalCactusSold / 1000000.0);
        } else if (totalCactusSold >= 1000) {
            return String.format("%.1fK", totalCactusSold / 1000.0);
        } else {
            return String.valueOf(totalCactusSold);
        }
    }
    
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();
        stats.put("totalCactusGenerated", totalCactusGenerated);
        stats.put("totalCactusSold", totalCactusSold);
        stats.put("totalMoneyEarned", totalMoneyEarned);
        stats.put("generatorsPlaced", generatorsPlaced);
        stats.put("boostersUsed", boostersUsed);
        stats.put("collectedRewards", collectedRewards.size());
        stats.put("averageCactusPerGenerator", getAverageCactusPerGenerator());
        stats.put("averageMoneyPerCactus", getAverageMoneyPerCactus());
        return stats;
    }
    
    @Override
    public String toString() {
        return "PlayerData{" +
                "playerUuid=" + playerUuid +
                ", totalCactusGenerated=" + totalCactusGenerated +
                ", totalCactusSold=" + totalCactusSold +
                ", totalMoneyEarned=" + totalMoneyEarned +
                ", generatorsPlaced=" + generatorsPlaced +
                ", boostersUsed=" + boostersUsed +
                ", collectedRewards=" + collectedRewards.size() +
                '}';
    }
}
