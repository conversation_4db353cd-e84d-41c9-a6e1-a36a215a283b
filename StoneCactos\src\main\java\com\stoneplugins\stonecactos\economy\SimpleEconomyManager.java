package com.stoneplugins.stonecactos.economy;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Sistema de economia simplificado para o plugin StoneCactos
 */
public class SimpleEconomyManager {
    
    private final StoneCactos plugin;
    private final Map<UUID, Double> playerBalances = new ConcurrentHashMap<>();
    
    // Configurações de preços
    private final double CACTUS_PRICE = 1.0;
    private final double UPGRADE_BASE_PRICE = 1000.0;
    private final double BOOSTER_BASE_PRICE = 500.0;
    
    public SimpleEconomyManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    public void setupEconomy() {
        plugin.getLogger().info("Sistema de economia simplificado configurado!");
    }
    
    // === MÉTODOS DE SALDO ===
    
    public double getBalance(UUID playerId) {
        return playerBalances.getOrDefault(playerId, 1000.0); // Saldo inicial de $1000
    }
    
    public boolean hasBalance(Player player, double amount) {
        return getBalance(player.getUniqueId()) >= amount;
    }
    
    public boolean withdrawMoney(Player player, double amount) {
        UUID playerId = player.getUniqueId();
        double currentBalance = getBalance(playerId);
        
        if (currentBalance >= amount) {
            playerBalances.put(playerId, currentBalance - amount);
            return true;
        }
        return false;
    }
    
    public void depositMoney(Player player, double amount) {
        UUID playerId = player.getUniqueId();
        double currentBalance = getBalance(playerId);
        playerBalances.put(playerId, currentBalance + amount);
    }
    
    public String formatMoney(double amount) {
        return String.format("$%.2f", amount);
    }
    
    // === MÉTODOS DE VENDA DE CACTOS ===
    
    public boolean sellCactus(Player player, int amount) {
        if (amount <= 0) return false;
        
        double totalValue = amount * CACTUS_PRICE;
        depositMoney(player, totalValue);
        
        player.sendMessage("§a[StoneCactos] §fVocê vendeu §e" + amount + " cactos §fpor §a" + formatMoney(totalValue) + "§f!");
        
        // Atualizar estatísticas
        plugin.getMemoryDataManager().updatePlayerStats(
            player.getUniqueId(), 0, amount, totalValue, 0, 0);
        
        return true;
    }
    
    // === MÉTODOS DE UPGRADES ===
    
    public boolean buyUpgrade(Player player, String upgradeType, int level) {
        double price = calculateUpgradePrice(upgradeType, level);
        
        if (!hasBalance(player, price)) {
            player.sendMessage("§c[StoneCactos] §fVocê não tem dinheiro suficiente! Necessário: §c" + formatMoney(price));
            return false;
        }
        
        if (withdrawMoney(player, price)) {
            player.sendMessage("§a[StoneCactos] §fUpgrade comprado com sucesso por §a" + formatMoney(price) + "§f!");
            return true;
        }
        
        return false;
    }
    
    private double calculateUpgradePrice(String upgradeType, int level) {
        double multiplier = 1.0;
        
        switch (upgradeType.toLowerCase()) {
            case "capacitytowers":
                multiplier = 1.5;
                break;
            case "quantityperconstruction":
                multiplier = 2.0;
                break;
            case "constructiontime":
                multiplier = 1.8;
                break;
            default:
                multiplier = 1.0;
        }
        
        return UPGRADE_BASE_PRICE * multiplier * Math.pow(1.5, level - 1);
    }
    
    public double getUpgradePrice(String upgradeType, int level) {
        return calculateUpgradePrice(upgradeType, level);
    }
    
    // === MÉTODOS DE BOOSTERS ===
    
    public boolean buyBooster(Player player, String boosterId) {
        double price = calculateBoosterPrice(boosterId);
        
        if (!hasBalance(player, price)) {
            player.sendMessage("§c[StoneCactos] §fVocê não tem dinheiro suficiente! Necessário: §c" + formatMoney(price));
            return false;
        }
        
        if (withdrawMoney(player, price)) {
            player.sendMessage("§a[StoneCactos] §fBooster comprado com sucesso por §a" + formatMoney(price) + "§f!");
            
            // Atualizar estatísticas
            plugin.getMemoryDataManager().updatePlayerStats(
                player.getUniqueId(), 0, 0, 0.0, 0, 1);
            
            return true;
        }
        
        return false;
    }
    
    private double calculateBoosterPrice(String boosterId) {
        double multiplier = 1.0;
        
        switch (boosterId.toLowerCase()) {
            case "speed":
            case "velocidade":
                multiplier = 1.0;
                break;
            case "production":
            case "producao":
                multiplier = 1.5;
                break;
            case "efficiency":
            case "eficiencia":
                multiplier = 2.0;
                break;
            default:
                multiplier = 1.0;
        }
        
        return BOOSTER_BASE_PRICE * multiplier;
    }
    
    public double getBoosterPrice(String boosterId) {
        return calculateBoosterPrice(boosterId);
    }
    
    // === MÉTODOS DE INFORMAÇÃO ===
    
    public void showBalance(Player player) {
        double balance = getBalance(player.getUniqueId());
        player.sendMessage("§a[StoneCactos] §fSeu saldo atual: §a" + formatMoney(balance));
    }
    
    public void addStartingMoney(Player player) {
        UUID playerId = player.getUniqueId();
        if (!playerBalances.containsKey(playerId)) {
            playerBalances.put(playerId, 1000.0);
            player.sendMessage("§a[StoneCactos] §fVocê recebeu §a$1000.00 §fde bônus inicial!");
        }
    }
    
    // === MÉTODOS DE CONFIGURAÇÃO ===
    
    public void setCactusPrice(double price) {
        // Em um sistema mais avançado, isso seria configurável
    }
    
    public double getCactusPrice() {
        return CACTUS_PRICE;
    }
    
    // === MÉTODOS DE ESTATÍSTICAS ===
    
    public String getEconomyInfo() {
        int totalPlayers = playerBalances.size();
        double totalMoney = playerBalances.values().stream().mapToDouble(Double::doubleValue).sum();
        
        return String.format("§e=== Economia StoneCactos ===\n" +
                           "§fJogadores registrados: §a%d\n" +
                           "§fDinheiro total em circulação: §a%s\n" +
                           "§fPreço do cacto: §a%s",
                           totalPlayers, formatMoney(totalMoney), formatMoney(CACTUS_PRICE));
    }
}
