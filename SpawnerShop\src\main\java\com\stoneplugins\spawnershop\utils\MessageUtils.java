package com.stoneplugins.spawnershop.utils;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.text.DecimalFormat;
import java.util.List;

public class MessageUtils {

    private final SpawnerShop plugin;
    private final DecimalFormat numberFormat;

    public MessageUtils(SpawnerShop plugin) {
        this.plugin = plugin;
        this.numberFormat = new DecimalFormat("#,###.##");
    }

    public String colorize(String message) {
        if (message == null)
            return "";
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    public String formatNumber(double number) {
        if (number >= 1000000000) {
            return String.format("%.1fB", number / 1000000000);
        } else if (number >= 1000000) {
            return String.format("%.1fM", number / 1000000);
        } else if (number >= 1000) {
            return String.format("%.1fK", number / 1000);
        } else {
            return numberFormat.format(number);
        }
    }

    public String formatNumberLong(long number) {
        return formatNumber((double) number);
    }

    public void sendMessage(Player player, String message) {
        if (message == null || message.isEmpty())
            return;
        player.sendMessage(colorize(message));
    }

    public void sendMessages(Player player, List<String> messages) {
        if (messages == null || messages.isEmpty())
            return;
        for (String message : messages) {
            sendMessage(player, message);
        }
    }

    public String replacePlaceholders(String message, Player player) {
        if (message == null)
            return "";

        // Placeholders básicos do jogador
        message = message.replace("{player}", player.getName());
        message = message.replace("{jogador}", player.getName());

        // Placeholders de dados do jogador
        int purchases = plugin.getPlayerDataManager().getPurchaseCount(player);
        int limit = plugin.getPlayerDataManager().getPurchaseLimit(player);
        int remaining = plugin.getPlayerDataManager().getRemainingLimit(player);
        double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
        String groups = plugin.getPlayerDataManager().getPlayerDiscountGroups(player);
        double balance = plugin.getEconomyManager().getBalance(player);

        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
        int multiplicador = playerData != null ? playerData.getMultiplicador() : 1;

        message = message.replace("{compras}", String.valueOf(purchases));
        message = message.replace("{limite}", String.valueOf(limit));
        message = message.replace("{restante}", String.valueOf(remaining));
        message = message.replace("{desconto}", String.valueOf((int) discount));
        message = message.replace("{grupos}", groups);
        message = message.replace("{saldo}", formatNumber(balance));
        message = message.replace("{descontos}", String.valueOf((int) discount) + "%");
        message = message.replace("{multiplicador}", String.valueOf(multiplicador));

        return message;
    }

    public String replacePlaceholders(String message, Player player, String spawnerType, int quantity, double price) {
        message = replacePlaceholders(message, player);

        // Placeholders específicos do spawner
        message = message.replace("{spawner}", spawnerType);
        message = message.replace("{quantidade}", String.valueOf(quantity));
        message = message.replace("{preco}", formatNumber(price));

        return message;
    }

    public String replaceSpawnerPlaceholders(String message, Player player, String spawnerType, double price,
            double discount, int maxQuantity) {
        message = replacePlaceholders(message, player);

        // Placeholders do spawner
        message = message.replace("{spawner}", spawnerType);
        message = message.replace("{preco}", formatNumber(price));
        message = message.replace("{desconto}", String.valueOf((int) discount));
        message = message.replace("{geradores}", String.valueOf(maxQuantity));
        message = message.replace("{grupos}", plugin.getPlayerDataManager().getPlayerDiscountGroups(player));

        return message;
    }

    public String getConfigMessage(String key) {
        return colorize(plugin.getConfigManager().getMessage(key));
    }

    public String getConfigMessage(String key, Player player) {
        String message = plugin.getConfigManager().getMessage(key);
        return colorize(replacePlaceholders(message, player));
    }

    public List<String> getConfigMessageList(String key) {
        List<String> messages = plugin.getConfigManager().getMessageList(key);
        for (int i = 0; i < messages.size(); i++) {
            messages.set(i, colorize(messages.get(i)));
        }
        return messages;
    }

    public List<String> getConfigMessageList(String key, Player player) {
        List<String> messages = plugin.getConfigManager().getMessageList(key);
        for (int i = 0; i < messages.size(); i++) {
            String message = messages.get(i);
            message = replacePlaceholders(message, player);
            messages.set(i, colorize(message));
        }
        return messages;
    }

    public void sendConfigMessage(Player player, String key) {
        String message = getConfigMessage(key, player);
        if (!message.isEmpty()) {
            player.sendMessage(message);
        }
    }

    public void sendConfigMessageList(Player player, String key) {
        List<String> messages = getConfigMessageList(key, player);
        for (String message : messages) {
            if (!message.isEmpty()) {
                player.sendMessage(message);
            }
        }
    }

    public String stripColors(String message) {
        if (message == null)
            return "";
        return ChatColor.stripColor(colorize(message));
    }

    public boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    public boolean isInteger(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Integer.parseInt(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}
