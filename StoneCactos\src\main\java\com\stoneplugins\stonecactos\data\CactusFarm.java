package com.stoneplugins.stonecactos.data;

import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class CactusFarm {
    
    private String id;
    private UUID owner;
    private Location location;
    private int towers;
    private int capacity;
    private int towersInDevelopment;
    private int developmentQueue;
    private int constructionSpeed; // em segundos
    private int quantityPerConstruction;
    private int storedCactus;
    private int battery;
    private boolean energySavingMode;
    private long lastConstruction;
    private List<UUID> friends;
    private String activeBooster;
    private long boosterExpiry;
    
    public CactusFarm(String id, UUID owner, Location location) {
        this.id = id;
        this.owner = owner;
        this.location = location;
        this.towers = 0;
        this.capacity = 100;
        this.towersInDevelopment = 0;
        this.developmentQueue = 0;
        this.constructionSpeed = 40; // 40 segundos padrão
        this.quantityPerConstruction = 2; // 2 torres por construção
        this.storedCactus = 0;
        this.battery = 100;
        this.energySavingMode = false;
        this.lastConstruction = System.currentTimeMillis();
        this.friends = new ArrayList<>();
        this.activeBooster = null;
        this.boosterExpiry = 0;
    }
    
    // Getters e Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public UUID getOwner() {
        return owner;
    }
    
    public void setOwner(UUID owner) {
        this.owner = owner;
    }
    
    public Location getLocation() {
        return location;
    }
    
    public void setLocation(Location location) {
        this.location = location;
    }
    
    public int getTowers() {
        return towers;
    }
    
    public void setTowers(int towers) {
        this.towers = towers;
    }
    
    public int getCapacity() {
        return capacity;
    }
    
    public void setCapacity(int capacity) {
        this.capacity = capacity;
    }
    
    public int getTowersInDevelopment() {
        return towersInDevelopment;
    }
    
    public void setTowersInDevelopment(int towersInDevelopment) {
        this.towersInDevelopment = towersInDevelopment;
    }
    
    public int getDevelopmentQueue() {
        return developmentQueue;
    }
    
    public void setDevelopmentQueue(int developmentQueue) {
        this.developmentQueue = developmentQueue;
    }
    
    public int getConstructionSpeed() {
        return constructionSpeed;
    }
    
    public void setConstructionSpeed(int constructionSpeed) {
        this.constructionSpeed = constructionSpeed;
    }
    
    public int getQuantityPerConstruction() {
        return quantityPerConstruction;
    }
    
    public void setQuantityPerConstruction(int quantityPerConstruction) {
        this.quantityPerConstruction = quantityPerConstruction;
    }
    
    public int getStoredCactus() {
        return storedCactus;
    }
    
    public void setStoredCactus(int storedCactus) {
        this.storedCactus = storedCactus;
    }
    
    public int getBattery() {
        return battery;
    }
    
    public void setBattery(int battery) {
        this.battery = battery;
    }
    
    public boolean isEnergySavingMode() {
        return energySavingMode;
    }
    
    public void setEnergySavingMode(boolean energySavingMode) {
        this.energySavingMode = energySavingMode;
    }
    
    public long getLastConstruction() {
        return lastConstruction;
    }
    
    public void setLastConstruction(long lastConstruction) {
        this.lastConstruction = lastConstruction;
    }
    
    public List<UUID> getFriends() {
        return friends;
    }
    
    public void setFriends(List<UUID> friends) {
        this.friends = friends;
    }
    
    public String getActiveBooster() {
        return activeBooster;
    }
    
    public void setActiveBooster(String activeBooster) {
        this.activeBooster = activeBooster;
    }
    
    public long getBoosterExpiry() {
        return boosterExpiry;
    }
    
    public void setBoosterExpiry(long boosterExpiry) {
        this.boosterExpiry = boosterExpiry;
    }
    
    // Métodos utilitários
    public boolean hasActiveBooster() {
        return activeBooster != null && System.currentTimeMillis() < boosterExpiry;
    }
    
    public boolean canConstruct() {
        return developmentQueue > 0 && towers + towersInDevelopment < capacity;
    }
    
    public boolean isPlayerAllowed(Player player) {
        return player.getUniqueId().equals(owner) || friends.contains(player.getUniqueId());
    }
    
    public void addFriend(UUID friendId) {
        if (!friends.contains(friendId)) {
            friends.add(friendId);
        }
    }
    
    public void removeFriend(UUID friendId) {
        friends.remove(friendId);
    }
    
    public void addCactusToStorage(int amount) {
        this.storedCactus += amount;
    }
    
    public void consumeBattery(int amount) {
        this.battery = Math.max(0, this.battery - amount);
    }
    
    public void rechargeBattery(int amount) {
        this.battery = Math.min(100, this.battery + amount);
    }
}
