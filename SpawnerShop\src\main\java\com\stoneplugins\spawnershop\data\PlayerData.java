package com.stoneplugins.spawnershop.data;

import java.util.UUID;

public class PlayerData {

    private UUID uuid;
    private String name;
    private int purchases;
    private int purchaseLimit;
    private long lastPurchase;
    private int multiplicador;

    public PlayerData(UUID uuid, String name) {
        this.uuid = uuid;
        this.name = name;
        this.purchases = 0;
        this.purchaseLimit = 64; // Valor padrão
        this.lastPurchase = 0;
        this.multiplicador = 1; // Valor padrão
    }

    public PlayerData(UUID uuid, String name, int purchases, int purchaseLimit, long lastPurchase, int multiplicador) {
        this.uuid = uuid;
        this.name = name;
        this.purchases = purchases;
        this.purchaseLimit = purchaseLimit;
        this.lastPurchase = lastPurchase;
        this.multiplicador = multiplicador;
    }

    // Getters
    public UUID getUuid() {
        return uuid;
    }

    public String getName() {
        return name;
    }

    public int getPurchases() {
        return purchases;
    }

    public int getPurchaseLimit() {
        return purchaseLimit;
    }

    public long getLastPurchase() {
        return lastPurchase;
    }

    public int getMultiplicador() {
        return multiplicador;
    }

    // Setters
    public void setName(String name) {
        this.name = name;
    }

    public void setPurchases(int purchases) {
        this.purchases = purchases;
    }

    public void setPurchaseLimit(int purchaseLimit) {
        this.purchaseLimit = purchaseLimit;
    }

    public void setLastPurchase(long lastPurchase) {
        this.lastPurchase = lastPurchase;
    }

    public void setMultiplicador(int multiplicador) {
        this.multiplicador = multiplicador;
    }

    // Utility methods
    public void addPurchases(int amount) {
        this.purchases += amount;
        this.lastPurchase = System.currentTimeMillis();
    }

    public boolean canPurchase(int amount) {
        // O limite é por compra, não acumulado
        return amount <= purchaseLimit;
    }

    public int getRemainingLimit() {
        // O limite é por compra, então sempre retorna o limite total
        return purchaseLimit;
    }

    public void increasePurchaseLimit(int amount) {
        this.purchaseLimit += amount;
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "uuid=" + uuid +
                ", name='" + name + '\'' +
                ", purchases=" + purchases +
                ", purchaseLimit=" + purchaseLimit +
                ", lastPurchase=" + lastPurchase +
                '}';
    }
}
