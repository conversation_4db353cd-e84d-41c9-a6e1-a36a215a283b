# ================================
#   CONFIGURAÇÃO SCOREBOARD OTIMIZADA
#   Linhas curtas para evitar erro de 40 caracteres
# ================================

ScoreBoard:
  use: true
  titulo: "&b&lPESCA"
  linhas:
    - '&1'
    - '&f Missões: &7%missoes_concluidas%'
    - '&f Bônus: &a%booster%'
    - '&2'
    - '&e&l ▸ Vara:'
    - '&b&l  ▎ &fNv: &7%vara_nivel%'
    - '&b&l  ▎ &fXP: &7%vara_xp%/%vara_next_xp%'
    - '&3'
    - '&f $: &a%coins%'
    - '&f Cash: &6✪%cash%'
    - '&f Peixes: &e✧%peixes%'
    - '&4'
    - '&bstormplugins.com'

# ================================
#   CONFIGURAÇÃO GERAL OTIMIZADA
# ================================

Geral:
  sql-type: SQLITE
  cash-plugin: PLAYERPOINTS
  format: LETRAS
  ativarXpNaVara: true
  xpPorPeixe: 50 # Reduzido para acelerar
  xpPadraoDeEvolucao: 500 # Reduzido para acelerar
  xpACadaNivel: 50 # Reduzido para acelerar
  autoCompletarRanking: true
  venderPeixesCoins: true
  venderPeixesCash: true
  lojaPeixes: true
  lojaSlots: "11,12,13,14,15"
  ativarItensLoja: true
  autoCompletarLoja: true
  lojaBooster: true
  raioPesca: 10
  chancePescarPeixe: 95.0 # Aumentado para 95%
  chanceEvoluirNivel: 50.0 # Aumentado para 50%
  chanceGanharCoins: 75.0 # Aumentado para 75%
  coinsAoPescar: 50.0
  tempoPesca: 2 # Reduzido para 2 segundos
  precoPeixesCoins: 10.0 # Preço por peixe em coins
  precoPeixesCash: 1.0 # Preço por peixe em cash
  ativarRecompensas: true
  recompensasPesca:
    - 'give %player% minecraft:DIAMOND 1;5.0'
    - 'give %player% minecraft:EMERALD 2;10.0'
  horarioPesca:
    use: false # Desabilitado para testes
  comandosPermitidos:
    - '/pesca'
    - '/pesca loja'
    - '/pesca help'
  mundosPermitidos:
    - 'world'
    - 'pesca'

# ================================
#   VARA OTIMIZADA
# ================================

Vara:
  nome: "&b&lVARA STORM &7(%peixes%)"
  lore:
    - '&7Clique com botão &fdireito + shift&7'
    - '&7para abrir o menu da vara.'
    - ''
    - '&f &b&l▎ &fSorte: &7%sorte%'
    - '&f &b&l▎ &fVelocidade: &7%velocidade%'
    - '&f &b&l▎ &fFortuna: &7%fortuna%'
  encantamentosInventarioTamanho: 45
  encantamentosInventarioFlechaVoltar: 40
  encantamentos:
    sorte:
      ativado: true
      slot: 20
      aumentarNivelAoComprar: true
      nivelPlayerNecessario: 1
      nivelPadrao: 0
      nivelMaximo: 5
      precoUpgrade: 100 # Reduzido
      item: "FEATHER:0"
      name: "&a&lSORTE"
      lore:
        - '&7Aumenta chance de pescar'
        - '&7em &f10%&7 por nível.'
        - ''
        - '&f Nível: &7%nivel%/%nivel_max%'
        - '&f Custo: &e%custo% peixes'
        - ''
        - '&aClique para evoluir'
    velocidade:
      ativado: true
      slot: 22
      aumentarNivelAoComprar: true
      nivelPlayerNecessario: 1
      nivelPadrao: 0
      nivelMaximo: 5
      precoUpgrade: 150 # Reduzido
      item: "POTION:0"
      name: "&b&lVELOCIDADE"
      lore:
        - '&7Diminui tempo de pesca'
        - '&7em &f1s&7 por nível.'
        - ''
        - '&f Nível: &7%nivel%/%nivel_max%'
        - '&f Custo: &e%custo% peixes'
        - ''
        - '&aClique para evoluir'
    fortuna:
      ativado: true
      slot: 24
      aumentarNivelAoComprar: true
      nivelPlayerNecessario: 1
      nivelPadrao: 0
      nivelMaximo: 5
      precoUpgrade: 200 # Reduzido
      item: "GOLD_INGOT:0"
      name: "&6&lFORTUNA"
      lore:
        - '&7Aumenta peixes ganhos'
        - '&7em &f10%&7 por nível.'
        - ''
        - '&f Nível: &7%nivel%/%nivel_max%'
        - '&f Custo: &e%custo% peixes'
        - ''
        - '&aClique para evoluir'

# ================================
#   LOJA OTIMIZADA
# ================================

Loja:
  capacete_diamante:
    item: "DIAMOND_HELMET:0"
    name: "&9&lCapacete Diamante"
    lore:
      - '&8 ITEM ÉPICO'
      - ''
      - '&f Proteção: &aIV'
      - '&f Durabilidade: &aInfinita'
      - ''
      - '&f Custo: &e✧%preco% peixes'
      - ''
      - '&aClique para comprar'
    glow: true
    preco: 500 # Reduzido
    give:
      item:
        use: true
        itens:
          capacete:
            item: "DIAMOND_HELMET:0"
            itemmeta:
              use: true
              name: "&9Capacete Épico"
              lore:
                - '&7Proteção máxima'
            enchantments:
              use: true
              enchants:
                - 'PROTECTION_ENVIRONMENTAL:4'
                - 'DURABILITY:3'

# ================================
#   BOOSTERS OTIMIZADOS
# ================================

Boosters:
  pesca_2x:
    item: "EXP_BOTTLE:0"
    name: "&aBooster Pesca 2x"
    lore:
      - ''
      - '&f Tipo: &aBooster Pesca'
      - '&f Multiplicador: &72.0x'
      - '&f Duração: &730 min'
      - '&f Preço: &7%preco%'
      - ''
    duracao: 0.5 # 30 minutos
    multiplicacao: 2.0
    preco: 1000 # Reduzido
    moeda: PEIXES

# ================================
#   MENUS OTIMIZADOS
# ================================

Menus:
  loja:
    coins:
      item: "IRON_INGOT:0"
      name: "&aVender por Coins"
      lore:
        - '&7Venda peixes por coins.'
        - ''
        - '&7Esquerdo: vender x64'
        - '&7Direito: vender tudo'
        - ''
        - '&f Peixes: &7%peixes%'
        - '&f Preço: &7%valor% coins'
        - ''
    cash:
      item: "GOLD_INGOT:0"
      name: "&6Vender por Cash"
      lore:
        - '&7Venda peixes por cash.'
        - ''
        - '&7Esquerdo: vender x64'
        - '&7Direito: vender tudo'
        - ''
        - '&f Peixes: &7%peixes%'
        - '&f Preço: &7%valor% cash'
        - ''
  ranking:
    jogador:
      name: "&f%pos%º &7%jogador%"
      lore:
        - ''
        - '&f Peixes: &6%peixes%'
        - '&f Posição: &a%pos%'
        - ''
  principal:
    tamanho: 27
    encantamentos:
      item: "ENCHANTMENT_TABLE:0"
      slot: 10
      name: "&dEncantamentos"
      lore:
        - '&7Gerencie sua vara.'
    top:
      item: "BOOK_AND_QUILL:0"
      slot: 11
      name: "&eTOP Jogadores"
      lore:
        - '&7Ver ranking de pesca.'
    boosters:
      item: "EXP_BOTTLE:0"
      name: "&aBoosters"
      slot: 12
      lore:
        - '&7Ver boosters ativos.'
    loja:
      item: "SIGN:0"
      slot: 13
      name: "&aLoja"
      lore:
        - '&7Comprar e vender.'
    pescar:
      item: "FISHING_ROD:0"
      slot: 15
      name: "&aIr Pescar"
      lore:
        - '&7Teleportar para pesca.'
    sairPescaria:
      item: "FISHING_ROD:0"
      name: "&cSair da Pesca"
      lore:
        - '&7Voltar ao spawn.'
