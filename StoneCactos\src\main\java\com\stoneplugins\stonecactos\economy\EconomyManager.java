package com.stoneplugins.stonecactos.economy;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;

public class EconomyManager {

    private final StoneCactos plugin;
    private Object vaultEconomy;
    private boolean vaultEnabled = false;

    // Sistema de economia interno como fallback
    private final Map<UUID, Double> internalBalances = new ConcurrentHashMap<>();
    private final double STARTING_BALANCE = 1000.0;

    public EconomyManager(StoneCactos plugin) {
        this.plugin = plugin;
    }

    /**
     * Configura o sistema de economia
     */
    public void setupEconomy() {
        if (plugin.getServer().getPluginManager().getPlugin("Vault") == null) {
            plugin.getLogger().warning("Vault não encontrado! Usando sistema de economia interno.");
            vaultEnabled = false;
            return;
        }

        try {
            // Tentar configurar Vault usando reflection para evitar dependências
            Class<?> economyClass = Class.forName("net.milkbowl.vault.economy.Economy");
            Object rsp = plugin.getServer().getServicesManager().getRegistration(economyClass);

            if (rsp == null) {
                plugin.getLogger().warning("Nenhum plugin de economia encontrado! Usando sistema interno.");
                vaultEnabled = false;
                return;
            }

            // Usar reflection para obter o provider
            vaultEconomy = rsp.getClass().getMethod("getProvider").invoke(rsp);
            vaultEnabled = true;

            String economyName = (String) vaultEconomy.getClass().getMethod("getName").invoke(vaultEconomy);
            plugin.getLogger().info("Sistema de economia configurado com " + economyName);

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao configurar Vault: " + e.getMessage() + ". Usando sistema interno.");
            vaultEnabled = false;
        }
    }

    /**
     * Verifica se o Vault está disponível
     */
    public boolean isVaultEnabled() {
        return vaultEnabled && vaultEconomy != null;
    }

    // === MÉTODOS DO SISTEMA INTERNO ===

    private double getInternalBalance(UUID playerId) {
        return internalBalances.getOrDefault(playerId, STARTING_BALANCE);
    }

    private void setInternalBalance(UUID playerId, double amount) {
        internalBalances.put(playerId, amount);
    }

    private boolean hasInternalBalance(UUID playerId, double amount) {
        return getInternalBalance(playerId) >= amount;
    }

    private boolean withdrawInternal(UUID playerId, double amount) {
        if (hasInternalBalance(playerId, amount)) {
            setInternalBalance(playerId, getInternalBalance(playerId) - amount);
            return true;
        }
        return false;
    }

    private void depositInternal(UUID playerId, double amount) {
        setInternalBalance(playerId, getInternalBalance(playerId) + amount);
    }

    /**
     * Obtém o saldo de um jogador
     */
    public double getBalance(Player player) {
        return getBalance(player.getUniqueId());
    }

    /**
     * Obtém o saldo de um jogador por UUID
     */
    public double getBalance(UUID playerUuid) {
        if (!isVaultEnabled()) {
            return getInternalBalance(playerUuid);
        }

        try {
            Player player = Bukkit.getPlayer(playerUuid);
            if (player == null) {
                return getInternalBalance(playerUuid);
            }

            return (Double) vaultEconomy.getClass().getMethod("getBalance", Player.class).invoke(vaultEconomy, player);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao obter saldo via Vault: " + e.getMessage());
            return getInternalBalance(playerUuid);
        }
    }

    /**
     * Verifica se um jogador tem saldo suficiente
     */
    public boolean hasBalance(Player player, double amount) {
        return hasBalance(player.getUniqueId(), amount);
    }

    /**
     * Verifica se um jogador tem saldo suficiente por UUID
     */
    public boolean hasBalance(UUID playerUuid, double amount) {
        return getBalance(playerUuid) >= amount;
    }

    /**
     * Remove dinheiro de um jogador
     */
    public boolean withdrawMoney(Player player, double amount) {
        return withdrawMoney(player.getUniqueId(), amount);
    }

    /**
     * Remove dinheiro de um jogador por UUID
     */
    public boolean withdrawMoney(UUID playerUuid, double amount) {
        if (!isVaultEnabled()) {
            return withdrawInternal(playerUuid, amount);
        }

        try {
            Player player = Bukkit.getPlayer(playerUuid);
            if (player == null) {
                return withdrawInternal(playerUuid, amount);
            }

            if (!hasBalance(playerUuid, amount)) {
                return false;
            }

            Object response = vaultEconomy.getClass().getMethod("withdrawPlayer", Player.class, double.class)
                    .invoke(vaultEconomy, player, amount);
            return (Boolean) response.getClass().getMethod("transactionSuccess").invoke(response);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao retirar dinheiro via Vault: " + e.getMessage());
            return withdrawInternal(playerUuid, amount);
        }
    }

    /**
     * Adiciona dinheiro a um jogador
     */
    public boolean depositMoney(Player player, double amount) {
        return depositMoney(player.getUniqueId(), amount);
    }

    /**
     * Adiciona dinheiro a um jogador por UUID
     */
    public boolean depositMoney(UUID playerUuid, double amount) {
        if (!isVaultEnabled()) {
            depositInternal(playerUuid, amount);
            return true;
        }

        try {
            Player player = Bukkit.getPlayer(playerUuid);
            if (player == null) {
                depositInternal(playerUuid, amount);
                return true;
            }

            Object response = vaultEconomy.getClass().getMethod("depositPlayer", Player.class, double.class)
                    .invoke(vaultEconomy, player, amount);
            return (Boolean) response.getClass().getMethod("transactionSuccess").invoke(response);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao depositar dinheiro via Vault: " + e.getMessage());
            depositInternal(playerUuid, amount);
            return true;
        }
    }

    /**
     * Formata um valor monetário
     */
    public String formatMoney(double amount) {
        if (!isVaultEnabled()) {
            return String.format("%.2f", amount);
        }

        try {
            return (String) vaultEconomy.getClass().getMethod("format", double.class).invoke(vaultEconomy, amount);
        } catch (Exception e) {
            return String.format("%.2f", amount);
        }
    }

    /**
     * Obtém o nome da moeda
     */
    public String getCurrencyName() {
        if (!isVaultEnabled()) {
            return "Dinheiro";
        }

        try {
            return (String) vaultEconomy.getClass().getMethod("currencyNamePlural").invoke(vaultEconomy);
        } catch (Exception e) {
            return "Dinheiro";
        }
    }

    /**
     * Processa a venda de cactos
     */
    public boolean sellCactus(Player player, int amount) {
        double unitValue = plugin.getConfigManager().getUnitCactusValue();
        double totalValue = amount * unitValue;

        if (depositMoney(player, totalValue)) {
            // Atualizar estatísticas do jogador
            plugin.getMemoryDataManager().updatePlayerStats(
                    player.getUniqueId(), 0, amount, totalValue, 0, 0);

            // Enviar mensagem de sucesso
            String message = plugin.getConfigManager().getMessage("cactusSold")
                    .replace("%cactus%", String.valueOf(amount))
                    .replace("%value%", formatMoney(totalValue));
            player.sendMessage(message);

            return true;
        }

        return false;
    }

    /**
     * Processa a compra de um upgrade
     */
    public boolean buyUpgrade(Player player, String upgradeType, int level) {
        double price = plugin.getConfigManager().getUpgradePrice(upgradeType, level);
        String economy = plugin.getConfigManager().getUpgradeEconomy(upgradeType, level);

        if (economy.equalsIgnoreCase("VAULT")) {
            if (hasBalance(player, price)) {
                if (withdrawMoney(player, price)) {
                    String message = plugin.getConfigManager().getMessage("successfulEvolution");
                    player.sendMessage(message);
                    return true;
                } else {
                    String message = plugin.getConfigManager().getMessage("insufficientBalance");
                    player.sendMessage(message);
                    return false;
                }
            } else {
                String message = plugin.getConfigManager().getMessage("insufficientBalance");
                player.sendMessage(message);
                return false;
            }
        } else {
            // TODO: Implementar outros sistemas de economia (CASH, etc.)
            plugin.getLogger().warning("Sistema de economia " + economy + " não implementado!");
            return false;
        }
    }

    /**
     * Processa a compra de um booster
     */
    public boolean buyBooster(Player player, String boosterId) {
        double price = plugin.getConfigManager().getBoosterPrice(boosterId);
        String economy = plugin.getConfigManager().getBoosterEconomy(boosterId);

        if (economy.equalsIgnoreCase("VAULT")) {
            if (hasBalance(player, price)) {
                if (withdrawMoney(player, price)) {
                    String message = plugin.getConfigManager().getMessage("boosterPurchased");
                    player.sendMessage(message);
                    return true;
                } else {
                    String message = plugin.getConfigManager().getMessage("insufficientBalance");
                    player.sendMessage(message);
                    return false;
                }
            } else {
                String message = plugin.getConfigManager().getMessage("insufficientBalance");
                player.sendMessage(message);
                return false;
            }
        } else {
            // TODO: Implementar outros sistemas de economia (CASH, etc.)
            plugin.getLogger().warning("Sistema de economia " + economy + " não implementado!");
            return false;
        }
    }

    /**
     * Obtém informações sobre a economia
     */
    public String getEconomyInfo() {
        if (!isVaultEnabled()) {
            return "§cSistema de economia não disponível";
        }

        try {
            String economyName = (String) vaultEconomy.getClass().getMethod("getName").invoke(vaultEconomy);
            return "§aEconomia: §f" + economyName + " §7| §aMoeda: §f" + getCurrencyName();
        } catch (Exception e) {
            return "§aEconomia: §fVault §7| §aMoeda: §f" + getCurrencyName();
        }
    }
}
