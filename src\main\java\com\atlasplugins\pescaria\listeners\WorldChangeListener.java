package com.atlasplugins.pescaria.listeners;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.items.FishingRodManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerChangedWorldEvent;
import org.bukkit.event.player.PlayerJoinEvent;

public class WorldChangeListener implements Listener {

    private final Pescaria plugin;
    private final FishingRodManager fishingRodManager;

    public WorldChangeListener(Pescaria plugin) {
        this.plugin = plugin;
        this.fishingRodManager = new FishingRodManager(plugin);
    }

    @EventHandler
    public void onWorldChange(PlayerChangedWorldEvent event) {
        Player player = event.getPlayer();
        String currentWorld = player.getWorld().getName();
        String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();

        // Debug logs
        plugin.getLogger().info("=== WORLD CHANGE DEBUG ===");
        plugin.getLogger().info("Player: " + player.getName());
        plugin.getLogger().info("Current World: " + currentWorld);
        plugin.getLogger().info("Pescaria World: " + pescariaWorld);
        plugin.getLogger().info("Is Fishing World: " + currentWorld.equalsIgnoreCase(pescariaWorld));
        plugin.getLogger().info("========================");

        // Se o jogador entrou no mundo de pesca
        if (currentWorld.equalsIgnoreCase(pescariaWorld)) {
            // Dar vara de pesca customizada
            fishingRodManager.giveFishingRod(player);

            // Atualizar scoreboard com delay para garantir que funcione
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                try {
                    plugin.getScoreboardManager().updateScoreboard(player);
                    player.sendMessage("§a§lPESCARIA §fBem-vindo à área de pesca!");
                } catch (Exception e) {
                    plugin.getLogger().warning("Erro ao atualizar scoreboard: " + e.getMessage());
                }
            }, 40L); // 2 segundos de delay
        } else {
            // Jogador saiu do mundo de pesca - remover vara e scoreboard
            fishingRodManager.removeFishingRod(player);

            // Remover scoreboard com delay
            plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                try {
                    plugin.getScoreboardManager().removeScoreboard(player);
                } catch (Exception e) {
                    plugin.getLogger().warning("Erro ao remover scoreboard: " + e.getMessage());
                }
            }, 10L);
        }
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // Verificar se o jogador está no mundo de pesca ao entrar
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            String currentWorld = player.getWorld().getName();
            String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();

            plugin.getLogger().info("=== PLAYER JOIN DEBUG ===");
            plugin.getLogger().info("Player: " + player.getName());
            plugin.getLogger().info("Current World: " + currentWorld);
            plugin.getLogger().info("Pescaria World: " + pescariaWorld);
            plugin.getLogger().info("========================");

            if (currentWorld.equalsIgnoreCase(pescariaWorld)) {
                // Dar vara de pesca
                fishingRodManager.giveFishingRod(player);

                // Atualizar scoreboard
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    plugin.getScoreboardManager().updateScoreboard(player);
                }, 40L);
            }
        }, 20L); // 1 segundo após o join
    }

    public FishingRodManager getFishingRodManager() {
        return fishingRodManager;
    }
}
