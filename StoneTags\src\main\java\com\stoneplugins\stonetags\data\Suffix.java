package com.stoneplugins.stonetags.data;

/**
 * Representa um suffix no sistema StoneTags
 */
public class Suffix {
    
    private final String id;
    private final String name;
    private final String displayName;
    private final String chatSuffix;
    private final String tabSuffix;
    private final String aboveName;
    private final boolean requiresPermission;
    private final String permission;
    private final int priority;
    private final boolean isInvisible;
    private final boolean isAnimated;
    private final String[] animationFrames;
    private final int animationSpeed;
    
    public Suffix(String id, String name, String displayName, String chatSuffix, 
                  String tabSuffix, String aboveName, boolean requiresPermission, 
                  String permission, int priority, boolean isInvisible, 
                  boolean isAnimated, String[] animationFrames, int animationSpeed) {
        this.id = id;
        this.name = name;
        this.displayName = displayName;
        this.chatSuffix = chatSuffix;
        this.tabSuffix = tabSuffix;
        this.aboveName = aboveName;
        this.requiresPermission = requiresPermission;
        this.permission = permission;
        this.priority = priority;
        this.isInvisible = isInvisible;
        this.isAnimated = isAnimated;
        this.animationFrames = animationFrames;
        this.animationSpeed = animationSpeed;
    }
    
    // Getters
    public String getId() { return id; }
    public String getName() { return name; }
    public String getDisplayName() { return displayName; }
    public String getChatSuffix() { return chatSuffix; }
    public String getTabSuffix() { return tabSuffix; }
    public String getAboveName() { return aboveName; }
    public boolean requiresPermission() { return requiresPermission; }
    public String getPermission() { return permission; }
    public int getPriority() { return priority; }
    public boolean isInvisible() { return isInvisible; }
    public boolean isAnimated() { return isAnimated; }
    public String[] getAnimationFrames() { return animationFrames; }
    public int getAnimationSpeed() { return animationSpeed; }
    
    /**
     * Obtém o frame atual da animação baseado no tempo
     */
    public String getCurrentAnimationFrame() {
        if (!isAnimated || animationFrames == null || animationFrames.length == 0) {
            return chatSuffix;
        }
        
        long currentTime = System.currentTimeMillis();
        int frameIndex = (int) ((currentTime / (animationSpeed * 50)) % animationFrames.length);
        return animationFrames[frameIndex];
    }
    
    /**
     * Obtém o suffix para chat (com animação se aplicável)
     */
    public String getDisplaySuffix() {
        return isAnimated ? getCurrentAnimationFrame() : chatSuffix;
    }
    
    @Override
    public String toString() {
        return "Suffix{id='" + id + "', name='" + name + "', priority=" + priority + "}";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Suffix suffix = (Suffix) obj;
        return id.equals(suffix.id);
    }
    
    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
