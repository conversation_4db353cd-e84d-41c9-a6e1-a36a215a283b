package com.atlasplugins.pescaria.integrations;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.lang.reflect.Method;

public class SCashIntegration {

    private final Pescaria plugin;
    private boolean enabled = false;
    private Plugin sCashPlugin;
    private Object sCashAPI;

    public SCashIntegration(Pescaria plugin) {
        this.plugin = plugin;
        setupSCash();
    }

    private void setupSCash() {
        sCashPlugin = Bukkit.getPluginManager().getPlugin("sCash");
        if (sCashPlugin != null && sCashPlugin.isEnabled()) {
            try {
                // Tentar obter a API do sCash
                Class<?> sCashClass = Class.forName("me.aglerr.sCash.api.sCashAPI");
                sCashAPI = sCashClass.newInstance();
                enabled = true;
                plugin.getLogger().info("Integração com sCash ativada!");
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao conectar com sCash: " + e.getMessage());
            }
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

    public double getBalance(Player player) {
        if (!enabled) return 0.0;

        try {
            Method getBalanceMethod = sCashAPI.getClass().getMethod("getBalance", Player.class);
            Object result = getBalanceMethod.invoke(sCashAPI, player);
            return result instanceof Number ? ((Number) result).doubleValue() : 0.0;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao obter saldo do sCash: " + e.getMessage());
            return 0.0;
        }
    }

    public boolean addBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method addBalanceMethod = sCashAPI.getClass().getMethod("addBalance", Player.class, double.class);
            addBalanceMethod.invoke(sCashAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao adicionar saldo no sCash: " + e.getMessage());
            return false;
        }
    }

    public boolean removeBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method removeBalanceMethod = sCashAPI.getClass().getMethod("removeBalance", Player.class, double.class);
            removeBalanceMethod.invoke(sCashAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao remover saldo do sCash: " + e.getMessage());
            return false;
        }
    }

    public boolean setBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method setBalanceMethod = sCashAPI.getClass().getMethod("setBalance", Player.class, double.class);
            setBalanceMethod.invoke(sCashAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao definir saldo do sCash: " + e.getMessage());
            return false;
        }
    }

    public boolean hasBalance(Player player, double amount) {
        if (!enabled) return false;
        return getBalance(player) >= amount;
    }

    public String formatBalance(double amount) {
        if (!enabled) return String.valueOf(amount);

        try {
            Method formatMethod = sCashAPI.getClass().getMethod("formatBalance", double.class);
            Object result = formatMethod.invoke(sCashAPI, amount);
            return result != null ? result.toString() : String.valueOf(amount);
        } catch (Exception e) {
            // Se não conseguir formatar, retornar valor simples
            return String.valueOf(amount);
        }
    }

    public String getCurrencySymbol() {
        if (!enabled) return "$";

        try {
            Method getSymbolMethod = sCashAPI.getClass().getMethod("getCurrencySymbol");
            Object result = getSymbolMethod.invoke(sCashAPI);
            return result != null ? result.toString() : "$";
        } catch (Exception e) {
            return "$";
        }
    }

    // Métodos para integração com o sistema de pesca
    public boolean sellFishForCash(Player player, int fishAmount, double pricePerFish) {
        if (!enabled) return false;

        double totalPrice = fishAmount * pricePerFish;
        
        if (addBalance(player, totalPrice)) {
            player.sendMessage("§a§lPESCARIA §fVocê vendeu §e" + fishAmount + " §fpeixes por §6" + 
                getCurrencySymbol() + formatBalance(totalPrice) + " §fde cash!");
            return true;
        }
        
        return false;
    }

    public boolean buyItemWithCash(Player player, double price, String itemName) {
        if (!enabled) return false;

        if (!hasBalance(player, price)) {
            player.sendMessage("§c§lPESCARIA §fVocê não tem cash suficiente! Necessário: §6" + 
                getCurrencySymbol() + formatBalance(price));
            return false;
        }

        if (removeBalance(player, price)) {
            player.sendMessage("§a§lPESCARIA §fVocê comprou §e" + itemName + " §fpor §6" + 
                getCurrencySymbol() + formatBalance(price) + " §fde cash!");
            return true;
        }

        return false;
    }

    // Placeholder para integração com PlaceholderAPI
    public String getBalanceFormatted(Player player) {
        if (!enabled) return "0";
        return formatBalance(getBalance(player));
    }

    public String getBalanceWithSymbol(Player player) {
        if (!enabled) return "$0";
        return getCurrencySymbol() + formatBalance(getBalance(player));
    }
}
