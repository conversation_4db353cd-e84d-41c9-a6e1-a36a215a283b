package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class TagSetCommand implements CommandExecutor {

    private final StoneTags plugin;

    public TagSetCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Verificar permissão de admin
        if (!sender.hasPermission("stonetags.admin.tagset")) {
            plugin.getMessageUtils().sendConfigMessage(sender, "noPermission");
            return true;
        }

        // Verificar argumentos
        if (args.length < 2) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /tagset <jogador> <tag>"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Exemplo: /tagset BonecaAmbalabu vip"));
            return true;
        }

        String playerName = args[0];
        String tagId = args[1];

        // Buscar jogador
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cJogador '" + playerName + "' não encontrado ou offline!"));
            return true;
        }

        // Verificar se a tag existe
        Tag tag = plugin.getTagManager().getTag(tagId);
        if (tag == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cTag '" + tagId + "' não encontrada!"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Tags disponíveis: " + 
                String.join(", ", plugin.getTagManager().getTagIds())));
            return true;
        }

        // Definir tag para o jogador
        plugin.getPlayerDataManager().setSelectedTag(target, tagId);

        // Mensagens de sucesso
        String tagDisplay = plugin.getMessageUtils().colorize(tag.getCurrentPrefix());
        
        sender.sendMessage(plugin.getMessageUtils().colorize(
            "&aTag " + tagDisplay + "&a definida para &f" + target.getName() + "&a!"
        ));
        
        target.sendMessage(plugin.getMessageUtils().colorize(
            "&aUm administrador definiu sua tag como: " + tagDisplay
        ));

        // Log da ação
        plugin.getLogger().info("[ADMIN] " + sender.getName() + " definiu a tag '" + tagId + 
                               "' para o jogador " + target.getName());

        return true;
    }
}
