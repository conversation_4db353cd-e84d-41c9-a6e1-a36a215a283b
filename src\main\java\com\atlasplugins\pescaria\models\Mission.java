package com.atlasplugins.pescaria.models;

import java.util.Map;

public class Mission {

    private final String id;
    private final int ordem;
    private final String name;
    private final int peixesToComplete;
    private final boolean recompensasAtivar;
    private final Map<String, String> recompensas;

    public Mission(String id, int ordem, String name, int peixesToComplete, boolean recompensasAtivar, Map<String, String> recompensas) {
        this.id = id;
        this.ordem = ordem;
        this.name = name;
        this.peixesToComplete = peixesToComplete;
        this.recompensasAtivar = recompensasAtivar;
        this.recompensas = recompensas;
    }

    public String getId() {
        return id;
    }

    public int getOrdem() {
        return ordem;
    }

    public String getName() {
        return name;
    }

    public int getPeixesToComplete() {
        return peixesToComplete;
    }

    public boolean isRecompensasAtivar() {
        return recompensasAtivar;
    }

    public Map<String, String> getRecompensas() {
        return recompensas;
    }

    @Override
    public String toString() {
        return "Mission{" +
                "id='" + id + '\'' +
                ", ordem=" + ordem +
                ", name='" + name + '\'' +
                ", peixesToComplete=" + peixesToComplete +
                ", recompensasAtivar=" + recompensasAtivar +
                ", recompensas=" + recompensas +
                '}';
    }
}