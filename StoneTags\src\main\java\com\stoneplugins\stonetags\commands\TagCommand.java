package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import com.stoneplugins.stonetags.gui.TagsMenu;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class TagCommand implements CommandExecutor {

    private final StoneTags plugin;

    public TagCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerOnly");
            return true;
        }

        Player player = (Player) sender;

        // Comando de reload para admins
        if (args.length > 0 && args[0].equalsIgnoreCase("reload")) {
            if (!player.hasPermission("stonetags.admin")) {
                plugin.getMessageUtils().sendConfigMessage(player, "noPermission");
                return true;
            }

            plugin.reloadConfig();
            plugin.getConfigManager().reloadConfig();
            player.sendMessage(plugin.getMessageUtils().colorize("&aConfiguração recarregada!"));
            player.sendMessage(plugin.getMessageUtils()
                    .colorize("&7Formato do chat: " + plugin.getConfigManager().getChatFormat()));
            return true;
        }

        // Verificar permissão
        if (!player.hasPermission("stonetags.use")) {
            plugin.getMessageUtils().sendConfigMessage(player, "noPermission");
            return true;
        }

        // /tag sempre abre o menu
        openTagsMenu(player);
        return true;
    }

    private void openTagsMenu(Player player) {
        TagsMenu menu = new TagsMenu(plugin, player, 1);
        menu.open();
    }

    private void listAvailableTags(Player player) {
        List<Tag> availableTags = plugin.getTagManager().getAvailableTags(player);

        if (availableTags.isEmpty()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não tem acesso a nenhuma tag!"));
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils()
                .colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lTAGS DISPONÍVEIS"));
        player.sendMessage("");

        for (Tag tag : availableTags) {
            String tagDisplay = plugin.getMessageUtils().colorize(tag.getChatPrefix());

            player.sendMessage(plugin.getMessageUtils().colorize(
                    "&8• &f" + tag.getName() + " " + tagDisplay + " &8- &7/tag " + tag.getId()));
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&7Use &f/tag <nome> &7para selecionar uma tag"));
        player.sendMessage(plugin.getMessageUtils().colorize("&7Use &f/tag remove &7para remover sua tag"));
    }

    private void setTag(Player player, String tagId) {
        Tag tag = plugin.getTagManager().getTag(tagId);

        if (tag == null) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cTag '" + tagId + "' não encontrada!"));
            player.sendMessage(plugin.getMessageUtils().colorize("&7Use &f/tag list &7para ver tags disponíveis"));
            return;
        }

        if (!plugin.getTagManager().canUseTag(player, tag)) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagNoPermission");
            return;
        }

        // Verificar se já está usando esta tag
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
        if (currentTag != null && currentTag.getId().equals(tagId)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê já está usando esta tag!"));
            return;
        }

        // Definir tag
        plugin.getPlayerDataManager().setSelectedTag(player, tagId);

        // Mensagem de sucesso
        String tagDisplay = plugin.getMessageUtils().colorize(tag.getChatPrefix());
        player.sendMessage(plugin.getMessageUtils().colorize("&aTag alterada para: " + tagDisplay));
    }

    private void removeTag(Player player) {
        if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedTag(player);
        player.sendMessage(plugin.getMessageUtils().colorize("&aSua tag foi removida!"));
    }

    private void showCurrentTag(Player player) {
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);

        if (currentTag == null) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lSUA TAG ATUAL"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: &7" + currentTag.getName()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag: " + currentTag.getChatPrefix()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPrioridade: &7" + currentTag.getPosition()));
        if (currentTag.isAnimated()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fAnimada: &aSimm"));
        }
        player.sendMessage("");
    }

    private void showTagInfo(Player player, String tagId) {
        Tag tag = plugin.getTagManager().getTag(tagId);

        if (tag == null) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cTag '" + tagId + "' não encontrada!"));
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lINFORMAÇÕES DA TAG"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fID: &7" + tag.getId()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: &7" + tag.getName()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag: " + tag.getChatPrefix()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPrioridade: &7" + tag.getPosition()));

        if (tag.hasPermissionRequirement()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7" + tag.getPermission()));
            boolean hasPermission = player.hasPermission(tag.getPermission());
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fVocê pode usar: " +
                    (hasPermission ? "&aSimm" : "&cNão")));
        } else {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7Nenhuma"));
        }

        if (tag.isAnimated()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fAnimada: &aSimm"));
        }

        player.sendMessage("");
    }

    private void showHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lCOMANDOS DE TAG"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag &8- &7Abrir menu ou listar tags"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag <nome> &8- &7Usar uma tag"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag set <nome> &8- &7Definir uma tag"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag remove &8- &7Remover tag atual"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag list &8- &7Listar tags disponíveis"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag menu &8- &7Abrir menu"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/tag info [tag] &8- &7Ver informações"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&7Comandos de suffix:"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix &8- &7Gerenciar sufixos"));
        player.sendMessage("");
    }
}
