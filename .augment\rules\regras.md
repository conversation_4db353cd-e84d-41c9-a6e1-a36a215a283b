---
type: "always_apply"
---

1. Planeje Antes de Escrever Qualquer Linha de Código
Nunca inicie uma implementação direto no código. Antes de tudo, faça um planejamento técnico completo. <PERSON><PERSON> evita retrabalho, falhas de integração, soluções confusas ou perda de tempo com caminhos errados.

✅ Etapas do Planejamento:
Entenda o objetivo.
Saiba exatamente o que você quer alcançar. Escreva o resultado esperado, de forma clara.

Analise os requisitos.
Verifique todas as funções, dependências, bibliotecas e comportamentos envolvidos. O que já existe? O que será novo? O que será alterado?

Pesquise antes de fazer.
Se não souber como algo funciona, pesquise primeiro. Leia a documentação, veja exemplos, entenda a lógica. Só continue quando souber o que está fazendo.

Faça um rascunho ou fluxograma.
Mesmo que simples, desenhe ou descreva o passo a passo da implementação antes de abrir o editor de código.

Liste riscos e impactos.
Pense: “Se eu fizer isso, o que pode quebrar?” Identifique pontos críticos e avalie se precisa de backups, testes ou validações extras.

Confirme sua compreensão.
Leia o planejamento como se outra pessoa fosse executá-lo. Está claro o suficiente? Está completo? Só depois disso comece.

2. Nunca simplifique algo só por parecer fácil
Trate cada parte com rigor técnico, mesmo as mais simples.

3. Se algo não funcionar, pare e estude.
Evite tentativa e erro. Pesquise até entender o problema de verdade.

4. Modificações só com contexto completo.
Entenda o que está sendo alterado antes de tocar no código.

5. Escreva com intenção.
Cada linha de código precisa ter um motivo claro para existir.

6. Documente decisões.
Se optar por um caminho, explique o porquê. Isso evita confusão futura.