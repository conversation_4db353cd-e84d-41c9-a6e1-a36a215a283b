package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Suffix;
import com.stoneplugins.stonetags.gui.SuffixMenu;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class SuffixCommand implements CommandExecutor {

    private final StoneTags plugin;

    public SuffixCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerOnly");
            return true;
        }

        Player player = (Player) sender;

        // Verificar permissão
        if (!player.hasPermission("stonetags.suffix.use")) {
            plugin.getMessageUtils().sendConfigMessage(player, "noPermission");
            return true;
        }

        // Se não há argumentos, abrir menu ou listar sufixos
        if (args.length == 0) {
            if (plugin.getConfigManager().useMenu()) {
                openSuffixMenu(player);
            } else {
                listAvailableSuffixes(player);
            }
            return true;
        }

        // Comandos com argumentos
        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "menu":
            case "gui":
                openSuffixMenu(player);
                break;

            case "list":
            case "listar":
                listAvailableSuffixes(player);
                break;

            case "set":
            case "usar":
            case "selecionar":
                if (args.length < 2) {
                    player.sendMessage(plugin.getMessageUtils().colorize("&cUso: /suffix set <suffix>"));
                    return true;
                }
                setSuffix(player, args[1]);
                break;

            case "remove":
            case "remover":
            case "clear":
                removeSuffix(player);
                break;

            case "info":
                if (args.length < 2) {
                    showCurrentSuffix(player);
                } else {
                    showSuffixInfo(player, args[1]);
                }
                break;

            case "help":
            case "ajuda":
                showHelp(player);
                break;

            default:
                // Tentar usar como nome de suffix
                setSuffix(player, args[0]);
                break;
        }

        return true;
    }

    private void openSuffixMenu(Player player) {
        SuffixMenu menu = new SuffixMenu(plugin, player, 1);
        menu.open();
    }

    private void listAvailableSuffixes(Player player) {
        List<Suffix> availableSuffixes = plugin.getSuffixManager().getAvailableSuffixes(player);

        if (availableSuffixes.isEmpty()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não tem acesso a nenhum suffix!"));
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lSUFFIXOS DISPONÍVEIS"));
        player.sendMessage("");

        for (Suffix suffix : availableSuffixes) {
            String suffixDisplay = plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
            String clickCommand = "/suffix " + suffix.getId();
            
            player.sendMessage(plugin.getMessageUtils().colorize(
                "&8• &f" + suffix.getName() + " " + suffixDisplay + " &8- &7Clique para usar"
            ));
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&7Use &f/suffix set <suffix> &7para selecionar um suffix"));
    }

    private void setSuffix(Player player, String suffixId) {
        Suffix suffix = plugin.getSuffixManager().getSuffix(suffixId);

        if (suffix == null) {
            plugin.getMessageUtils().sendConfigMessage(player, "suffixNotFound");
            return;
        }

        if (!plugin.getSuffixManager().canUseSuffix(player, suffix)) {
            plugin.getMessageUtils().sendConfigMessage(player, "suffixNoPermission");
            return;
        }

        // Verificar se já está usando este suffix
        Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
        if (currentSuffix != null && currentSuffix.getId().equals(suffixId)) {
            plugin.getMessageUtils().sendConfigMessage(player, "suffixAlreadySelected");
            return;
        }

        // Definir suffix
        plugin.getPlayerDataManager().setSelectedSuffix(player, suffixId);

        // Mensagem de sucesso
        plugin.getMessageUtils().sendConfigMessage(player, "suffixSelected",
                "{suffix}", plugin.getMessageUtils().colorize(suffix.getName()));
    }

    private void removeSuffix(Player player) {
        if (!plugin.getPlayerDataManager().hasSelectedSuffix(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhum suffix!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedSuffix(player);
        plugin.getMessageUtils().sendConfigMessage(player, "suffixRemoved");
    }

    private void showCurrentSuffix(Player player) {
        Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
        
        if (currentSuffix == null) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhum suffix!"));
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lSEU SUFFIX ATUAL"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: &7" + currentSuffix.getName()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fSuffix: " + currentSuffix.getDisplaySuffix()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPrioridade: &7" + currentSuffix.getPriority()));
        if (currentSuffix.isAnimated()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fAnimado: &aSimm"));
        }
        player.sendMessage("");
    }

    private void showSuffixInfo(Player player, String suffixId) {
        Suffix suffix = plugin.getSuffixManager().getSuffix(suffixId);
        
        if (suffix == null) {
            plugin.getMessageUtils().sendConfigMessage(player, "suffixNotFound");
            return;
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lINFORMAÇÕES DO SUFFIX"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fID: &7" + suffix.getId()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: &7" + suffix.getName()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fSuffix: " + suffix.getDisplaySuffix()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPrioridade: &7" + suffix.getPriority()));
        
        if (suffix.requiresPermission()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7" + suffix.getPermission()));
            boolean hasPermission = player.hasPermission(suffix.getPermission());
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fVocê pode usar: " + 
                (hasPermission ? "&aSimm" : "&cNão")));
        } else {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7Nenhuma"));
        }
        
        if (suffix.isAnimated()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fAnimado: &aSimm"));
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fVelocidade: &7" + suffix.getAnimationSpeed() + "ms"));
        }
        
        player.sendMessage("");
    }

    private void showHelp(Player player) {
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f&lCOMANDOS DE SUFFIX"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix &8- &7Abrir menu ou listar sufixos"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix <suffix> &8- &7Usar um suffix"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix set <suffix> &8- &7Definir um suffix"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix remove &8- &7Remover suffix atual"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix list &8- &7Listar sufixos disponíveis"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix info [suffix] &8- &7Ver informações"));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &f/suffix menu &8- &7Abrir menu"));
        player.sendMessage("");
    }
}
