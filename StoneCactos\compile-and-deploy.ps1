# Script PowerShell para compilar e deployar StoneCactos
param(
    [switch]$StopServer = $false,
    [switch]$StartServer = $false,
    [switch]$Force = $false
)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Compilando e Deployando StoneCactos" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

# Definir caminhos
$ProjectDir = $PSScriptRoot
$ServerDir = Join-Path (Split-Path $ProjectDir -Parent) "servidor"
$PluginsDir = Join-Path $ServerDir "plugins"
$TargetJar = Join-Path $ProjectDir "target\stonecactos-1.0.jar"
$ServerJar = Join-Path $PluginsDir "stonecactos-1.0.jar"
$ServerJarNew = Join-Path $PluginsDir "stonecactos-1.0-new.jar"
$ServerStartScript = Join-Path $ServerDir "start_server.bat"

Write-Host "Diretório do projeto: $ProjectDir" -ForegroundColor Gray
Write-Host "Diretório do servidor: $ServerDir" -ForegroundColor Gray

# Função para verificar se o servidor está rodando
function Test-ServerRunning {
    $processes = Get-Process | Where-Object { $_.ProcessName -like "*java*" -and $_.MainWindowTitle -like "*minecraft*" }
    return $processes.Count -gt 0
}

# Função para parar o servidor
function Stop-MinecraftServer {
    Write-Host "Tentando parar o servidor Minecraft..." -ForegroundColor Yellow
    
    $processes = Get-Process | Where-Object { 
        $_.ProcessName -like "*java*" -and 
        ($_.MainWindowTitle -like "*minecraft*" -or $_.CommandLine -like "*spigot*" -or $_.CommandLine -like "*paper*")
    }
    
    if ($processes.Count -gt 0) {
        Write-Host "Encontrados $($processes.Count) processos do servidor" -ForegroundColor Yellow
        foreach ($process in $processes) {
            try {
                $process.CloseMainWindow()
                Start-Sleep -Seconds 2
                if (!$process.HasExited) {
                    $process.Kill()
                }
                Write-Host "Processo $($process.Id) finalizado" -ForegroundColor Green
            }
            catch {
                Write-Host "Erro ao finalizar processo $($process.Id): $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        Start-Sleep -Seconds 3
        return $true
    }
    else {
        Write-Host "Nenhum processo do servidor encontrado" -ForegroundColor Green
        return $false
    }
}

# Compilar o plugin
Write-Host "`n[1/4] Compilando plugin..." -ForegroundColor Yellow

# Tentar usar Maven primeiro
$mavenFound = $false
$mavenCommands = @("mvn", "mvn.cmd", "C:\Program Files\Apache\maven\bin\mvn.cmd")

foreach ($mvnCmd in $mavenCommands) {
    try {
        $null = Get-Command $mvnCmd -ErrorAction Stop
        Write-Host "Maven encontrado: $mvnCmd" -ForegroundColor Green
        
        & $mvnCmd clean package -q
        if ($LASTEXITCODE -eq 0) {
            $mavenFound = $true
            Write-Host "Compilação Maven concluída com sucesso!" -ForegroundColor Green
            break
        }
    }
    catch {
        continue
    }
}

# Se Maven falhou, usar compilação manual
if (!$mavenFound -or !(Test-Path $TargetJar)) {
    Write-Host "Maven não disponível ou falhou. Usando compilação manual..." -ForegroundColor Yellow
    
    $compileScript = Join-Path $ProjectDir "compile_fixed.bat"
    if (Test-Path $compileScript) {
        & cmd /c "`"$compileScript`""
    }
    else {
        Write-Host "ERRO: Script de compilação não encontrado!" -ForegroundColor Red
        exit 1
    }
}

# Verificar se o JAR foi gerado
if (!(Test-Path $TargetJar)) {
    Write-Host "ERRO: Arquivo JAR não foi gerado!" -ForegroundColor Red
    exit 1
}

$jarSize = (Get-Item $TargetJar).Length
Write-Host "Plugin compilado: $TargetJar ($jarSize bytes)" -ForegroundColor Green

# Verificar se o servidor está rodando
Write-Host "`n[2/4] Verificando status do servidor..." -ForegroundColor Yellow
$serverRunning = Test-ServerRunning

if ($serverRunning) {
    Write-Host "Servidor Minecraft detectado em execução" -ForegroundColor Yellow
    
    if ($StopServer -or $Force) {
        $stopped = Stop-MinecraftServer
        $serverRunning = !$stopped
    }
    else {
        Write-Host "Use -StopServer para parar automaticamente ou -Force para forçar" -ForegroundColor Yellow
    }
}
else {
    Write-Host "Servidor não está rodando" -ForegroundColor Green
}

# Fazer backup do plugin antigo
Write-Host "`n[3/4] Fazendo deploy do plugin..." -ForegroundColor Yellow

if (Test-Path $ServerJar) {
    $backupPath = Join-Path $PluginsDir "stonecactos-1.0-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss').jar"
    try {
        Copy-Item $ServerJar $backupPath -Force
        Write-Host "Backup criado: $(Split-Path $backupPath -Leaf)" -ForegroundColor Green
    }
    catch {
        Write-Host "Aviso: Não foi possível criar backup: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

# Copiar novo plugin
$copySuccess = $false

if (!$serverRunning -or $Force) {
    # Tentar copiar diretamente
    try {
        Copy-Item $TargetJar $ServerJar -Force
        $copySuccess = $true
        Write-Host "Plugin copiado diretamente para o servidor!" -ForegroundColor Green
    }
    catch {
        Write-Host "Falha na cópia direta: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

if (!$copySuccess) {
    # Copiar com nome temporário
    try {
        Copy-Item $TargetJar $ServerJarNew -Force
        Write-Host "Plugin copiado como arquivo temporário: stonecactos-1.0-new.jar" -ForegroundColor Yellow
        Write-Host "INSTRUÇÕES MANUAIS:" -ForegroundColor Cyan
        Write-Host "1. Pare o servidor se estiver rodando" -ForegroundColor White
        Write-Host "2. Remova: stonecactos-1.0.jar" -ForegroundColor White
        Write-Host "3. Renomeie: stonecactos-1.0-new.jar -> stonecactos-1.0.jar" -ForegroundColor White
        Write-Host "4. Inicie o servidor" -ForegroundColor White
    }
    catch {
        Write-Host "ERRO: Falha ao copiar plugin: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "Plugin disponível em: $TargetJar" -ForegroundColor Yellow
        Write-Host "Copie manualmente para: $ServerJar" -ForegroundColor Yellow
    }
}

# Iniciar servidor se solicitado
if ($StartServer -and (Test-Path $ServerStartScript)) {
    Write-Host "`n[4/4] Iniciando servidor..." -ForegroundColor Yellow
    Start-Process -FilePath $ServerStartScript -WorkingDirectory $ServerDir
    Write-Host "Servidor iniciado!" -ForegroundColor Green
}

Write-Host "`n========================================" -ForegroundColor Cyan
Write-Host "    DEPLOY CONCLUÍDO!" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan

if ($copySuccess) {
    Write-Host "✅ Plugin atualizado com sucesso no servidor" -ForegroundColor Green
}
else {
    Write-Host "⚠️  Plugin compilado, mas requer ação manual" -ForegroundColor Yellow
}

Write-Host "`nUso do script:" -ForegroundColor Gray
Write-Host "  .\compile-and-deploy.ps1                 # Compilar apenas" -ForegroundColor Gray
Write-Host "  .\compile-and-deploy.ps1 -StopServer     # Parar servidor e copiar" -ForegroundColor Gray
Write-Host "  .\compile-and-deploy.ps1 -Force          # Forçar cópia mesmo com servidor rodando" -ForegroundColor Gray
Write-Host "  .\compile-and-deploy.ps1 -StopServer -StartServer  # Ciclo completo" -ForegroundColor Gray
