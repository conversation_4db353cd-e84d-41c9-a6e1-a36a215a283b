package com.stoneplugins.spawnershop.gui;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.data.Spawner;
import com.stoneplugins.spawnershop.listeners.PlayerChatListener;
import com.stoneplugins.spawnershop.utils.ItemUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.scheduler.BukkitTask;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

public class MainMenu {

    private final SpawnerShop plugin;
    private final Player player;
    private final int page;
    private final int maxPage;

    private final List<Spawner> spawners;
    private BukkitTask updateTask;
    private Inventory currentInventory;

    public MainMenu(SpawnerShop plugin, Player player) {
        this(plugin, player, 1);
    }

    public MainMenu(SpawnerShop plugin, Player player, int page) {
        this.plugin = plugin;
        this.player = player;
        this.page = page;

        // Obter spawners na ordem correta
        this.spawners = plugin.getSpawnerManager().getSpawners();

        // Calcular páginas
        String slotsConfig = plugin.getConfigManager().getMenuSlots("main");
        String[] slots = slotsConfig.split(",");
        int itemsPerPage = slots.length;
        this.maxPage = (int) Math.ceil((double) spawners.size() / itemsPerPage);
    }

    public void open() {
        String title = plugin.getConfigManager().getMenuName("main");
        int size = plugin.getConfigManager().getMenuSize("main");

        Inventory inventory = Bukkit.createInventory(null, size, plugin.getMessageUtils().colorize(title));

        // Preencher com vidro cinza
        ItemStack glass = ItemUtils.createGlassPane(7, " ");
        for (int i = 0; i < size; i++) {
            inventory.setItem(i, glass);
        }

        // Adicionar spawners
        addSpawners(inventory);

        // Adicionar itens especiais
        addProfileItem(inventory);
        addRankingItem(inventory);

        // Adicionar navegação
        addNavigationItems(inventory);

        this.currentInventory = inventory;

        // Registrar este menu no listener para gerenciar timers
        plugin.getInventoryListener().registerMenu(player, this);

        player.openInventory(inventory);
    }

    public void cleanup() {
        if (updateTask != null) {
            updateTask.cancel();
        }
    }

    private void addSpawners(Inventory inventory) {
        String slotsConfig = plugin.getConfigManager().getMenuSlots("main");
        String[] slots = slotsConfig.split(",");

        int itemsPerPage = slots.length;
        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, spawners.size());

        for (int i = startIndex; i < endIndex; i++) {
            Spawner spawner = spawners.get(i);
            int slotIndex = i - startIndex;

            if (slotIndex < slots.length) {
                int slot = Integer.parseInt(slots[slotIndex].trim());
                ItemStack item = createSpawnerItem(spawner);
                inventory.setItem(slot, item);
            }
        }
    }

    private ItemStack createSpawnerItem(Spawner spawner) {
        boolean canBuy = plugin.getSpawnerManager().canPlayerBuySpawner(player, spawner);

        // Verificar se o spawner está bloqueado por data
        if (!spawner.isReleased()) {
            // Spawner bloqueado por data - usar skull de bloqueado
            String blockedSkull = "3ed1aba73f639f4bc42bd48196c715197be2712c3b962c97ebf9e9ed8efa025";
            String name = "&c&l🔒 BLOQUEADO - " + spawner.getTitle();

            List<String> lore = new ArrayList<>();
            lore.add("&c&lSPAWNER BLOQUEADO!");
            lore.add("");
            lore.add("&fEste spawner sera liberado em:");
            lore.add("&e" + spawner.getTimeUntilRelease());
            lore.add("");
            lore.add("&7Data de liberacao: &f" + spawner.getReleaseDate());

            return ItemUtils.createSkull(blockedSkull, name, lore);
        }

        if (!canBuy) {
            // Spawner não disponível por outros motivos (permissão, rank, etc)
            String itemConfig = plugin.getConfigManager().getUnavailableSpawnerItem();
            String name = plugin.getConfigManager().getUnavailableSpawnerName();
            List<String> lore = new ArrayList<>(plugin.getConfigManager().getUnavailableSpawnerLore());

            return ItemUtils.parseItemFromConfig(itemConfig, name, lore);
        }

        // Spawner normal
        String name = spawner.getTitle();
        List<String> lore = new ArrayList<>(plugin.getConfigManager().getSpawnerLore());

        // Substituir placeholders
        double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
        double finalPrice = spawner.getPriceWithDiscount(discount);
        int maxQuantity = plugin.getSpawnerManager().getMaxPurchasableQuantity(player, spawner);
        String groups = plugin.getPlayerDataManager().getPlayerDiscountGroups(player);
        String status = plugin.getSpawnerManager().getSpawnerStatus(player, spawner);

        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);
            line = line.replace("{preco}", plugin.getEconomyManager().formatBalance(finalPrice));
            line = line.replace("{grupos}", groups);
            line = line.replace("{desconto}", String.valueOf((int) discount));
            line = line.replace("{geradores}", String.valueOf(maxQuantity));
            line = line.replace("{status}", status);
            lore.set(i, line);
        }

        ItemStack item = ItemUtils.parseItemFromConfig(spawner.getSkull(), name, lore);

        // Adicionar glow se disponível
        if (canBuy) {
            item = ItemUtils.addGlow(item);
        }

        return item;
    }

    private void addProfileItem(Inventory inventory) {
        int slot = plugin.getConfigManager().getProfileSlot();
        String name = plugin.getConfigManager().getProfileName();
        List<String> lore = new ArrayList<>(plugin.getConfigManager().getProfileLore());

        // Substituir placeholders
        for (int i = 0; i < lore.size(); i++) {
            lore.set(i, plugin.getMessageUtils().replacePlaceholders(lore.get(i), player));
        }

        ItemStack item = ItemUtils.createPlayerSkull(player, name, lore);
        inventory.setItem(slot, item);
    }

    private void addRankingItem(Inventory inventory) {
        int slot = plugin.getConfigManager().getRankingSlot();
        String itemConfig = plugin.getConfigManager().getRankingItem();
        String name = plugin.getConfigManager().getRankingName();
        List<String> lore = new ArrayList<>(plugin.getConfigManager().getRankingLore());

        ItemStack item = ItemUtils.parseItemFromConfig(itemConfig, name, lore);
        inventory.setItem(slot, item);
    }

    private void addNavigationItems(Inventory inventory) {
        if (maxPage > 1) {
            // Seta para página anterior
            if (page > 1) {
                int backSlot = plugin.getConfigManager().getMenuArrowBackPage("main");
                ItemStack backArrow = ItemUtils.createArrow("&aPágina Anterior",
                        Arrays.asList("&7Clique para ir para a página " + (page - 1)), false);
                inventory.setItem(backSlot, backArrow);
            }

            // Seta para próxima página
            if (page < maxPage) {
                int nextSlot = plugin.getConfigManager().getMenuArrowNextPage("main");
                ItemStack nextArrow = ItemUtils.createArrow("&aPróxima Página",
                        Arrays.asList("&7Clique para ir para a página " + (page + 1)), true);
                inventory.setItem(nextSlot, nextArrow);
            }
        }
    }

    public void handleClick(int slot, ItemStack item, ClickType clickType) {
        // Verificar se clicou em um spawner
        String slotsConfig = plugin.getConfigManager().getMenuSlots("main");
        String[] slots = slotsConfig.split(",");

        for (int i = 0; i < slots.length; i++) {
            int spawnerSlot = Integer.parseInt(slots[i].trim());
            if (slot == spawnerSlot) {
                int spawnerIndex = ((page - 1) * slots.length) + i;
                if (spawnerIndex < spawners.size()) {
                    Spawner spawner = spawners.get(spawnerIndex);
                    handleSpawnerClick(spawner, clickType);
                }
                return;
            }
        }

        // Verificar outros cliques
        if (slot == plugin.getConfigManager().getProfileSlot()) {
            player.closeInventory();
            PlayerChatListener chatListener = plugin.getPlayerChatListener();
            if (chatListener != null) {
                chatListener.addWaitingForMultiplicador(player);
            }
        } else if (slot == plugin.getConfigManager().getRankingSlot()) {
            player.closeInventory();
            new RankingMenu(plugin, player).open();
        } else if (slot == plugin.getConfigManager().getMenuArrowBackPage("main") && page > 1) {
            new MainMenu(plugin, player, page - 1).open();
        } else if (slot == plugin.getConfigManager().getMenuArrowNextPage("main") && page < maxPage) {
            new MainMenu(plugin, player, page + 1).open();
        }
    }

    // Método de compatibilidade
    public void handleClick(int slot, ItemStack item) {
        handleClick(slot, item, ClickType.LEFT);
    }

    private void handleSpawnerClick(Spawner spawner, ClickType clickType) {
        // Verificar se o spawner está liberado por data
        if (!spawner.isReleased()) {
            player.sendMessage(plugin.getMessageUtils().colorize(
                    "&c&l🔒 SPAWNER BLOQUEADO! &fEste spawner sera liberado em: &e" + spawner.getTimeUntilRelease()));
            return;
        }

        if (!plugin.getSpawnerManager().canPlayerBuySpawner(player, spawner)) {
            String status = plugin.getSpawnerManager().getSpawnerStatus(player, spawner);
            player.sendMessage(plugin.getMessageUtils().colorize(status));
            return;
        }

        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
        int multiplicador = playerData.getMultiplicador();

        if (clickType == ClickType.RIGHT) {
            // Botão direito: comprar usando multiplicador
            attemptPurchase(spawner, multiplicador);
        } else if (clickType == ClickType.DROP) {
            // Tecla Q: comprar máximo possível respeitando limite
            int maxQuantity = plugin.getSpawnerManager().getMaxPurchasableQuantityWithLimit(player, spawner);
            if (maxQuantity > 0) {
                attemptPurchase(spawner, maxQuantity);
            } else {
                player.sendMessage(plugin.getMessageUtils().colorize("&c&lVoce nao pode comprar nenhum gerador!"));
            }
        } else {
            // Botão esquerdo: escolher quantidade
            player.closeInventory();
            PlayerChatListener chatListener = plugin.getPlayerChatListener();
            if (chatListener != null) {
                chatListener.addWaitingForQuantity(player, spawner.getId());
            }
        }
    }

    // Método de compatibilidade
    private void handleSpawnerClick(Spawner spawner) {
        handleSpawnerClick(spawner, ClickType.LEFT);
    }

    private void attemptPurchase(Spawner spawner, int quantity) {
        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);

        // Verificar limite por compra (não acumulado)
        if (quantity > playerData.getPurchaseLimit()) {
            player.sendMessage(plugin.getMessageUtils()
                    .colorize("&c&lLIMITE EXCEDIDO! &fVoce so pode comprar ate &a" + playerData.getPurchaseLimit()
                            + "&f geradores por vez."));
            return;
        }

        // Tentar comprar (SpawnerManager já envia mensagem de sucesso)
        boolean success = plugin.getSpawnerManager().purchaseSpawner(player, spawner, quantity);

        // Se a compra foi bem-sucedida, atualizar a GUI em tempo real
        if (success) {
            updateInventoryInRealTime();
        }
    }

    private void updateInventoryInRealTime() {
        // Atualizar o inventário em tempo real após uma compra
        plugin.getServer().getScheduler().runTask(plugin, () -> {
            try {
                // Fechar e reabrir o menu com dados atualizados
                player.closeInventory();
                new MainMenu(plugin, player).open();

            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao atualizar GUI em tempo real: " + e.getMessage());
            }
        });
    }

}
