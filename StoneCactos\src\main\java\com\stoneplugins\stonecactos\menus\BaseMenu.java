package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;

public abstract class BaseMenu {

    protected final StoneCactos plugin;
    protected final Player player;
    protected Inventory inventory;

    public BaseMenu(StoneCactos plugin, Player player) {
        this.plugin = plugin;
        this.player = player;
    }

    /**
     * Abre o menu para o jogador
     */
    public void open() {
        createInventory();
        setupItems();
        player.openInventory(inventory);
    }

    /**
     * Cria o inventário do menu
     */
    protected abstract void createInventory();

    /**
     * Configura os itens do menu
     */
    protected abstract void setupItems();

    /**
     * Processa clique no menu
     */
    public abstract void handleClick(int slot, boolean rightClick, boolean shiftClick);

    /**
     * Cria um item personalizado
     */
    protected ItemStack createItem(Material material, String name, String... lore) {
        return createItem(material, (short) 0, name, Arrays.asList(lore));
    }

    /**
     * Cria um item personalizado com data
     */
    protected ItemStack createItem(Material material, short data, String name, List<String> lore) {
        ItemStack item = new ItemStack(material, 1, data);
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            if (name != null) {
                meta.setDisplayName(plugin.getMessageUtils().colorize(name));
            }

            if (lore != null && !lore.isEmpty()) {
                List<String> formattedLore = new java.util.ArrayList<>();
                for (String line : lore) {
                    formattedLore.add(plugin.getMessageUtils().colorize(line));
                }
                meta.setLore(formattedLore);
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    /**
     * Cria um item de vidro colorido para decoração
     */
    protected ItemStack createGlassPane() {
        return createGlassPane((short) 7); // Cinza claro por padrão
    }

    /**
     * Cria um item de vidro colorido específico
     */
    protected ItemStack createGlassPane(short color) {
        ItemStack glass = new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, color);
        ItemMeta meta = glass.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(plugin.getMessageUtils().colorize("§r"));
            glass.setItemMeta(meta);
        }
        return glass;
    }

    /**
     * Preenche slots específicos com vidro
     */
    protected void fillGlassSlots(String slotsConfig) {
        if (slotsConfig == null || slotsConfig.isEmpty()) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        ItemStack glass = createGlassPane();

        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                if (slot >= 0 && slot < inventory.getSize()) {
                    inventory.setItem(slot, glass);
                }
            } catch (NumberFormatException e) {
                // Ignorar slots inválidos
            }
        }
    }

    /**
     * Cria um item baseado na configuração
     */
    protected ItemStack createConfigItem(String menuName, String itemName) {
        String materialConfig = plugin.getConfigManager().getMenuItemMaterial(menuName, itemName);
        String name = plugin.getConfigManager().getMenuItemName(menuName, itemName);
        List<String> lore = plugin.getConfigManager().getMenuItemLore(menuName, itemName);

        // Parse material e data
        Material material;
        short data = 0;

        if (materialConfig.contains(":")) {
            String[] parts = materialConfig.split(":");
            material = Material.valueOf(parts[0]);
            data = Short.parseShort(parts[1]);
        } else {
            material = Material.valueOf(materialConfig);
        }

        return createItem(material, data, name, lore);
    }

    /**
     * Obtém o slot de um item da configuração
     */
    protected int getConfigSlot(String menuName, String itemName) {
        return plugin.getConfigManager().getMenuItemSlot(menuName, itemName);
    }

    /**
     * Fecha o menu
     */
    public void close() {
        player.closeInventory();
    }

    /**
     * Atualiza o menu
     */
    public void update() {
        setupItems();
    }

    /**
     * Verifica se um slot está dentro dos limites do inventário
     */
    protected boolean isValidSlot(int slot) {
        return slot >= 0 && slot < inventory.getSize();
    }

    /**
     * Define um item em um slot específico
     */
    protected void setItem(int slot, ItemStack item) {
        if (isValidSlot(slot)) {
            inventory.setItem(slot, item);
        }
    }

    /**
     * Obtém o inventário do menu
     */
    public Inventory getInventory() {
        return inventory;
    }

    /**
     * Obtém o jogador do menu
     */
    public Player getPlayer() {
        return player;
    }

    /**
     * Formata números grandes
     */
    protected String formatNumber(long number) {
        if (number >= 1000000000) {
            return String.format("%.1fB", number / 1000000000.0);
        } else if (number >= 1000000) {
            return String.format("%.1fM", number / 1000000.0);
        } else if (number >= 1000) {
            return String.format("%.1fK", number / 1000.0);
        } else {
            return String.valueOf(number);
        }
    }

    /**
     * Formata dinheiro
     */
    protected String formatMoney(double money) {
        // return plugin.getEconomyManager().formatMoney(money);
        return String.format("$%.2f", money); // Temporário: formatação simples
    }

    /**
     * Substitui placeholders em uma string
     */
    protected String replacePlaceholders(String text, String... replacements) {
        String result = text;
        for (int i = 0; i < replacements.length; i += 2) {
            if (i + 1 < replacements.length) {
                result = result.replace(replacements[i], replacements[i + 1]);
            }
        }
        return result;
    }

    /**
     * Substitui placeholders em uma lista de strings
     */
    protected List<String> replacePlaceholders(List<String> lines, String... replacements) {
        List<String> result = new java.util.ArrayList<>();
        for (String line : lines) {
            result.add(replacePlaceholders(line, replacements));
        }
        return result;
    }

    /**
     * Configura o botão de voltar padrão
     */
    protected void setupBackButton() {
        setupBackButton(31); // Slot padrão
    }

    /**
     * Configura o botão de voltar em um slot específico
     */
    protected void setupBackButton(int slot) {
        ItemStack backButton = createItem(Material.ARROW, "&c⬅ Voltar", "&7Clique para voltar ao menu principal");
        setItem(slot, backButton);
    }

    /**
     * Trata o clique no botão de voltar (deve ser sobrescrito pelos menus filhos)
     */
    protected void handleBackClick() {
        player.closeInventory();
    }
}
