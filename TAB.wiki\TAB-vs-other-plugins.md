# ---------------------------------------
# THIS PAGE IS A WORK IN PROGRESS
# ---------------------------------------

# About
This page compares TAB to other plugins offering similar features based on various parameters.

### Open issues
Number of open issues on GitHub. Keep in mind this number shouldn't be taken 100% seriously, because:
* People often open issues to ask for help instead of reporting an issue.
* If nobody is using a plugin, nobody can find bugs. Bug not found doesn't mean it's not there.
* Some issues may be feature requests, which may not be present elsewhere as those are not accepted.

### Price
The price to get the jar. Most plugins are free, some are paid.

### Minecraft
Supported Minecraft versions by the plugin.

### Platforms
Supported platforms by the plugin. Full tracked list includes:
* Bukkit ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)
* Sponge ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352)
* Fabric ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1)
* Forge ![image](https://github.com/NEZNAMY/TAB/assets/6338394/268c1571-0087-466b-b502-ec16017ac1d6)
* BungeeCord ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9)
* Velocity ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d)

### Async
Whether the plugin runs in server's main thread (sync) or in a different thread (async). If a plugin is running sync, it may slow down the server's TPS. All of these features can be done async.

# Belowname
| Attribute                         | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            |
|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues**                   | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      |
| **Price**                         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     |
| **Minecraft**                     | 1.5+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             |
| **Platforms**                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) |
| **Async**                         | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| PlaceholderAPI support            | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Configurable number               | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Configurable text                 | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| 1.20.3+ NumberFormat support      | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                |
| Conditionally disable the feature | If any specified condition is met                                                                                                                                                                                                                                                                                                                                                                                                                                                |

# Bossbar
| Attribute         | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [UltraBar](https://github.com/ryandw11/UltraBar)                                             | [AnimaBossBar](https://www.spigotmc.org/resources/bossbar-spigot-free-bossbar-plugin-1-9-1-18-1.59135/) |
|-------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------|
| **Open issues**   | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/ryandw11/UltraBar)                            | -                                                                                                       |
| **Price**         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)            |
| **Minecraft**     | 1.5+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.12+                                                                                        | 1.9 - 1.18.1                                                                                            |
| **Platforms**     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)            |
| **Async**         | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                            | ❌                                                                                                       |
| Announcements     | ✔ (defined in config)                                                                                                                                                                                                                                                                                                                                                                                                                                                            | ✔ (defined at runtime)                                                                       | ✔ (defined at runtime)                                                                                  |
| Toggle command    | ✔ (customizable)                                                                                                                                                                                                                                                                                                                                                                                                                                                                 | ❌                                                                                            | ❌                                                                                                       |
| Display condition | Anything that has a placeholder                                                                                                                                                                                                                                                                                                                                                                                                                                                  | On join / death / command / in worldguard region                                             | ❌                                                                                                       |

# Global Playerlist
| Attribute       | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                     | [BungeeTabListPlus](https://github.com/CodeCrafter47/BungeeTabListPlus)                      | [Velocitab](https://github.com/WiIIiam278/Velocitab)                                         |
|-----------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|
| **Open issues** | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                               | ![Issues](https://img.shields.io/github/issues/CodeCrafter47/BungeeTabListPlus)              | ![Issues](https://img.shields.io/github/issues/WiIIiam278/Velocitab)                         |
| **Price**       | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                              | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) |
| **Platforms**   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) |

# Header/Footer
| Attribute       | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [TabList](https://github.com/montlikadani/TabList)                                                                                                                                                                                                                                     |
|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues** | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/montlikadani/TabList)                                                                                                                                                                                                                   |
| **Price**       | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                           |
| **Platforms**   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) |

# Layout
| Attribute       | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [BungeeTabListPlus](https://github.com/CodeCrafter47/BungeeTabListPlus)                                                                                                                   |
|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues** | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/CodeCrafter47/BungeeTabListPlus)                                                                                                           |
| **Price**       | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                              |
| **Platforms**   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) |

# Nametags
| Attribute                         | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [NametagEdit](https://github.com/sgtcaze/NametagEdit)                                        |
|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|
| **Open issues**                   | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/sgtcaze/NametagEdit)                          |
| **Price**                         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) |
| **Minecraft**                     | 1.5+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.7+                                                                                         |
| **Platforms**                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) |
| **Async**                         | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                            |
| Customization                     | Per-group<br />Per-player<br />Per-world<br />Per-server                                                                                                                                                                                                                                                                                                                                                                                                                         | Per-group<br />Per-player                                                                    |
| PlaceholderAPI support            | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                            |
| Configurable collision            | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                            |
| Configurable nametag visibility   | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                            |
| Conditionally disable the feature | If any specified condition is met                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                            |
| Bonus                             | - Option to use armor stands to bypass<br />   all limits and support multiple lines<br />- Anti-override to avoid random plugins breaking it                                                                                                                                                                                                                                                                                                                                    | -                                                                                            |

# Per world playerlist

# Playerlist objective
| Attribute                         | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [TabList](https://github.com/montlikadani/TabList)                                                                                                                                        |
|-----------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues**                   | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/montlikadani/TabList)                                                                                                                      |
| **Price**                         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                              |
| **Platforms**                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) |
| Display any number                | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                                                                                                                         |
| Supports hearts display type      | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                                                                                                                         |
| 1.20.3+ NumberFormat support      | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                                                                                                                         |
| Conditionally disable the feature | When any specified condition is met                                                                                                                                                                                                                                                                                                                                                                                                                                              | Disable in worlds                                                                                                                                                                         |

# Scoreboard
| Attribute                             | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [RealScoreboard](https://github.com/joserodpt/RealScoreboard)                                    | [AnimatedScoreboard](https://www.spigotmc.org/resources/20848/)                                                                                                                             | [SternalBoard](https://github.com/ShieldCommunity/SternalBoard)                                                                                                                             | [KiteBoard](https://github.com/Niall7459/KiteBoard-Documentation)                                                                                                                           |
|---------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|--------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues**                       | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/joserodpt/RealScoreboard)                         | -                                                                                                                                                                                           | ![Issues](https://img.shields.io/github/issues/ShieldCommunity/SternalBoard)                                                                                                                | ![Issues](https://img.shields.io/github/issues/Niall7459/KiteBoard-Documentation)                                                                                                           |
| **Price**                             | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/1286a130-6173-4414-b5c6-43d79d75794d)                                                                                                |
| **Minecraft**                         | 1.5+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.13+                                                                                            | 1.8+                                                                                                                                                                                        | 1.7+                                                                                                                                                                                        | 1.8 - 1.20.1                                                                                                                                                                                |
| **Platforms**                         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                |
| **Async**                             | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                                | ✔                                                                                                                                                                                           | ✔                                                                                                                                                                                           | ✔                                                                                                                                                                                           |
| Customizable toggle command           | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                                | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           |
| 1.20.3+ NumberFormat support          | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                                | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           |
| Max line length limit (<1.13 / 1.13+) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/b770a19b-9f1a-44c1-8164-394c788998b8) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942)                                                                                                                                                                                                                                                                                      | - / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/98d0f411-5b2c-4ab2-a167-8189d5c32a02) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/98d0f411-5b2c-4ab2-a167-8189d5c32a02) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/98d0f411-5b2c-4ab2-a167-8189d5c32a02) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) |

| Attribute                             | [FeatherBoard](https://www.spigotmc.org/resources/featherboard.2691/)                                                                                                                                                                                                                    | [UltraScoreboards](https://www.spigotmc.org/resources/ultra-scoreboards.93726/)                                                                                                             | [QuickBoard](https://github.com/tadeas-drab/QuickBoard)                                                                                                                                     | [SimpleScore](https://github.com/r4g3baby/SimpleScore)                                                                                                                                      | [InfiniteScoreboard](https://www.spigotmc.org/resources/infinite-scoreboard.87425/)                                                                                                         |
|---------------------------------------|------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| **Open issues**                       | -                                                                                                                                                                                                                                                                                        | -                                                                                                                                                                                           | ![Issues](https://img.shields.io/github/issues/tadeas-drab/QuickBoard)                                                                                                                      | ![Issues](https://img.shields.io/github/issues/r4g3baby/SimpleScore)                                                                                                                        | -                                                                                                                                                                                           |
| **Price**                             | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/dd25c457-6b4e-4771-a366-3c5ec154f007)                                                                                                                                                                                             | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f2a536e5-4a56-4b5c-9e3d-a86d8761876e)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                |
| **Minecraft**                         | 1.7+                                                                                                                                                                                                                                                                                     | 1.8+                                                                                                                                                                                        | 1.8 - 1.16.1                                                                                                                                                                                | 1.8+                                                                                                                                                                                        | 1.8 - 1.19                                                                                                                                                                                  |
| **Platforms**                         | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                                                                                                             | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9)   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577)                                                                                                |
| **Async**                             | ✔                                                                                                                                                                                                                                                                                        | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ✔                                                                                                                                                                                           |
| Customizable toggle command           | ❌                                                                                                                                                                                                                                                                                        | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           |
| 1.20.3+ NumberFormat support          | ❌                                                                                                                                                                                                                                                                                        | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           | ❌                                                                                                                                                                                           |
| Max line length limit (<1.13 / 1.13+) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/09e5314f-ac46-40ec-b8c8-3ba54a5bf8fc)-![image](https://github.com/NEZNAMY/TAB/assets/6338394/b770a19b-9f1a-44c1-8164-394c788998b8) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ed5c6f89-d700-41a2-b4dd-49ebc846d8c7) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/3c7c3e31-31e6-4bce-9453-b74b0cadd942) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/98d0f411-5b2c-4ab2-a167-8189d5c32a02) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/5db3ba81-3956-4b84-950f-6132d5a10c71) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/98d0f411-5b2c-4ab2-a167-8189d5c32a02) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/5db3ba81-3956-4b84-950f-6132d5a10c71) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/09e5314f-ac46-40ec-b8c8-3ba54a5bf8fc) / ![image](https://github.com/NEZNAMY/TAB/assets/6338394/5db3ba81-3956-4b84-950f-6132d5a10c71) |

# Sorting
| Attribute       | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [NametagEdit](https://github.com/sgtcaze/NametagEdit)                                        | [TabList](https://github.com/montlikadani/TabList)                                                                                                                                        | [Velocitab](https://github.com/WiIIiam278/Velocitab)                                         |
|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|
| **Open issues** | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/sgtcaze/NametagEdit)                          | ![Issues](https://img.shields.io/github/issues/montlikadani/TabList)                                                                                                                      | ![Issues](https://img.shields.io/github/issues/WiIIiam278/Velocitab)                         |
| **Price**       | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                              | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) |
| **Minecraft**   | 1.8+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.8+                                                                                         | 1.8+                                                                                                                                                                                      | 1.8+                                                                                         |
| **Platforms**   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) |
| **Async**       | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ❌                                                                                            | ✔                                                                                                                                                                                         | ✔                                                                                            |
| Sorting options | Groups<br />Permissions<br />Placeholder alphabetically<br />Placeholder numerically<br />Pre-defined placeholder values                                                                                                                                                                                                                                                                                                                                                         | Groups                                                                                       | Groups                                                                                                                                                                                    | Placeholder alphabetically<br />Placeholder numerically                                      |

# Tablist name formatting
| Attribute       | [TAB](https://github.com/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                                            | [TabList](https://github.com/montlikadani/TabList)                                                                                                                                                                                                                                     | [BungeeTabListPlus](https://github.com/CodeCrafter47/BungeeTabListPlus)                                                                                                                   | [SimpleTabList](https://github.com/TheYIMIR/SimpleTabList)                                   | [Velocitab](https://github.com/WiIIiam278/Velocitab)                                         |
|-----------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------|
| **Open issues** | ![Issues](https://img.shields.io/github/issues/NEZNAMY/TAB)                                                                                                                                                                                                                                                                                                                                                                                                                      | ![Issues](https://img.shields.io/github/issues/montlikadani/TabList)                                                                                                                                                                                                                   | ![Issues](https://img.shields.io/github/issues/CodeCrafter47/BungeeTabListPlus)                                                                                                           | ![Issues](https://img.shields.io/github/issues/TheYIMIR/SimpleTabList)                       | ![Issues](https://img.shields.io/github/issues/WiIIiam278/Velocitab)                         |
| **Price**       | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                                                                                                                                                                                                                     | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                                                                                                                           | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a)                                                                                              | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/9bfa85bd-771c-4d78-ab15-be76665bd84a) |
| **Minecraft**   | 1.8+                                                                                                                                                                                                                                                                                                                                                                                                                                                                             | 1.8+                                                                                                                                                                                                                                                                                   | 1.8+                                                                                                                                                                                      | 1.16+                                                                                        | 1.8+                                                                                         |
| **Platforms**   | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/ffbf8384-fff5-4a32-8fc5-0768246e91c1) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/f3cc3194-6dfc-4b15-8d46-52144a4a0352) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) ![image](https://github.com/NEZNAMY/TAB/assets/6338394/99ae3191-7b35-4883-91a9-4edbb1e88ac9) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/e39f160b-f2f4-4ccc-84ad-583092741577) | ![image](https://github.com/NEZNAMY/TAB/assets/6338394/53ad76a7-f61c-4b4f-8411-32ad5c473c4d) |
| **Async**       | ✔                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | ✔                                                                                                                                                                                                                                                                                      | ✔                                                                                                                                                                                         | ❌                                                                                            | ✔                                                                                            |