package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.menus.*;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class MenuManager {

    private final StoneCactos plugin;
    private final Map<UUID, BaseMenu> openMenus;

    public MenuManager(StoneCactos plugin) {
        this.plugin = plugin;
        this.openMenus = new HashMap<>();
    }

    /**
     * Abre o menu principal de um gerador
     */
    public void openMainMenu(Player player, CactusGenerator generator) {
        MainMenu menu = new MainMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de armazém
     */
    public void openWarehouseMenu(Player player, CactusGenerator generator) {
        WarehouseMenu menu = new WarehouseMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de recompensas
     */
    public void openRewardsMenu(Player player) {
        // RewardsMenu menu = new RewardsMenu(plugin, player);
        // openMenu(player, menu);
        player.sendMessage("§cMenu de recompensas temporariamente desabilitado!");
    }

    /**
     * Abre o menu de boosters
     */
    public void openBoostersMenu(Player player, CactusGenerator generator) {
        BoostersMenu menu = new BoostersMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de amigos
     */
    public void openFriendsMenu(Player player, CactusGenerator generator) {
        FriendsMenu menu = new FriendsMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de permissões de amigo
     */
    public void openFriendPermissionMenu(Player player, CactusGenerator generator, UUID friendUuid) {
        FriendPermissionMenu menu = new FriendPermissionMenu(plugin, player, generator, friendUuid);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de depósito
     */
    public void openDepositMenu(Player player, CactusGenerator generator) {
        DepositMenu menu = new DepositMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre o menu de upgrade
     */
    public void openUpgradeMenu(Player player, CactusGenerator generator) {
        UpgradeMenu menu = new UpgradeMenu(plugin, player, generator);
        openMenu(player, menu);
    }

    /**
     * Abre um menu e registra na lista de menus abertos
     */
    private void openMenu(Player player, BaseMenu menu) {
        // Fechar menu anterior se existir
        closeMenu(player);

        // Registrar novo menu
        openMenus.put(player.getUniqueId(), menu);

        // Abrir menu
        menu.open();
    }

    /**
     * Fecha o menu de um jogador
     */
    public void closeMenu(Player player) {
        BaseMenu menu = openMenus.remove(player.getUniqueId());
        // O menu será fechado automaticamente quando o inventário for fechado
    }

    /**
     * Obtém o menu aberto de um jogador
     */
    public BaseMenu getOpenMenu(Player player) {
        return openMenus.get(player.getUniqueId());
    }

    /**
     * Verifica se um jogador tem um menu aberto
     */
    public boolean hasOpenMenu(Player player) {
        return openMenus.containsKey(player.getUniqueId());
    }

    /**
     * Processa clique em menu
     */
    public void handleMenuClick(Player player, int slot, boolean rightClick, boolean shiftClick) {
        BaseMenu menu = getOpenMenu(player);
        if (menu != null) {
            menu.handleClick(slot, rightClick, shiftClick);
        }
    }

    /**
     * Processa fechamento de menu
     */
    public void handleMenuClose(Player player) {
        closeMenu(player);
    }

    /**
     * Atualiza o menu de um jogador
     */
    public void updateMenu(Player player) {
        BaseMenu menu = getOpenMenu(player);
        if (menu != null) {
            menu.update();
        }
    }

    /**
     * Verifica se um título de inventário é de um menu do plugin
     */
    public boolean isPluginMenu(String title) {
        // Verificar títulos dos menus configurados (com colorização)
        String mainTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("main"));
        String warehouseTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("warehouse"));
        String rewardsTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("rewards"));
        String boostersTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("boosters"));
        String friendsTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("friends"));
        String depositTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("deposit"));
        String friendPermissionTitle = plugin.getMessageUtils()
                .colorize(plugin.getConfigManager().getMenuTitle("friendPermission"));
        String upgradeTitle = "§8Evoluir Desenvolvimento";

        return title.equals(mainTitle) ||
                title.equals(warehouseTitle) ||
                title.equals(rewardsTitle) ||
                title.equals(boostersTitle) ||
                title.equals(friendsTitle) ||
                title.equals(depositTitle) ||
                title.equals(friendPermissionTitle) ||
                title.equals(upgradeTitle);
    }

    /**
     * Fecha todos os menus abertos
     */
    public void closeAllMenus() {
        for (UUID playerUuid : openMenus.keySet()) {
            Player player = plugin.getServer().getPlayer(playerUuid);
            if (player != null) {
                player.closeInventory();
            }
        }
        openMenus.clear();
    }

    /**
     * Obtém estatísticas dos menus
     */
    public String getMenuStatistics() {
        return String.format("§eMenus Abertos: §f%d", openMenus.size());
    }
}
