# ================================
#     CONFIGURAÇÃO PESCARIA
# ================================

MySQL:
  host: "localhost"
  user: "root"
  password: "1234"
  database: "db"

SQLite:
  file: "database.db"

Geral:
  sql-type: SQLITE # SQLITE OR MYSQL
  cash-plugin: PLAYERPOINTS # PLUGINS: PLAYERPOINS, YPOINTS, ATLASECONOMIASECUNDARIA, STORMECONOMIASECUNDARIA, HARDGOLD, YECONOMIAS
  yeconomiasEconomy: TOKENS # SÓ TERA EFEITO CASO ESTIVER USANDO YECONOMIAS
  format: LETRAS # LETRAS OU NUMEROS
  ativarXpNaVara: true
  xpPorPeixe: 100 # Xp dado quando pesca um peixe
  xpPadraoDeEvolucao: 1000 # Xp que deve ser conseguido para upar o level da vara de pesca
  xpACadaNivel: 100 # Cada vez que um jogador evoluir o nivel da vara, o nivel sera multiplicado por esse xp
  autoCompletarRanking: true
  venderPeixesCoins: true # Ativar/desativar opção de vender peixes por coins na loja de peixes
  venderPeixesCash: true # Ativar/desativar opção de vender peixes por cash na loja de peixes
  lojaPeixes: true # Ativar/desativar loja de peixes
  lojaSlots: "11,12,13,14,15,19,20,21,22,23,24,25,29,30,31,32,33"
  ativarItensLoja: true # Ativar/desativar itens da loja caso queira utilizar apenas a opção de vender peixes
  autoCompletarLoja: true
  lojaBooster: true # Ativar/desativar a loja de boosters
  raioPesca: 10 # Raio de blocos que o jogador podera lançar a vara para pescar
  chancePescarPeixe: 1.5 # Chance de conseguir um peixe pescando
  chanceEvoluirNivel: 1.0 # Chance de evoluir o nivel da vara pescando
  chanceGanharCoins: 1.0 # Chance de ganhar coins pescando
  coinsAoPescar: 100.0 # Coins que serão dados ao pescar
  tempoPesca: 5 # Tempo em segundos em que o jogador recebera peixes de acordo com a chance configurada
  precoPeixesCoins: 100.0 # A cada 1 peixe vale 100 coins
  precoPeixesCash: 1.0 # A cada 1 peixe vale 1 de cash
  ativarRecompensas: true
  recompensasPesca: # COMANDO (%player% = nome do jogador) ; CHANCE
    - 'give %player% minecraft:STONE 1;10.0'
  horarioPesca:
    use: true
    horarios:
      entrada: "16:00"
      saida: "22:00"
  comandosPermitidos: # Comandos permitidos na area de pesca
    - '/pesca'
  mundosPermitidos: # Mundos em que é permitido pescar
    - 'world'
    - 'pesca'

ScoreBoard:
  use: true
  titulo: "&b&lÁREA DE PESCA"
  linhas:
    - '&1'
    - '&f M. Concluidas: &7%missoes_concluidas%'
    - '&f Bônus: &a%booster%'
    - '&2'
    - '&e&l ▸ Sua vara:'
    - '&b&l  ▎ &fNível: &7%vara_nivel%'
    - '&b&l  ▎ &fXP: &7%vara_xp%/%vara_next_xp%'
    - '&3'
    - '&f Dinheiro: &2$&a%coins%'
    - '&f Cash: &6✪%cash%'
    - '&f Peixes: &e✧%peixes%'
    - '&4'
    - '&bstormplugins.com'

Vara:
  nome: "&b&lVARA STORM &7(%peixes%)"
  lore:
    - '&7Clique com o botão &fdireito + shift&7 para'
    - '&7abrir o menu da sua vara.'
    - ''
    - '&f &b&l▎ &fSorte: &7%sorte%'
    - '&f &b&l▎ &fVelocidade: &7%velocidade%'
    - '&f &b&l▎ &fFortuna: &7%fortuna%'
  encantamentosInventarioTamanho: 45
  encantamentosInventarioFlechaVoltar: 40
  encantamentos:
    sorte: # Aumenta a chance de ganhar peixes pescando em 10% a cada nivel aumenta +10%
      ativado: true
      slot: 20
      aumentarNivelAoComprar: true # Ao comprar um upgrade o nivel necessario irá aumentar
      nivelPlayerNecessario: 1 # Nivel do player necessario para comprar um upgrade
      nivelPadrao: 0
      nivelMaximo: 2
      precoUpgrade: 1500 # Preco em peixes
      item: "FEATHER:0"
      name: "&a&lSORTE"
      lore:
        - '&7Aumente a chance de ganhar peixes'
        - '&7ao pescar em &f10%&7.'
        - ''
        - '&f Nível atual: &7%nivel%'
        - '&f Nível máximo: &7%nivel_max%'
        - ''
        - '&f Nível de pesca necessario: &7%nivel_necessario%'
        - '&f Custo para evoluir: &e%custo% de peixes'
        - ''
        - '&aClique para evoluir'
    velocidade: # Dimuniu o tempo de ganhar recompensas pescando em 1 segundo a cada nivel aumenta +1 segundo
      ativado: true
      slot: 22
      aumentarNivelAoComprar: true # Ao comprar um upgrade o nivel necessario irá aumentar
      nivelPlayerNecessario: 1 # Nivel do player necessario para comprar um upgrade
      nivelPadrao: 0
      nivelMaximo: 2
      precoUpgrade: 2000 # Preco em peixes
      item: "POTION:0"
      name: "&b&lVELOCIDADE"
      lore:
        - '&7Diminua o tempo para receber recompensas'
        - '&7pescando em &f1&7 segundo.'
        - ''
        - '&f Nível atual: &7%nivel%'
        - '&f Nível máximo: &7%nivel_max%'
        - ''
        - '&f Nível de pesca necessario: &7%nivel_necessario%'
        - '&f Custo para evoluir: &e%custo% de peixes'
        - ''
        - '&aClique para evoluir'
    fortuna: # Aumenta a quantidade de peixes ganhos em 10% a cada nivel aumenta +10%
      ativado: true
      slot: 24
      aumentarNivelAoComprar: true # Ao comprar um upgrade o nivel necessario irá aumentar
      nivelPlayerNecessario: 1 # Nivel do player necessario para comprar um upgrade
      nivelPadrao: 0
      nivelMaximo: 2
      precoUpgrade: 10000 # Preco em peixes
      item: "GOLD_INGOT:0"
      name: "&6&lFORTUNA"
      lore:
        - '&7Aumente a quantidade de peixes ganhos'
        - '&7ao pescar em &f10%&7.'
        - ''
        - '&f Nível atual: &7%nivel%'
        - '&f Nível máximo: &7%nivel_max%'
        - ''
        - '&f Nível de pesca necessario: &7%nivel_necessario%'
        - '&f Custo para evoluir: &e%custo% de peixes'
        - ''
        - '&aClique para evoluir'

Loja:
  diamante:
    item: "DIAMOND_HELMET:0"
    name: "&9&lCAPACETE NEGRO"
    lore:
      - '&8 ITEM RARO'
      - ''
      - '&f Encantamentos:'
      - '&7 - Proteção VI'
      - '&7 - Inquebrável VI'
      - ''
      - '&f Detalhes:'
      - '&7 - Causa dano adicional em seu inimigo.'
      - '&7 - É imune ao dano adicional da espada negra.'
      - ''
      - '&f Custo: &e✧%preco% de peixes'
      - ''
      - '&aClique para adquirir'
    glow: true
    preco: 10000
    give:
      command:
        use: false
        commands:
          - 'give %jogador% DIAMOND 1'
      item:
        use: true
        itens:
          diamante:
            item: "DIAMOND_HELMET:0"
            itemmeta:
              use: false
              name: "&9Capacete Negro"
              lore:
                - '&7Dano adicional: +0.5'
            enchantments:
              use: false
              enchants: # Encantamento : Nivel
                - 'DAMAGE_ALL:1'

Boosters:
  booster1:
    item: "EXP_BOTTLE:0"
    name: "&aBooster de Pesca"
    lore:
      - ''
      - '&f Tipo: &aBooster de Pesca'
      - '&f Multiplicação: &72.0x'
      - '&f Duração: &71 hora.'
      - '&f Preço: &7%preco%'
      - ''
    duracao: 1 # Coloque a quantidade em horas
    multiplicacao: 2.0
    preco: 50000
    moeda: COINS # COINS, PEIXES OU CASH

Menus:
  loja:
    coins:
      item: "IRON_INGOT:0"
      name: "&aCoins"
      lore:
        - '&7Venda seus peixes por coins.'
        - ''
        - '&7Clique com direito para vender tudo'
        - '&7Clique com esquerdo para vender x64'
        - ''
        - '&f Seus peixes: &7%peixes%'
        - '&f Valor de venda: &7%valor% coins'
        - '&f Valor de venda x64: &7%valor64% coins'
        - ''
    cash:
      item: "GOLD_INGOT:0"
      name: "&aCash"
      lore:
        - '&7Venda seus peixes por cash.'
        - ''
        - '&7Clique com direito para vender tudo'
        - '&7Clique com esquerdo para vender x64'
        - ''
        - '&f Seus peixes: &7%peixes%'
        - '&f Valor de venda: &7%valor% de cash'
        - '&f Valor de venda x64: &7%valor64% de cash'
        - ''
  ranking:
    jogador:
      name: "&f%pos%º &7%jogador%"
      lore:
        - ''
        - '&f Peixes: &6%peixes%'
        - '&f Posição: &a%pos%'
        - ''
  principal:
    tamanho: 27
    encantamentos:
      item: "b2f79016cad84d1ae21609c4813782598e387961be13c15682752f126dce7a"
      slot: 10
      name: "&aEncantamentos"
      lore:
        - '&7Clique para gerenciar sua vara de pescar.'
    top:
      item: "BOOK_AND_QUILL:0"
      slot: 11
      name: "&aTOP Jogadores"
      lore:
        - '&7Clique para ver os jogadores que mais pescaram.'
    boosters:
      item: "EXP_BOTTLE:0"
      name: "&aBoosters"
      slot: 12
      lore:
        - '&7Clique para ver os boosters de pesca.'
    loja:
      item: "SIGN:0"
      slot: 13
      name: "&aLoja de Peixes"
      lore:
        - '&7Clique para abrir a loja de peixes.'
    pescar:
      item: "FISHING_ROD:0"
      slot: 15 # O slot do sairPescaria é o mesmo deste
      name: "&aIr pescar"
      lore:
        - '&7Clique ir pescar'
    sairPescaria:
      item: "FISHING_ROD:0"
      name: "&cSair da Área de Pesca"
      lore:
        - '&7Clique para sair'
