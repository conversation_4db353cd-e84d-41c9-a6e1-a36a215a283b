package com.atlasplugins.pescaria.listeners;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.enchants.CustomEnchantments;
import com.atlasplugins.pescaria.hud.FishingHUD;
import com.atlasplugins.pescaria.items.FishingRodManager;
import org.bukkit.Bukkit;
import org.bukkit.Sound;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

import java.util.Random;
import java.util.UUID;
import org.bukkit.Material;

public class FishingListener implements Listener {

    private final Pescaria plugin;
    private final FishingRodManager fishingRodManager;
    private final CustomEnchantments customEnchantments;
    private final FishingHUD fishingHUD;
    private final Random random;

    public FishingListener(Pescaria plugin) {
        this.plugin = plugin;
        this.fishingRodManager = new FishingRodManager(plugin);
        this.customEnchantments = new CustomEnchantments(plugin);
        this.fishingHUD = new FishingHUD(plugin);
        this.random = new Random();
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        Player player = event.getPlayer();
        World world = player.getWorld();
        String pescariaWorldName = plugin.getConfigManager().getPescariaWorldName();

        // Debug logs
        plugin.getLogger().info("=== FISHING EVENT DEBUG ===");
        plugin.getLogger().info("Player: " + player.getName());
        plugin.getLogger().info("Current World: " + world.getName());
        plugin.getLogger().info("Pescaria World: " + pescariaWorldName);
        plugin.getLogger().info("Is Fishing World: " + world.getName().equalsIgnoreCase(pescariaWorldName));
        plugin.getLogger().info("Event State: " + event.getState());
        plugin.getLogger().info("==========================");

        // Verificar se o jogador está no mundo de pesca
        if (!world.getName().equalsIgnoreCase(pescariaWorldName)) {
            plugin.getLogger().info("Not in fishing world - ignoring event");
            return;
        }

        plugin.getLogger().info("Processing fishing event in correct world");

        // Carregar encantamentos do jogador
        customEnchantments.loadPlayerEnchants(player.getUniqueId());

        // Carregar todos os encantamentos
        UUID uuid = player.getUniqueId();
        int velocidadeLevel = customEnchantments.getEnchantLevel(uuid, CustomEnchantments.EnchantType.VELOCIDADE);
        int sorteLevel = customEnchantments.getEnchantLevel(uuid, CustomEnchantments.EnchantType.SORTE);
        int fortunaLevel = customEnchantments.getEnchantLevel(uuid, CustomEnchantments.EnchantType.FORTUNA);
        int redemoinhoLevel = customEnchantments.getEnchantLevel(uuid, CustomEnchantments.EnchantType.REDEMOINHO);
        int peixeDuplicadoLevel = customEnchantments.getEnchantLevel(uuid,
                CustomEnchantments.EnchantType.PEIXE_DUPLICADO);
        int experienciaAprimoradaLevel = customEnchantments.getEnchantLevel(uuid,
                CustomEnchantments.EnchantType.EXPERIENCIA_APRIMORADA);

        // Debug dos encantamentos
        plugin.getLogger().info("=== ENCANTAMENTOS DEBUG ===");
        plugin.getLogger().info("Player: " + player.getName());
        plugin.getLogger().info("Velocidade: " + velocidadeLevel);
        plugin.getLogger().info("Sorte: " + sorteLevel);
        plugin.getLogger().info("Fortuna: " + fortunaLevel);
        plugin.getLogger().info("Redemoinho: " + redemoinhoLevel);
        plugin.getLogger().info("Peixe Duplicado: " + peixeDuplicadoLevel);
        plugin.getLogger().info("Experiência Aprimorada: " + experienciaAprimoradaLevel);
        plugin.getLogger().info("==========================");

        // Mensagens baseadas no estado da pesca
        if (event.getState() == PlayerFishEvent.State.FISHING) {
            // Quando joga a vara na água
            player.playSound(player.getLocation(), Sound.SPLASH, 0.5f, 0.8f);

            // Aplicar Redemoinho - reduz tempo de espera
            if (redemoinhoLevel > 0) {
                plugin.getLogger()
                        .info("Aplicando Redemoinho nível " + redemoinhoLevel + " - reduzindo tempo de espera");

                // Redemoinho: força o peixe a morder mais rápido
                plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
                    if (player.isOnline() && player.getItemInHand() != null
                            && player.getItemInHand().getType().toString().contains("FISHING_ROD")) {
                        // Simular mordida do peixe
                        player.sendMessage("§b§lREDEMOINHO §fUm peixe mordeu a isca!");
                        player.playSound(player.getLocation(), Sound.SPLASH, 1.0f, 1.5f);
                    }
                }, Math.max(10L, 60L - (redemoinhoLevel * 10L))); // 3 segundos base - 0.5s por nível
            }

        } else if (event.getState() == PlayerFishEvent.State.CAUGHT_FISH) {
            // Aplicar chance de pescar baseada nos encantamentos (chance alta para teste)
            double baseChance = 95.0; // 95% de chance de pescar
            double finalChance = customEnchantments.applyLuckBonus(player.getUniqueId(), baseChance);

            if (random.nextDouble() * 100 < finalChance) {
                // Calcular quantidade de peixes com Fortuna
                int fishAmount = 1 + fortunaLevel; // 1 base + 1 por nível de fortuna

                // Aplicar Peixe Duplicado
                if (peixeDuplicadoLevel > 0) {
                    double duplicateChance = peixeDuplicadoLevel * 15.0; // 15% por nível
                    if (Math.random() * 100 < duplicateChance) {
                        fishAmount *= 2; // Duplicar peixes
                        player.sendMessage("§6§lPEIXE DUPLICADO §fVocê pescou em dobro!");
                        player.playSound(player.getLocation(), Sound.LEVEL_UP, 1.0f, 2.0f);
                    }
                }

                // Adicionar peixes ao jogador
                int currentFishes = plugin.getPlayerManager().getPlayerFishes(uuid);
                plugin.getPlayerManager().setPlayerFishes(uuid, currentFishes + fishAmount);

                // Aplicar Experiência Aprimorada
                if (experienciaAprimoradaLevel > 0) {
                    int xpAmount = 3 + (experienciaAprimoradaLevel * 2); // 3 base + 2 por nível
                    org.bukkit.entity.ExperienceOrb xpOrb = player.getWorld().spawn(player.getLocation(),
                            org.bukkit.entity.ExperienceOrb.class);
                    xpOrb.setExperience(xpAmount);
                    player.sendMessage("§d§lEXPERIÊNCIA APRIMORADA §f+" + xpAmount + " XP!");
                }

                // Dar XP para a vara de pesca
                int baseXP = plugin.getConfigManager().getXpPerFish();
                fishingRodManager.addXP(player, baseXP);

                // Atualizar vara no inventário para mostrar novos encantamentos
                fishingRodManager.updateFishingRodInInventory(player);

                // Chance de ganhar coins
                if (random.nextDouble() * 100 < plugin.getConfigManager().getCoinsChance()) {
                    double coins = plugin.getConfigManager().getCoinsPerFish();
                    if (plugin.getEconomyIntegration() != null && plugin.getEconomyIntegration().hasEconomy()) {
                        plugin.getEconomyIntegration().depositPlayer(player, coins);
                        String formattedCoins = plugin.getEconomyIntegration().formatMoney(coins);
                        player.sendMessage("§a§lPESCARIA §fVocê ganhou §e" + formattedCoins + "§f!");
                    }
                }

                // Efeitos sonoros e visuais
                player.playSound(player.getLocation(), Sound.SPLASH, 1.0f, 1.2f);
                player.playSound(player.getLocation(), Sound.ORB_PICKUP, 0.5f, 1.5f);

                // Atualizar scoreboard
                plugin.getScoreboardManager().updateScoreboard(player);

                // Mensagem de peixe pescado com detalhes dos encantamentos
                String fishText = fishAmount > 1 ? fishAmount + " peixes" : "um peixe";
                player.sendMessage("§a§lPESCARIA §fVocê pescou " + fishText + "! " +
                        "§7(Sorte: " + sorteLevel + ", Fortuna: " + fortunaLevel +
                        (peixeDuplicadoLevel > 0 ? ", Duplicado: " + peixeDuplicadoLevel : "") +
                        (experienciaAprimoradaLevel > 0 ? ", XP+: " + experienciaAprimoradaLevel : "") + ")");
            }
        }
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        final Player player = event.getPlayer();

        // Agendar atualização da scoreboard para garantir que seja executada após
        // outros plugins
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            World world = player.getWorld();
            String pescariaWorldName = plugin.getConfigManager().getPescariaWorldName();

            // Verificar se o jogador está no mundo de pesca
            if (world.getName().equalsIgnoreCase(pescariaWorldName)) {
                plugin.getScoreboardManager().updateScoreboard(player);
            }
        }, 20L); // 1 segundo de delay
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Salvar dados do jogador
        plugin.getPlayerManager().savePlayer(player.getUniqueId());

        // Remover scoreboard
        plugin.getScoreboardManager().removeScoreboard(player);

        // Limpar HUD
        fishingHUD.clearHUD(player.getUniqueId());
    }

    @EventHandler
    public void onPlayerTeleport(PlayerTeleportEvent event) {
        final Player player = event.getPlayer();
        World toWorld = event.getTo().getWorld();
        World fromWorld = event.getFrom().getWorld();
        String pescariaWorldName = plugin.getConfigManager().getPescariaWorldName();

        // Se o jogador está entrando no mundo de pesca
        if (toWorld.getName().equalsIgnoreCase(pescariaWorldName) &&
                !fromWorld.getName().equalsIgnoreCase(pescariaWorldName)) {

            Bukkit.getScheduler().runTaskLater(plugin, () -> {
                plugin.getScoreboardManager().updateScoreboard(player);
                fishingRodManager.giveFishingRod(player);
                player.sendMessage("§a§lPESCARIA §fVocê entrou na área de pesca!");
            }, 5L);
        }
        // Se o jogador está saindo do mundo de pesca
        else if (fromWorld.getName().equalsIgnoreCase(pescariaWorldName) &&
                !toWorld.getName().equalsIgnoreCase(pescariaWorldName)) {

            plugin.getScoreboardManager().removeScoreboard(player);

            // Remover vara de pesca customizada do inventário
            fishingRodManager.removeFishingRod(player);
        }
    }

    private int getFishingRodXP(java.util.UUID uuid) {
        return plugin.getConfigManager().getDataConfig().getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10; // Nível máximo
    }
}