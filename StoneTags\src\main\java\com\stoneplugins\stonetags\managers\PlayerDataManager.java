package com.stoneplugins.stonetags.managers;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.PlayerData;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.Bukkit;
import org.bukkit.entity.ArmorStand;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.Player;
import org.bukkit.scoreboard.Scoreboard;
import org.bukkit.scoreboard.Team;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class PlayerDataManager {

    private final StoneTags plugin;
    private final Map<UUID, PlayerData> playerDataCache;
    private final Map<UUID, ArmorStand> aboveNameHolograms;

    public PlayerDataManager(StoneTags plugin) {
        this.plugin = plugin;
        this.playerDataCache = new HashMap<>();
        this.aboveNameHolograms = new HashMap<>();
    }

    /**
     * Carrega os dados de um jogador
     */
    public PlayerData loadPlayerData(UUID playerId) {
        // Verificar se já está no cache
        PlayerData cachedData = playerDataCache.get(playerId);
        if (cachedData != null) {
            return cachedData;
        }

        // Carregar do banco de dados
        PlayerData playerData = plugin.getDatabaseManager().loadPlayerData(playerId);

        // Adicionar ao cache
        playerDataCache.put(playerId, playerData);

        return playerData;
    }

    /**
     * Salva os dados de um jogador
     */
    public void savePlayerData(PlayerData playerData) {
        // Salvar no banco de dados
        plugin.getDatabaseManager().savePlayerData(playerData);

        // Atualizar cache
        playerDataCache.put(playerData.getPlayerId(), playerData);
    }

    /**
     * Remove os dados de um jogador do cache
     */
    public void unloadPlayerData(UUID playerId) {
        PlayerData playerData = playerDataCache.remove(playerId);
        if (playerData != null) {
            // Salvar antes de remover
            plugin.getDatabaseManager().savePlayerData(playerData);
        }

        // Remover hologram se existir
        ArmorStand hologram = aboveNameHolograms.remove(playerId);
        if (hologram != null && !hologram.isDead()) {
            hologram.remove();
        }
    }

    /**
     * Salva todos os dados dos jogadores
     */
    public void saveAllPlayerData() {
        for (PlayerData playerData : playerDataCache.values()) {
            plugin.getDatabaseManager().savePlayerData(playerData);
        }
        plugin.getLogger().info("Dados de " + playerDataCache.size() + " jogadores salvos!");
    }

    /**
     * Obtém a tag selecionada de um jogador
     */
    public Tag getSelectedTag(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());

        if (!playerData.hasSelectedTag()) {
            return null;
        }

        return plugin.getTagManager().getTag(playerData.getSelectedTagId());
    }

    /**
     * Define a tag selecionada de um jogador
     */
    public void setSelectedTag(Player player, String tagId) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.setSelectedTagId(tagId);
        savePlayerData(playerData);

        // Gerenciar animação RGB
        Tag newTag = plugin.getTagManager().getTag(tagId);
        plugin.getLogger().info("DEBUG: Tag encontrada: " + (newTag != null ? newTag.getId() : "null"));
        if (newTag != null) {
            plugin.getLogger().info("DEBUG: Tag animada: " + newTag.isAnimated());
        }
        if (newTag != null && newTag.isAnimated()) {
            plugin.getLogger().info("Ativando animação RGB para " + player.getName() + " com tag " + tagId);
            plugin.getAnimationManager().addPlayerAnimation(player, newTag);
        } else {
            plugin.getLogger().info("DEBUG: Removendo animação para " + player.getName() + " - Tag: "
                    + (newTag != null ? "não animada" : "null"));
            plugin.getAnimationManager().removePlayerAnimation(player);
        }

        // Atualizar display name e tab list
        updatePlayerDisplay(player);
    }

    /**
     * Remove a tag selecionada de um jogador
     */
    public void removeSelectedTag(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.removeTag();
        savePlayerData(playerData);

        // Atualizar display name e tab list
        updatePlayerDisplay(player);
    }

    /**
     * Obtém o sufixo customizado de um jogador
     */
    public String getCustomSuffix(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        return playerData.getCustomSuffix();
    }

    /**
     * Define o sufixo customizado de um jogador
     */
    public void setCustomSuffix(Player player, String suffix) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.setCustomSuffix(suffix);
        savePlayerData(playerData);

        // Atualizar display name
        updatePlayerDisplay(player);
    }

    /**
     * Obtém o nome customizado de um jogador
     */
    public String getCustomName(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        return playerData.getCustomName();
    }

    /**
     * Define o nome customizado de um jogador
     */
    public void setCustomName(Player player, String customName) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.setCustomName(customName);
        savePlayerData(playerData);

        // Atualizar display name
        updatePlayerDisplay(player);
    }

    /**
     * Atualiza o display name e tab list de um jogador
     */
    public void updatePlayerDisplay(Player player) {
        Tag selectedTag = getSelectedTag(player);
        com.stoneplugins.stonetags.data.Suffix selectedSuffix = getSelectedSuffix(player);
        PlayerData playerData = loadPlayerData(player.getUniqueId());

        String displayName = player.getName();
        String tabName = player.getName();

        // Aplicar nome customizado se existir
        if (playerData.hasCustomName()) {
            displayName = plugin.getMessageUtils().replacePlaceholders(playerData.getCustomName(), player);
        }

        // Construir prefix e suffix completos
        String fullPrefix = "";
        String fullSuffix = "";

        // Adicionar tag prefix se existir e não for invisível
        if (selectedTag != null && !selectedTag.isInvisible()) {
            fullPrefix = plugin.getMessageUtils().colorize(selectedTag.getCurrentPrefix(player));
        }

        // Adicionar suffix se existir e não for invisível
        if (selectedSuffix != null && !selectedSuffix.isInvisible()) {
            fullSuffix = plugin.getMessageUtils().colorize(selectedSuffix.getDisplaySuffix());
        }

        // Adicionar sufixo customizado se existir
        if (playerData.hasCustomSuffix()) {
            fullSuffix += plugin.getMessageUtils().colorize(playerData.getCustomSuffix());
        }

        displayName = fullPrefix + displayName + fullSuffix;

        // Tab list - construir com prefix e suffix completos
        tabName = fullPrefix + player.getName() + fullSuffix;

        // Aplicar cores
        displayName = plugin.getMessageUtils().colorize(displayName);
        tabName = plugin.getMessageUtils().colorize(tabName);

        // Definir display name
        player.setDisplayName(displayName);

        // Definir tab list name com limite de caracteres
        if (tabName.length() > 16) {
            // Minecraft 1.8 tem limite de 16 caracteres no tab
            tabName = tabName.substring(0, 16);
        }
        player.setPlayerListName(tabName);

        // FORÇAR atualização após TAB processar (delay de 1 tick)
        final String finalDisplayName = displayName;
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            player.setDisplayName(finalDisplayName);
        }, 1L);

        // DESABILITADO: Scoreboard team - deixar TAB gerenciar
        // updateScoreboardTeam(player, selectedTag);

        // DESABILITADO: Hologram do ArmorStand - deixar TAB gerenciar as nametags
        // updateAboveNameHologram(player, selectedTag);

        // Integração com TAB via placeholders
        updateTabPlugin(player, selectedTag);

        // IMPORTANTE: No Minecraft 1.8.8, os holograms sao automaticamente visiveis
        // Nao ha necessidade de controle manual de visibilidade
    }

    /**
     * Atualiza o scoreboard team para mostrar prefix/suffix no tab
     */
    private void updateScoreboardTeam(Player player, Tag selectedTag) {
        plugin.getLogger().info("=== DEBUG: updateScoreboardTeam para " + player.getName() + " ===");

        // Usar scoreboard principal do servidor para que todos vejam
        Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();

        // Nome do team baseado na tag
        String teamName = "stonetags_" + (selectedTag != null ? selectedTag.getId() : "default");

        // Remover jogador de teams antigos
        for (Team team : scoreboard.getTeams()) {
            if (team.getName().startsWith("stonetags_")) {
                team.removeEntry(player.getName());
            }
        }

        if (selectedTag != null && !selectedTag.isInvisible()) {
            plugin.getLogger().info("Criando team para tag: " + selectedTag.getId());

            Team team = scoreboard.getTeam(teamName);
            if (team == null) {
                team = scoreboard.registerNewTeam(teamName);
                plugin.getLogger().info("Team criado no scoreboard principal: " + teamName);
            }

            // Configurar prefix e suffix do team
            String prefix = plugin.getMessageUtils().colorize(selectedTag.getTabPrefix());
            String suffix = plugin.getMessageUtils().colorize(selectedTag.getTabSuffix());

            plugin.getLogger()
                    .info("Tab prefix original: '" + selectedTag.getTabPrefix() + "' -> colorizado: '" + prefix + "'");
            plugin.getLogger()
                    .info("Tab suffix original: '" + selectedTag.getTabSuffix() + "' -> colorizado: '" + suffix + "'");

            // Limitar tamanho (Minecraft 1.8 tem limite de 16 caracteres)
            if (prefix.length() > 16)
                prefix = prefix.substring(0, 16);
            if (suffix.length() > 16)
                suffix = suffix.substring(0, 16);

            team.setPrefix(prefix);
            team.setSuffix(suffix);

            plugin.getLogger().info("Team configurado - Prefix: '" + prefix + "', Suffix: '" + suffix + "'");

            // Adicionar jogador ao team
            team.addEntry(player.getName());
            plugin.getLogger().info("Jogador " + player.getName() + " adicionado ao team " + teamName);

            // Aplicar scoreboard para todos os jogadores online
            for (Player onlinePlayer : Bukkit.getOnlinePlayers()) {
                onlinePlayer.setScoreboard(scoreboard);
            }

        } else {
            plugin.getLogger().info("Nenhuma tag válida para criar team");
        }
    }

    /**
     * Atualiza o hologram acima do jogador
     */
    private void updateAboveNameHologram(Player player, Tag selectedTag) {
        plugin.getLogger().info("=== DEBUG: updateAboveNameHologram para " + player.getName() + " ===");

        // Remover hologram anterior se existir
        ArmorStand oldHologram = aboveNameHolograms.remove(player.getUniqueId());
        if (oldHologram != null && !oldHologram.isDead()) {
            oldHologram.remove();
            plugin.getLogger().info("Hologram anterior removido para " + player.getName());
        }

        // Criar novo hologram se a tag tiver aboveName
        if (selectedTag != null && selectedTag.hasAboveName() && !selectedTag.isInvisible()) {
            plugin.getLogger().info("Tag tem aboveName: '" + selectedTag.getAboveName() + "'");
            try {
                // Criar ArmorStand invisivel acima do jogador - ALTURA CORRIGIDA PARA FICAR
                // VISIVEL
                ArmorStand hologram = (ArmorStand) player.getWorld().spawnEntity(
                        player.getLocation().add(0, 2.0, 0), EntityType.ARMOR_STAND);

                // Configurar ArmorStand - CONFIGURACAO MELHORADA
                hologram.setVisible(false);
                hologram.setGravity(false);
                hologram.setCanPickupItems(false);
                hologram.setCustomNameVisible(true);
                hologram.setMarker(true); // Torna o ArmorStand nao colidivel
                hologram.setSmall(true); // Pequeno
                // Metodos nao disponiveis no 1.8:
                // hologram.setInvulnerable(true); // Invulneravel
                // hologram.setSilent(true); // Silencioso
                hologram.setBasePlate(false); // Sem base
                hologram.setArms(false); // Sem bracos

                // Definir texto do hologram
                String aboveText = plugin.getMessageUtils().colorize(selectedTag.getAboveName());
                aboveText = plugin.getMessageUtils().replacePlaceholders(aboveText, player);
                hologram.setCustomName(aboveText);

                plugin.getLogger().info("Hologram criado para " + player.getName() + " com texto: '" + aboveText + "'");

                // IMPORTANTE: No Minecraft 1.8.8, o hologram eh automaticamente visivel para
                // todos
                // Nao ha necessidade de showEntity pois nao existe nesta versao

                // Armazenar referência
                aboveNameHolograms.put(player.getUniqueId(), hologram);

                // Agendar task para mover o hologram com o jogador
                startHologramFollowTask(player, hologram);

            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao criar hologram para " + player.getName() + ": " + e.getMessage());
                e.printStackTrace();
            }
        } else {
            plugin.getLogger()
                    .info("Não criando hologram - Tag: " + (selectedTag != null ? selectedTag.getId() : "null") +
                            ", hasAboveName: " + (selectedTag != null ? selectedTag.hasAboveName() : "false") +
                            ", isInvisible: " + (selectedTag != null ? selectedTag.isInvisible() : "false"));
        }
    }

    /**
     * Inicia task para fazer o hologram seguir o jogador
     */
    private void startHologramFollowTask(Player player, ArmorStand hologram) {
        Bukkit.getScheduler().runTaskTimer(plugin, () -> {
            if (!player.isOnline() || hologram.isDead()) {
                hologram.remove();
                aboveNameHolograms.remove(player.getUniqueId());
                return;
            }

            // SEMPRE MOSTRAR O HOLOGRAM - REMOVIDO SISTEMA DE DISTANCIA
            // Atualizar posicao do hologram
            hologram.teleport(player.getLocation().add(0, 2.0, 0)); // ALTURA CORRIGIDA

            // Garantir que esta sempre visivel
            if (!hologram.isCustomNameVisible()) {
                hologram.setCustomNameVisible(true);
            }

            // GARANTIR que o hologram esteja sempre visivel (1.8.8 nao precisa de
            // showEntity)

        }, 0L, 10L); // Atualizar a cada 0.5 segundos

    }

    /**
     * Obtém informações de um jogador para placeholders
     */
    public String getPlayerTagInfo(Player player, String info) {
        Tag selectedTag = getSelectedTag(player);
        if (selectedTag == null)
            return "";

        return plugin.getTagManager().getTagInfo(selectedTag.getId(), info);
    }

    /**
     * Obtém informações do sufixo customizado para placeholders
     */
    public String getPlayerSuffixInfo(Player player, String info) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());

        switch (info.toLowerCase()) {
            case "suffix":
                return playerData.hasCustomSuffix() ? playerData.getCustomSuffix() : "";
            case "hassuffix":
                return String.valueOf(playerData.hasCustomSuffix());
            default:
                return "";
        }
    }

    /**
     * Verifica se um jogador tem uma tag selecionada
     */
    public boolean hasSelectedTag(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        return playerData.hasSelectedTag();
    }

    /**
     * Define o suffix selecionado de um jogador
     */
    public void setSelectedSuffix(Player player, String suffixId) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.setSelectedSuffixId(suffixId);
        savePlayerData(playerData);

        // Atualizar display name e tab list
        updatePlayerDisplay(player);
    }

    /**
     * Remove o suffix selecionado de um jogador
     */
    public void removeSelectedSuffix(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        playerData.removeSuffix();
        savePlayerData(playerData);

        // Atualizar display name e tab list
        updatePlayerDisplay(player);
    }

    /**
     * Obtém o suffix selecionado de um jogador
     */
    public com.stoneplugins.stonetags.data.Suffix getSelectedSuffix(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        String suffixId = playerData.getSelectedSuffixId();

        if (suffixId == null || suffixId.isEmpty()) {
            return null;
        }

        return plugin.getSuffixManager().getSuffix(suffixId);
    }

    /**
     * Verifica se um jogador tem um suffix selecionado
     */
    public boolean hasSelectedSuffix(Player player) {
        PlayerData playerData = loadPlayerData(player.getUniqueId());
        return playerData.hasSelectedSuffix();
    }

    /**
     * Obtém a tag de maior prioridade automaticamente
     */
    public void setHighestPriorityTag(Player player) {
        plugin.getLogger().info("=== DEBUG: setHighestPriorityTag para " + player.getName() + " ===");

        Tag highestTag = plugin.getTagManager().getHighestPriorityTag(player);
        plugin.getLogger().info("Tag de maior prioridade: " + (highestTag != null ? highestTag.getId() : "nenhuma"));

        if (highestTag != null) {
            plugin.getLogger().info("Definindo tag " + highestTag.getId() + " para " + player.getName());
            setSelectedTag(player, highestTag.getId());
        } else {
            plugin.getLogger().info("Nenhuma tag disponível para " + player.getName());
        }
    }

    /**
     * Integração com o plugin TAB via placeholders
     */
    private void updateTabPlugin(Player player, Tag selectedTag) {
        // TAB integration via placeholders - silent mode
        if (plugin.getServer().getPluginManager().getPlugin("TAB") != null) {
            // TAB will use placeholders automatically
        }
    }
}
