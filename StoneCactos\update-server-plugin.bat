@echo off
echo ========================================
echo    Atualizando Plugin no Servidor
echo ========================================

set "PLUGINS_DIR=..\servidor\plugins"
set "OLD_PLUGIN=%PLUGINS_DIR%\stonecactos-1.0.jar"
set "NEW_PLUGIN=%PLUGINS_DIR%\stonecactos-1.0-new.jar"

REM Verificar se o novo plugin existe
if not exist "%NEW_PLUGIN%" (
    echo ERRO: Arquivo stonecactos-1.0-new.jar nao encontrado!
    echo Execute primeiro: compile-and-deploy.ps1
    pause
    exit /b 1
)

echo Verificando se o servidor esta rodando...
tasklist /FI "IMAGENAME eq java.exe" 2>NUL | find /I /N "java.exe">NUL
if "%ERRORLEVEL%"=="0" (
    echo AVISO: Processos Java detectados!
    echo Certifique-se de que o servidor Minecraft esta parado.
    echo.
    choice /C SN /M "Continuar mesmo assim (S/N)"
    if errorlevel 2 (
        echo Operacao cancelada.
        pause
        exit /b 1
    )
)

echo.
echo Fazendo backup do plugin atual...
if exist "%OLD_PLUGIN%" (
    set "BACKUP_NAME=stonecactos-1.0-replaced-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.jar"
    set "BACKUP_NAME=!BACKUP_NAME: =0!"
    copy "%OLD_PLUGIN%" "%PLUGINS_DIR%\!BACKUP_NAME!" >nul
    echo Backup criado: !BACKUP_NAME!
    
    echo Removendo plugin antigo...
    del "%OLD_PLUGIN%"
    if exist "%OLD_PLUGIN%" (
        echo ERRO: Nao foi possivel remover o plugin antigo!
        echo O servidor pode estar rodando e bloqueando o arquivo.
        pause
        exit /b 1
    )
    echo Plugin antigo removido.
) else (
    echo Nenhum plugin antigo encontrado.
)

echo.
echo Instalando novo plugin...
ren "%NEW_PLUGIN%" "stonecactos-1.0.jar"

if exist "%OLD_PLUGIN%" (
    echo ========================================
    echo    PLUGIN ATUALIZADO COM SUCESSO!
    echo ========================================
    echo O plugin StoneCactos foi atualizado.
    echo Voce pode iniciar o servidor agora.
) else (
    echo ERRO: Falha ao renomear o novo plugin!
    pause
    exit /b 1
)

echo.
choice /C SN /M "Deseja iniciar o servidor agora (S/N)"
if errorlevel 1 if not errorlevel 2 (
    echo Iniciando servidor...
    cd /d "..\servidor"
    if exist "start_server.bat" (
        start "" "start_server.bat"
        echo Servidor iniciado!
    ) else if exist "start.bat" (
        start "" "start.bat"
        echo Servidor iniciado!
    ) else (
        echo Script de inicio do servidor nao encontrado.
        echo Inicie manualmente.
    )
)

pause
