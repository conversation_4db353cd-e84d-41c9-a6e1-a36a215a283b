package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Arrays;
import java.util.List;

public class WarehouseMenu extends BaseMenu {

    private final CactusGenerator generator;

    public WarehouseMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("warehouse"));
        int size = plugin.getConfigManager().getMenuSize("warehouse");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro (se configurado)
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.warehouse.glassPaneSlots");
        if (glassSlots != null) {
            fillGlassSlots(glassSlots);
        }

        // Item de cactos
        setupCactusItem();

        // Botão de voltar
        setupBackButton();
    }

    private void setupCactusItem() {
        int slot = getConfigSlot("warehouse", "plantation");
        String name = plugin.getConfigManager().getMenuItemName("warehouse", "plantation");
        List<String> lore = plugin.getConfigManager().getMenuItemLore("warehouse", "plantation");

        // Calcular valores
        int cactusAmount = generator.getStoredCactus();
        double unitValue = plugin.getConfigManager().getUnitCactusValue();
        double totalValue = cactusAmount * unitValue;

        // Substituir placeholders
        lore = replacePlaceholders(lore,
                "%quantityCactus%", formatNumber(cactusAmount),
                "%unitValue%", formatMoney(unitValue),
                "%totalValue%", formatMoney(totalValue));

        // Criar item de cacto
        ItemStack item = createItem(Material.CACTUS, name, lore.toArray(new String[0]));

        // Se não há cactos, deixar o item acinzentado
        if (cactusAmount == 0) {
            List<String> emptyLore = Arrays.asList("§7Produza cactos para vendê-los aqui.");
            item = createItem(Material.valueOf("STAINED_GLASS_PANE"), (short) 8,
                    "§cNenhum cacto armazenado", emptyLore);
        }

        setItem(slot, item);
    }

    @Override
    protected void setupBackButton() {
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.warehouse.backToMainInventory.slot", 31);
        ItemStack backButton = createItem(Material.ARROW, "&c⬅ Voltar", "&7Clique para voltar ao menu principal");
        setItem(backSlot, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        int cactusSlot = getConfigSlot("warehouse", "plantation");
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.warehouse.backToMainInventory.slot", 31);

        if (slot == cactusSlot) {
            handleCactusSell();
        } else if (slot == backSlot) {
            handleBackClick();
        } else {
            plugin.getLogger().info("DEBUG: Clique em slot não mapeado: " + slot);
        }
    }

    private void handleCactusSell() {
        // Verificar permissão
        if (!generator.isOwner(player) && !generator.hasPermission(player.getUniqueId(), "sell_cactus")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        int cactusAmount = generator.getStoredCactus();

        if (cactusAmount <= 0) {
            player.sendMessage(plugin.getConfigManager().getMessage("dontHaveCactusToSell"));
            return;
        }

        // Vender cactos
        // if (plugin.getEconomyManager().sellCactus(player, cactusAmount)) {
        if (true) { // Temporário: sempre permitir venda
            // Remover cactos do gerador
            generator.setStoredCactus(0);
            plugin.getGeneratorManager().saveGenerator(generator);

            // Atualizar holograma
            plugin.getHologramManager().updateHologram(generator);

            // Atualizar menu
            update();
        }
    }

    @Override
    protected void handleBackClick() {
        // Fechar inventário primeiro
        player.closeInventory();

        // Abrir menu principal após um delay para evitar conflitos
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getMenuManager().openMainMenu(player, generator);
        }, 2L);
    }
}
