package com.stoneplugins.spawnershop.managers;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.data.Spawner;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class PlayerDataManager {

    private final SpawnerShop plugin;
    private final Map<UUID, PlayerData> playerDataCache;
    private final Map<UUID, PendingPurchase> pendingPurchases;

    public PlayerDataManager(SpawnerShop plugin) {
        this.plugin = plugin;
        this.playerDataCache = new HashMap<>();
        this.pendingPurchases = new HashMap<>();
    }

    public PlayerData getPlayerData(Player player) {
        return getPlayerData(player.getUniqueId(), player.getName());
    }

    public PlayerData getPlayerData(UUID uuid, String name) {
        // Verificar cache primeiro
        PlayerData data = playerDataCache.get(uuid);
        if (data != null) {
            // Atualizar nome se necessário
            if (!data.getName().equals(name)) {
                data.setName(name);
                savePlayerData(data);
            }
            return data;
        }

        // Buscar no banco de dados
        data = plugin.getDatabaseManager().getPlayerData(uuid);
        if (data == null) {
            // Criar novo jogador
            data = new PlayerData(uuid, name);
            data.setPurchaseLimit(plugin.getConfigManager().getDefaultLimit());
            savePlayerData(data);
        } else {
            // Atualizar nome se necessário
            if (!data.getName().equals(name)) {
                data.setName(name);
                savePlayerData(data);
            }
        }

        // Adicionar ao cache
        playerDataCache.put(uuid, data);
        return data;
    }

    public void savePlayerData(PlayerData playerData) {
        plugin.getDatabaseManager().savePlayerData(playerData);
        playerDataCache.put(playerData.getUuid(), playerData);
    }

    public void unloadPlayerData(UUID uuid) {
        PlayerData data = playerDataCache.get(uuid);
        if (data != null) {
            savePlayerData(data);
            playerDataCache.remove(uuid);
        }
    }

    public void saveAllPlayerData() {
        for (PlayerData data : playerDataCache.values()) {
            plugin.getDatabaseManager().savePlayerData(data);
        }
    }

    public boolean canPlayerPurchase(Player player, int quantity) {
        if (!plugin.getConfigManager().isLimitEnabled()) {
            return true;
        }

        if (player.hasPermission("spawnershop.bypass.limit")) {
            return true;
        }

        PlayerData data = getPlayerData(player);
        return data.canPurchase(quantity);
    }

    public void addPurchase(Player player, String spawnerType, int quantity, double price) {
        PlayerData data = getPlayerData(player);
        data.addPurchases(quantity);
        savePlayerData(data);

        // Adicionar ao histórico
        plugin.getDatabaseManager().addPurchaseHistory(
                player.getUniqueId(),
                spawnerType,
                quantity,
                price);
    }

    public void increasePurchaseLimit(Player player, int amount) {
        PlayerData data = getPlayerData(player);
        data.increasePurchaseLimit(amount);
        savePlayerData(data);
    }

    public int getRemainingLimit(Player player) {
        if (!plugin.getConfigManager().isLimitEnabled()) {
            return Integer.MAX_VALUE;
        }

        if (player.hasPermission("spawnershop.bypass.limit")) {
            return Integer.MAX_VALUE;
        }

        PlayerData data = getPlayerData(player);
        return data.getRemainingLimit();
    }

    public int getPurchaseCount(Player player) {
        PlayerData data = getPlayerData(player);
        return data.getPurchases();
    }

    public int getPurchaseLimit(Player player) {
        PlayerData data = getPlayerData(player);
        return data.getPurchaseLimit();
    }

    public List<PlayerData> getTopPlayers(int limit) {
        return plugin.getDatabaseManager().getTopPlayers(limit);
    }

    public double getPlayerDiscount(Player player) {
        double totalDiscount = 0.0;

        List<String> discounts = plugin.getConfigManager().getDiscounts();
        for (String discountConfig : discounts) {
            String[] parts = discountConfig.split(":");
            if (parts.length >= 2) {
                String permission = parts[0];
                double discount = Double.parseDouble(parts[1]);

                if (player.hasPermission(permission)) {
                    totalDiscount += discount;
                }
            }
        }

        return Math.min(totalDiscount, 100.0); // Máximo 100% de desconto
    }

    public String getPlayerDiscountGroups(Player player) {
        StringBuilder groups = new StringBuilder();

        List<String> discounts = plugin.getConfigManager().getDiscounts();
        for (String discountConfig : discounts) {
            String[] parts = discountConfig.split(":");
            if (parts.length >= 3) {
                String permission = parts[0];
                String groupName = parts[2];

                if (player.hasPermission(permission)) {
                    if (groups.length() > 0) {
                        groups.append(", ");
                    }
                    groups.append(groupName);
                }
            }
        }

        return groups.length() > 0 ? groups.toString() : "Nenhum";
    }

    public void clearCache() {
        saveAllPlayerData();
        playerDataCache.clear();
    }

    // Métodos para compras pendentes
    public void addPendingPurchase(Player player, Spawner spawner, int quantity) {
        pendingPurchases.put(player.getUniqueId(), new PendingPurchase(spawner, quantity));
    }

    public PendingPurchase getPendingPurchase(Player player) {
        return pendingPurchases.get(player.getUniqueId());
    }

    public void removePendingPurchase(Player player) {
        pendingPurchases.remove(player.getUniqueId());
    }

    public boolean hasPendingPurchase(Player player) {
        return pendingPurchases.containsKey(player.getUniqueId());
    }

    // Classe interna para compras pendentes
    public static class PendingPurchase {
        private final Spawner spawner;
        private final int quantity;

        public PendingPurchase(Spawner spawner, int quantity) {
            this.spawner = spawner;
            this.quantity = quantity;
        }

        public Spawner getSpawner() {
            return spawner;
        }

        public int getQuantity() {
            return quantity;
        }
    }
}
