package com.atlasplugins.pescaria.integrations;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

import java.lang.reflect.Method;

public class BetterEconomyIntegration {

    private final Pescaria plugin;
    private boolean enabled = false;
    private Plugin betterEconomyPlugin;
    private Object economyAPI;

    public BetterEconomyIntegration(Pescaria plugin) {
        this.plugin = plugin;
        setupBetterEconomy();
    }

    private void setupBetterEconomy() {
        betterEconomyPlugin = Bukkit.getPluginManager().getPlugin("BetterEconomy");
        if (betterEconomyPlugin != null && betterEconomyPlugin.isEnabled()) {
            try {
                // Tentar obter a API do BetterEconomy
                Class<?> economyClass = Class.forName("me.hsgamer.bettereconomy.api.EconomyAPI");
                Method getInstanceMethod = economyClass.getMethod("getInstance");
                economyAPI = getInstanceMethod.invoke(null);
                enabled = true;
                plugin.getLogger().info("Integração com BetterEconomy ativada!");
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao conectar com BetterEconomy: " + e.getMessage());
            }
        }
    }

    public boolean isEnabled() {
        return enabled;
    }

    public double getBalance(Player player) {
        if (!enabled) return 0.0;

        try {
            Method getBalanceMethod = economyAPI.getClass().getMethod("getBalance", Player.class);
            Object result = getBalanceMethod.invoke(economyAPI, player);
            return result instanceof Number ? ((Number) result).doubleValue() : 0.0;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao obter saldo do BetterEconomy: " + e.getMessage());
            return 0.0;
        }
    }

    public boolean addBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method addBalanceMethod = economyAPI.getClass().getMethod("addBalance", Player.class, double.class);
            addBalanceMethod.invoke(economyAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao adicionar saldo no BetterEconomy: " + e.getMessage());
            return false;
        }
    }

    public boolean removeBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method removeBalanceMethod = economyAPI.getClass().getMethod("removeBalance", Player.class, double.class);
            removeBalanceMethod.invoke(economyAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao remover saldo do BetterEconomy: " + e.getMessage());
            return false;
        }
    }

    public boolean setBalance(Player player, double amount) {
        if (!enabled) return false;

        try {
            Method setBalanceMethod = economyAPI.getClass().getMethod("setBalance", Player.class, double.class);
            setBalanceMethod.invoke(economyAPI, player, amount);
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao definir saldo do BetterEconomy: " + e.getMessage());
            return false;
        }
    }

    public boolean hasBalance(Player player, double amount) {
        if (!enabled) return false;
        return getBalance(player) >= amount;
    }

    public String formatBalance(double amount) {
        if (!enabled) return String.valueOf(amount);

        try {
            Method formatMethod = economyAPI.getClass().getMethod("formatBalance", double.class);
            Object result = formatMethod.invoke(economyAPI, amount);
            return result != null ? result.toString() : String.valueOf(amount);
        } catch (Exception e) {
            // Se não conseguir formatar, usar formatação padrão
            return String.format("%.2f", amount);
        }
    }

    public String getCurrencySymbol() {
        if (!enabled) return "$";

        try {
            Method getSymbolMethod = economyAPI.getClass().getMethod("getCurrencySymbol");
            Object result = getSymbolMethod.invoke(economyAPI);
            return result != null ? result.toString() : "$";
        } catch (Exception e) {
            return "$";
        }
    }

    public String getCurrencyName() {
        if (!enabled) return "Money";

        try {
            Method getNameMethod = economyAPI.getClass().getMethod("getCurrencyName");
            Object result = getNameMethod.invoke(economyAPI);
            return result != null ? result.toString() : "Money";
        } catch (Exception e) {
            return "Money";
        }
    }

    public int getPlayerRank(Player player) {
        if (!enabled) return -1;

        try {
            Method getRankMethod = economyAPI.getClass().getMethod("getPlayerRank", Player.class);
            Object result = getRankMethod.invoke(economyAPI, player);
            return result instanceof Number ? ((Number) result).intValue() : -1;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao obter ranking do BetterEconomy: " + e.getMessage());
            return -1;
        }
    }

    // Métodos para integração com o sistema de pesca
    public boolean sellFishForMoney(Player player, int fishAmount, double pricePerFish) {
        if (!enabled) return false;

        double totalPrice = fishAmount * pricePerFish;
        
        if (addBalance(player, totalPrice)) {
            player.sendMessage("§a§lPESCARIA §fVocê vendeu §e" + fishAmount + " §fpeixes por §a" + 
                getCurrencySymbol() + formatBalance(totalPrice) + "§f!");
            return true;
        }
        
        return false;
    }

    public boolean buyItemWithMoney(Player player, double price, String itemName) {
        if (!enabled) return false;

        if (!hasBalance(player, price)) {
            player.sendMessage("§c§lPESCARIA §fVocê não tem dinheiro suficiente! Necessário: §a" + 
                getCurrencySymbol() + formatBalance(price));
            return false;
        }

        if (removeBalance(player, price)) {
            player.sendMessage("§a§lPESCARIA §fVocê comprou §e" + itemName + " §fpor §a" + 
                getCurrencySymbol() + formatBalance(price) + "§f!");
            return true;
        }

        return false;
    }

    // Placeholders para integração com PlaceholderAPI
    public String getBalanceFormatted(Player player) {
        if (!enabled) return "0.00";
        return formatBalance(getBalance(player));
    }

    public String getBalanceWithSymbol(Player player) {
        if (!enabled) return "$0.00";
        return getCurrencySymbol() + formatBalance(getBalance(player));
    }

    public String getPlayerRankFormatted(Player player) {
        if (!enabled) return "N/A";
        int rank = getPlayerRank(player);
        return rank > 0 ? String.valueOf(rank) : "N/A";
    }
}
