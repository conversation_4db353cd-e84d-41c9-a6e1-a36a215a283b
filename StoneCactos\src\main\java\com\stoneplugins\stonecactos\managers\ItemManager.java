package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.Booster;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ItemManager {

  private final StoneCactos plugin;

  public ItemManager(StoneCactos plugin) {
    this.plugin = plugin;
  }

  /**
   * Cria o item gerador de cactos
   */
  public ItemStack createCactusGenerator(String ownerName, int towers, int capacity) {
    String texture = plugin.getConfigManager().getConfig().getString("cactus.item");
    String name = plugin.getConfigManager().getConfig().getString("cactus.name", "&a&lGERADOR DE CACTOS");
    List<String> lore = plugin.getConfigManager().getConfig().getStringList("cactus.lore");

    ItemStack item;

    // Verificar se é uma textura de head ou material
    if (texture.length() == 64 && !texture.contains(":")) {
      // É uma textura de head
      item = createSkullWithTexture(texture);
    } else {
      // É um material
      String[] parts = texture.split(":");
      Material material = Material.valueOf(parts[0]);
      short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;

      item = new ItemStack(material, 1, data);
    }

    // Aplicar nome e lore
    ItemMeta meta = item.getItemMeta();
    if (meta != null) {
      meta.setDisplayName(plugin.getMessageUtils().colorize(name));

      if (lore != null && !lore.isEmpty()) {
        List<String> formattedLore = new ArrayList<>();
        for (String line : lore) {
          String formattedLine = line
              .replace("%owner%", ownerName)
              .replace("%towers%", String.valueOf(towers))
              .replace("%capacity%", String.valueOf(capacity));
          formattedLore.add(plugin.getMessageUtils().colorize(formattedLine));
        }
        meta.setLore(formattedLore);
      }

      item.setItemMeta(meta);
    }

    return item;
  }

  /**
   * Cria um item de booster
   */
  public ItemStack createBoosterItem(String boosterId) {
    if (!plugin.getConfigManager().hasBooster(boosterId)) {
      return new ItemStack(Material.BARRIER);
    }

    String name = plugin.getConfigManager().getBoosterName(boosterId);
    List<String> lore = plugin.getConfigManager().getBoosterLore(boosterId);
    String item = plugin.getConfigManager().getBoosterItem(boosterId);
    double price = plugin.getConfigManager().getBoosterPrice(boosterId);
    int time = plugin.getConfigManager().getBoosterTime(boosterId);
    double multiplier = plugin.getConfigManager().getBoosterMultiplier(boosterId);

    ItemStack itemStack;

    // Verificar se é uma textura de head ou material
    if (item.length() == 64 && !item.contains(":")) {
      // É uma textura de head
      itemStack = createSkullWithTexture(item);
    } else {
      // É um material
      String[] parts = item.split(":");
      Material material = Material.valueOf(parts[0]);
      short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;

      itemStack = new ItemStack(material, 1, data);
    }

    // Aplicar nome e lore
    ItemMeta meta = itemStack.getItemMeta();
    if (meta != null) {
      meta.setDisplayName(plugin.getMessageUtils().colorize(name));

      if (lore != null && !lore.isEmpty()) {
        List<String> formattedLore = new ArrayList<>();
        for (String line : lore) {
          String formattedLine = line
              .replace("%price%", formatPrice(price))
              .replace("%time%", formatTime(time))
              .replace("%multiplier%", String.valueOf(multiplier));
          formattedLore.add(plugin.getMessageUtils().colorize(formattedLine));
        }
        meta.setLore(formattedLore);
      }

      itemStack.setItemMeta(meta);
    }

    return itemStack;
  }

  /**
   * Cria um item de recompensa
   */
  public ItemStack createRewardItem(String rewardId, boolean collected, int playerCactus) {
    if (!plugin.getConfigManager().hasReward(rewardId)) {
      return new ItemStack(Material.BARRIER);
    }

    String name = plugin.getConfigManager().getRewardName(rewardId);
    List<String> lore = plugin.getConfigManager().getRewardLore(rewardId);
    String item = plugin.getConfigManager().getRewardItem(rewardId);
    int necessaryCactus = plugin.getConfigManager().getRewardNecessaryCactus(rewardId);

    ItemStack itemStack;

    // Verificar se é uma textura de head ou material
    if (item.length() == 64 && !item.contains(":")) {
      // É uma textura de head
      itemStack = createSkullWithTexture(item);
    } else {
      // É um material
      String[] parts = item.split(":");
      Material material = Material.valueOf(parts[0]);
      short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;

      itemStack = new ItemStack(material, 1, data);
    }

    // Aplicar nome e lore
    ItemMeta meta = itemStack.getItemMeta();
    if (meta != null) {
      meta.setDisplayName(plugin.getMessageUtils().colorize(name));

      if (lore != null && !lore.isEmpty()) {
        List<String> formattedLore = new ArrayList<>();
        for (String line : lore) {
          String status;
          if (collected) {
            status = plugin.getConfigManager().getMessage("rewardStatus.collected");
          } else if (playerCactus >= necessaryCactus) {
            status = plugin.getConfigManager().getMessage("rewardStatus.notCollected");
          } else {
            status = "§cVocê precisa de " + (necessaryCactus - playerCactus) + " cactos a mais";
          }

          String formattedLine = line
              .replace("%cactus%", String.valueOf(necessaryCactus))
              .replace("%status%", status);
          formattedLore.add(plugin.getMessageUtils().colorize(formattedLine));
        }
        meta.setLore(formattedLore);
      }

      itemStack.setItemMeta(meta);
    }

    return itemStack;
  }

  /**
   * Cria um item de vidro colorido para decoração dos menus
   */
  public ItemStack createGlassPane() {
    return new ItemStack(Material.valueOf("STAINED_GLASS_PANE"), 1, (short) 7); // Cinza claro
  }

  /**
   * Cria um item personalizado com material, nome e lore
   */
  public ItemStack createCustomItem(Material material, String name, List<String> lore) {
    return createCustomItem(material, (short) 0, name, lore);
  }

  /**
   * Cria um item personalizado com material, data, nome e lore
   */
  public ItemStack createCustomItem(Material material, short data, String name, List<String> lore) {
    ItemStack item = new ItemStack(material, 1, data);
    ItemMeta meta = item.getItemMeta();

    if (meta != null) {
      if (name != null) {
        meta.setDisplayName(plugin.getMessageUtils().colorize(name));
      }

      if (lore != null && !lore.isEmpty()) {
        List<String> formattedLore = new ArrayList<>();
        for (String line : lore) {
          formattedLore.add(plugin.getMessageUtils().colorize(line));
        }
        meta.setLore(formattedLore);
      }

      item.setItemMeta(meta);
    }

    return item;
  }

  /**
   * Cria uma cabeça de jogador
   */
  public ItemStack createPlayerHead(String playerName) {
    ItemStack skull = new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (short) 3);
    SkullMeta meta = (SkullMeta) skull.getItemMeta();

    if (meta != null) {
      meta.setOwner(playerName);
      skull.setItemMeta(meta);
    }

    return skull;
  }

  /**
   * Cria uma cabeça com textura personalizada
   */
  private ItemStack createSkullWithTexture(String texture) {
    ItemStack skull = new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (short) 3);

    try {
      // Aplicar textura usando reflection para Minecraft 1.8
      SkullMeta skullMeta = (SkullMeta) skull.getItemMeta();

      // Criar GameProfile com textura
      Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
      Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");

      Object gameProfile = gameProfileClass.getConstructor(java.util.UUID.class, String.class)
          .newInstance(java.util.UUID.randomUUID(), "CustomHead");

      Object property = propertyClass.getConstructor(String.class, String.class)
          .newInstance("textures", texture);

      Object propertyMap = gameProfile.getClass().getMethod("getProperties").invoke(gameProfile);
      propertyMap.getClass().getMethod("put", Object.class, Object.class).invoke(propertyMap, "textures",
          property);

      // Aplicar GameProfile ao SkullMeta usando reflection
      java.lang.reflect.Field profileField = skullMeta.getClass().getDeclaredField("profile");
      profileField.setAccessible(true);
      profileField.set(skullMeta, gameProfile);

      skull.setItemMeta(skullMeta);

    } catch (Exception e) {
      // Se falhar, usar skull básico
      plugin.getLogger().warning("Falha ao aplicar textura customizada: " + e.getMessage());
    }

    return skull;
  }

  /**
   * Verifica se um item é um gerador de cactos
   */
  public boolean isCactusGenerator(ItemStack item) {
    if (item == null || item.getType() != Material.SKULL_ITEM) {
      return false;
    }

    if (!item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
      return false;
    }

    String displayName = item.getItemMeta().getDisplayName();
    return displayName.contains("GERADOR DE CACTOS");
  }

  /**
   * Verifica se um item é um booster
   */
  public boolean isBooster(ItemStack item, String boosterId) {
    if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
      return false;
    }

    String expectedName = plugin.getConfigManager().getBoosterName(boosterId);
    String itemName = item.getItemMeta().getDisplayName();

    return itemName.equals(plugin.getMessageUtils().colorize(expectedName));
  }

  // Métodos utilitários
  private String formatPrice(double price) {
    if (price >= 1000000) {
      return String.format("%.1fM", price / 1000000);
    } else if (price >= 1000) {
      return String.format("%.1fK", price / 1000);
    } else {
      return String.format("%.0f", price);
    }
  }

  private String formatTime(int minutes) {
    if (minutes >= 60) {
      int hours = minutes / 60;
      int remainingMinutes = minutes % 60;
      if (remainingMinutes > 0) {
        return hours + "h " + remainingMinutes + "m";
      } else {
        return hours + "h";
      }
    } else {
      return minutes + "m";
    }
  }
}
