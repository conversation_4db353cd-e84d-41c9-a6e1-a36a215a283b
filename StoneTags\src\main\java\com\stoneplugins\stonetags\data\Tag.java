package com.stoneplugins.stonetags.data;

import java.util.List;

public class Tag {

    private final String id;
    private final String name;
    private final String prefix;
    private final String suffix;
    private final String color;
    private final String icon;
    private final String permission;
    private final int position;
    private final List<String> description;
    private final String preview;
    private final String tabPrefix;
    private final String tabSuffix;
    private final String aboveName;
    private final boolean invisible;
    private final boolean animated;
    private final List<String> animationFrames;

    public Tag(String id, String name, String prefix, String suffix, String color,
            String icon, String permission, int position, List<String> description,
            String preview, String tabPrefix, String tabSuffix, String aboveName,
            boolean invisible, boolean animated, List<String> animationFrames) {
        this.id = id;
        this.name = name;
        this.prefix = prefix;
        this.suffix = suffix;
        this.color = color;
        this.icon = icon;
        this.permission = permission;
        this.position = position;
        this.description = description;
        this.preview = preview;
        this.tabPrefix = tabPrefix;
        this.tabSuffix = tabSuffix;
        this.aboveName = aboveName;
        this.invisible = invisible;
        this.animated = animated;
        this.animationFrames = animationFrames;
    }

    // Getters
    public String getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getPrefix() {
        return prefix;
    }

    public String getSuffix() {
        return suffix;
    }

    public String getColor() {
        return color;
    }

    public String getIcon() {
        return icon;
    }

    public String getPermission() {
        return permission;
    }

    public int getPosition() {
        return position;
    }

    public List<String> getDescription() {
        return description;
    }

    public String getPreview() {
        return preview;
    }

    public String getTabPrefix() {
        return tabPrefix;
    }

    public String getTabSuffix() {
        return tabSuffix;
    }

    public String getAboveName() {
        return aboveName;
    }

    public boolean isInvisible() {
        return invisible;
    }

    public boolean isAnimated() {
        return animated;
    }

    public List<String> getAnimationFrames() {
        return animationFrames;
    }

    // Métodos utilitários
    public boolean hasPermissionRequirement() {
        return permission != null && !permission.isEmpty();
    }

    public boolean hasAboveName() {
        return aboveName != null && !aboveName.isEmpty();
    }

    /**
     * Obtém o prefix para chat (usa o prefix normal se não especificado)
     */
    public String getChatPrefix() {
        return prefix != null ? prefix : "";
    }

    /**
     * Obtém o suffix para chat (usa o suffix normal se não especificado)
     */
    public String getChatSuffix() {
        return suffix != null ? suffix : "";
    }

    public boolean hasDescription() {
        return description != null && !description.isEmpty();
    }

    public String getCurrentPrefix() {
        return getCurrentPrefix(null);
    }

    public String getCurrentPrefix(org.bukkit.entity.Player player) {
        com.stoneplugins.stonetags.StoneTags plugin = com.stoneplugins.stonetags.StoneTags.getInstance();
        if (plugin != null) {
            plugin.getLogger().info("DEBUG getCurrentPrefix: Tag=" + id + ", Animated=" + animated + ", Player="
                    + (player != null ? player.getName() : "null"));
        }

        if (animated) {
            // Para tags animadas, sempre usar sistema RGB
            if (player != null) {
                // Usar AnimationManager para frame RGB específico do jogador
                if (plugin != null && plugin.getAnimationManager() != null) {
                    String animatedFrame = plugin.getAnimationManager().getCurrentFrameText(player);
                    if (plugin != null) {
                        plugin.getLogger().info("DEBUG: Frame RGB do AnimationManager: "
                                + (animatedFrame != null ? animatedFrame : "null"));
                    }
                    if (animatedFrame != null) {
                        return animatedFrame;
                    }
                }
            }

            // Fallback para RGB genérico - SISTEMA SIMPLIFICADO
            String tagName = name.replaceAll("&[0-9a-fk-or]", ""); // Remove códigos de cor
            long currentTime = System.currentTimeMillis() / 200; // Velocidade da animação
            int frameIndex = (int) (currentTime % 7); // 7 cores diferentes

            if (plugin != null) {
                plugin.getLogger()
                        .info("DEBUG: Gerando RGB fallback - TagName=" + tagName + ", FrameIndex=" + frameIndex);
            }

            // Cores fixas para garantir que funcione
            String[] colors = { "&c", "&6", "&e", "&a", "&b", "&9", "&d" };
            String currentColor = colors[frameIndex];

            // Gerar tag colorida simples
            String result = "&8[" + currentColor + tagName + "&8] ";

            if (plugin != null) {
                plugin.getLogger().info("DEBUG: RGB Result SIMPLES=" + result);
            }

            return result;
        }

        if (plugin != null) {
            plugin.getLogger().info("DEBUG: Usando prefix padrão: " + prefix);
        }
        return prefix;
    }

    /**
     * Converte hue para cor do Minecraft (versão simplificada do RainbowGenerator)
     */
    private String getRainbowColor(float hue) {
        // Mapear hue para cores do Minecraft de forma mais distribuída
        hue = hue % 1.0f; // Garantir que está entre 0-1

        if (hue < 0.14f)
            return "&c"; // Vermelho
        if (hue < 0.28f)
            return "&6"; // Laranja
        if (hue < 0.42f)
            return "&e"; // Amarelo
        if (hue < 0.56f)
            return "&a"; // Verde
        if (hue < 0.70f)
            return "&b"; // Aqua
        if (hue < 0.84f)
            return "&9"; // Azul
        return "&d"; // Rosa/Roxo
    }

    @Override
    public String toString() {
        return "Tag{" +
                "id='" + id + '\'' +
                ", name='" + name + '\'' +
                ", position=" + position +
                ", invisible=" + invisible +
                ", animated=" + animated +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        Tag tag = (Tag) obj;
        return id.equals(tag.id);
    }

    @Override
    public int hashCode() {
        return id.hashCode();
    }
}
