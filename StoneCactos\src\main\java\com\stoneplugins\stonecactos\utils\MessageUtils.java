package com.stoneplugins.stonecactos.utils;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.text.DecimalFormat;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MessageUtils {

    private final StoneCactos plugin;
    private final DecimalFormat numberFormat;
    private static final Pattern HEX_PATTERN = Pattern.compile("&#([A-Fa-f0-9]{6})");

    public MessageUtils(StoneCactos plugin) {
        this.plugin = plugin;
        this.numberFormat = new DecimalFormat("#,###.##");
    }

    /**
     * Coloriza uma string com códigos de cor do Minecraft
     */
    public String colorize(String message) {
        if (message == null)
            return "";

        // Suporte para cores hex (1.16+)
        if (isHexSupported()) {
            message = translateHexColorCodes(message);
        }

        // Traduzir códigos de cor padrão
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * Coloriza uma lista de strings
     */
    public List<String> colorize(List<String> messages) {
        if (messages == null)
            return null;

        for (int i = 0; i < messages.size(); i++) {
            messages.set(i, colorize(messages.get(i)));
        }
        return messages;
    }

    /**
     * Envia uma mensagem colorizada para um CommandSender
     */
    public void sendMessage(CommandSender sender, String message) {
        if (message == null || message.isEmpty())
            return;
        sender.sendMessage(colorize(message));
    }

    /**
     * Envia uma mensagem com prefixo para um CommandSender
     */
    public void sendPrefixedMessage(CommandSender sender, String message) {
        String prefix = "&7[&eStoneCactos&7] "; // Prefixo padrão
        sendMessage(sender, prefix + message);
    }

    /**
     * Envia uma mensagem do config para um CommandSender
     */
    public void sendConfigMessage(CommandSender sender, String key) {
        String message = plugin.getConfigManager().getMessage(key);
        sendPrefixedMessage(sender, message);
    }

    /**
     * Obtém uma mensagem colorizada do config
     */
    public String getConfigMessage(String key) {
        return colorize(plugin.getConfigManager().getMessage(key));
    }

    /**
     * Obtém uma mensagem colorizada do config com placeholders do jogador
     */
    public String getConfigMessage(String key, Player player) {
        String message = plugin.getConfigManager().getMessage(key);
        return colorize(replacePlaceholders(message, player));
    }

    /**
     * Obtém uma lista de mensagens colorizada do config
     */
    public List<String> getConfigMessageList(String key) {
        List<String> messages = plugin.getConfigManager().getMessageList(key);
        return colorize(messages);
    }

    /**
     * Obtém uma lista de mensagens colorizada do config com placeholders do jogador
     */
    public List<String> getConfigMessageList(String key, Player player) {
        List<String> messages = plugin.getConfigManager().getMessageList(key);
        for (int i = 0; i < messages.size(); i++) {
            String message = messages.get(i);
            message = replacePlaceholders(message, player);
            messages.set(i, colorize(message));
        }
        return messages;
    }

    /**
     * Envia uma mensagem do config para um jogador
     */
    public void sendConfigMessage(Player player, String key) {
        String message = getConfigMessage(key, player);
        if (!message.isEmpty()) {
            player.sendMessage(message);
        }
    }

    /**
     * Envia uma lista de mensagens do config para um jogador
     */
    public void sendConfigMessageList(Player player, String key) {
        List<String> messages = getConfigMessageList(key, player);
        for (String message : messages) {
            if (!message.isEmpty()) {
                player.sendMessage(message);
            }
        }
    }

    /**
     * Remove todas as cores de uma string
     */
    public String stripColor(String message) {
        if (message == null)
            return "";
        return ChatColor.stripColor(colorize(message));
    }

    /**
     * Formata números grandes
     */
    public String formatNumber(double number) {
        if (number >= 1000000000) {
            return String.format("%.1fB", number / 1000000000);
        } else if (number >= 1000000) {
            return String.format("%.1fM", number / 1000000);
        } else if (number >= 1000) {
            return String.format("%.1fK", number / 1000);
        } else {
            return numberFormat.format(number);
        }
    }

    /**
     * Substitui placeholders básicos do jogador
     */
    public String replacePlaceholders(String message, Player player) {
        if (message == null || player == null)
            return message;

        message = message.replace("{player}", player.getName());
        message = message.replace("{displayname}", player.getDisplayName());
        message = message.replace("{world}", player.getWorld().getName());

        return message;
    }

    /**
     * Verifica se o servidor suporta cores hex
     */
    private boolean isHexSupported() {
        // Para Spigot 1.8.8, não há suporte a cores hex
        return false;
    }

    /**
     * Traduz códigos de cor hex para o formato do Minecraft
     */
    private String translateHexColorCodes(String message) {
        Matcher matcher = HEX_PATTERN.matcher(message);
        StringBuffer buffer = new StringBuffer();

        while (matcher.find()) {
            String hexCode = matcher.group(1);
            try {
                // Para versões 1.8.8, não há suporte a cores hex
                // Manter o código original
                matcher.appendReplacement(buffer, "&#" + hexCode);
            } catch (Exception e) {
                // Se falhar, manter o código original
                matcher.appendReplacement(buffer, "&#" + hexCode);
            }
        }
        matcher.appendTail(buffer);

        return buffer.toString();
    }

    /**
     * Verifica se uma string é numérica
     */
    public boolean isNumeric(String str) {
        if (str == null || str.isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * Formata uma lista de strings
     */
    public String[] colorizeArray(String[] messages) {
        if (messages == null)
            return new String[0];

        String[] colorized = new String[messages.length];
        for (int i = 0; i < messages.length; i++) {
            colorized[i] = colorize(messages[i]);
        }

        return colorized;
    }
}
