worlds:
  PlotWorld:
    plot:
      height: 64
      size: 42
      filling:
      - '1:0'
      floor:
      - '2:0'
      bedrock: true
      biome: FOREST
      auto_merge: false
      create_signs: true
    wall:
      block: '44:0'
      block_claimed: '44:1'
      filling: '1:0'
      height: 64
    road:
      width: 7
      height: 64
      block: '155:0'
    misc_spawn_unowned: false
    home:
      nonmembers: side
      default: side
    schematic:
      specify_on_claim: false
      on_claim: false
      file: 'null'
    economy:
      prices:
        merge: 100
        sell: 100
        claim: 100
      use: false
    chat:
      enabled: false
    limits:
      max-members: 128
    world:
      max_height: 256
      gamemode: creative
      min_height: 1
      border: false
    event:
      spawn:
        egg: false
        breeding: false
        custom: true
    natural_mob_spawning: false
    mob_spawner_spawning: false
    flags: {}
