package com.stoneplugins.spawnershop.commands;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.gui.MainMenu;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class SpawnerShopCommand implements CommandExecutor {

    private final SpawnerShop plugin;

    public SpawnerShopCommand(SpawnerShop plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
            return true;
        }

        Player player = (Player) sender;

        // Sempre abrir menu principal
        try {
            new MainMenu(plugin, player).open();
        } catch (Exception e) {
            player.sendMessage("§cErro ao abrir o menu: " + e.getMessage());
            plugin.getLogger().severe("Erro ao abrir MainMenu: " + e.getMessage());
            e.printStackTrace();
        }
        return true;
    }
}
