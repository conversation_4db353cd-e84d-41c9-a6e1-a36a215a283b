package com.atlasplugins.pescaria.items;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.enchants.CustomEnchantments;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.UUID;

public class FishingRodManager {

    private final Pescaria plugin;
    private final CustomEnchantments customEnchantments;

    public FishingRodManager(Pescaria plugin) {
        this.plugin = plugin;
        this.customEnchantments = new CustomEnchantments(plugin);
    }

    public ItemStack createFishingRod(Player player) {
        UUID uuid = player.getUniqueId();
        int xp = getFishingRodXP(uuid);
        int level = calculateLevel(xp);

        ItemStack fishingRod = new ItemStack(Material.FISHING_ROD);
        ItemMeta meta = fishingRod.getItemMeta();

        meta.setDisplayName(ChatColor.YELLOW + "Vara de Pesca " +
                ChatColor.GRAY + "[Nível " + level + "] " +
                ChatColor.YELLOW + "❋" + String.format("%04d", Math.abs(fishingRod.hashCode() % 10000)));

        meta.setLore(Arrays.asList(
                ChatColor.GRAY + "XP: " + ChatColor.GREEN + xp + ChatColor.GRAY + "/" + getXPForNextLevel(level),
                "",
                ChatColor.GRAY + "Sorte do Mar " + ChatColor.AQUA + "I",
                ChatColor.GRAY + "Pilhagem " + ChatColor.AQUA + "I",
                "",
                ChatColor.GRAY + "Pilhagem: " + ChatColor.WHITE + "1",
                ChatColor.DARK_GRAY + "minecraft:fishing_rod",
                ChatColor.DARK_GRAY + "NBT: 1 tag(s)"));

        fishingRod.setItemMeta(meta);

        // NÃO adicionar encantamentos vanilla - usar apenas sistema customizado
        customEnchantments.loadPlayerEnchants(uuid);

        // Aplicar apenas encantamentos customizados na lore
        customEnchantments.updateFishingRodLore(player, fishingRod);
        return fishingRod;
    }

    public boolean isFishingRod(ItemStack item) {
        if (item == null || item.getType() != Material.FISHING_ROD) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        String displayName = meta.getDisplayName();
        return displayName.contains("Vara de Pesca") && displayName.contains("[Nível");
    }

    public void giveFishingRod(Player player) {
        // Verificar se o jogador já tem uma vara de pesca customizada
        for (ItemStack item : player.getInventory().getContents()) {
            if (isFishingRod(item)) {
                return; // Já tem uma vara
            }
        }

        // Dar a vara de pesca
        ItemStack fishingRod = createFishingRod(player);
        player.getInventory().addItem(fishingRod);
        player.sendMessage("§a§lPESCARIA §fVocê recebeu uma vara de pesca!");
    }

    public void addXP(Player player, int amount) {
        UUID uuid = player.getUniqueId();
        int currentXP = getFishingRodXP(uuid);
        int newXP = currentXP + amount;

        setFishingRodXP(uuid, newXP);

        int oldLevel = calculateLevel(currentXP);
        int newLevel = calculateLevel(newXP);

        if (newLevel > oldLevel) {
            player.sendMessage("§a§lPESCARIA §fSua vara de pesca evoluiu para o nível §e" + newLevel + "§f!");
            updateFishingRodInInventory(player);
        }
    }

    public void updateFishingRodInInventory(Player player) {
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (isFishingRod(item)) {
                ItemStack newRod = createFishingRod(player);
                // Atualizar lore com encantamentos customizados
                customEnchantments.updateFishingRodLore(player, newRod);
                player.getInventory().setItem(i, newRod);
                break;
            }
        }
    }

    public void removeFishingRod(Player player) {
        // Salvar encantamentos antes de remover
        UUID uuid = player.getUniqueId();
        customEnchantments.savePlayerEnchants(uuid);

        // Remover todas as varas de pesca customizadas do inventário
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (isFishingRod(item)) {
                player.getInventory().setItem(i, null);
            }
        }
        player.sendMessage("§c§lPESCARIA §fSua vara de pesca foi removida!");
    }

    private int getFishingRodXP(UUID uuid) {
        return plugin.getConfigManager().getDataConfig().getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
    }

    private void setFishingRodXP(UUID uuid, int xp) {
        plugin.getConfigManager().getDataConfig().set("Players." + uuid.toString() + ".FishingRodXP", xp);
        plugin.getConfigManager().saveDataConfig();
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10; // Nível máximo
    }

    private int getXPForNextLevel(int currentLevel) {
        switch (currentLevel) {
            case 1:
                return 250;
            case 2:
                return 500;
            case 3:
                return 1000;
            case 4:
                return 2000;
            case 5:
                return 4000;
            case 6:
                return 8000;
            case 7:
                return 16000;
            case 8:
                return 32000;
            case 9:
                return 64000;
            default:
                return 64000; // Nível máximo
        }
    }
}
