package com.atlasplugins.pescaria.integrations;

import com.atlasplugins.pescaria.Pescaria;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;

public class EconomyIntegration {

    private final Pescaria plugin;
    private Economy vaultEconomy;
    private boolean hasVault = false;
    private boolean hasBetterEconomy = false;
    private boolean hasSCash = false;

    // Tipos de economia suportados
    public enum EconomyType {
        VAULT,
        BETTER_ECONOMY,
        SCASH,
        NONE
    }

    private EconomyType currentEconomyType = EconomyType.NONE;

    public EconomyIntegration(Pescaria plugin) {
        this.plugin = plugin;
        setupEconomy();
    }

    private void setupEconomy() {
        // Verificar Vault
        if (Bukkit.getPluginManager().getPlugin("Vault") != null) {
            RegisteredServiceProvider<Economy> rsp = Bukkit.getServicesManager().getRegistration(Economy.class);
            if (rsp != null) {
                vaultEconomy = rsp.getProvider();
                hasVault = true;
                currentEconomyType = EconomyType.VAULT;
                plugin.getLogger().info("Integração com Vault Economy ativada!");
            }
        }

        // Verificar BetterEconomy
        if (Bukkit.getPluginManager().getPlugin("BetterEconomy") != null) {
            hasBetterEconomy = true;
            if (currentEconomyType == EconomyType.NONE) {
                currentEconomyType = EconomyType.BETTER_ECONOMY;
            }
            plugin.getLogger().info("BetterEconomy detectado!");
        }

        // Verificar sCash
        if (Bukkit.getPluginManager().getPlugin("sCash") != null) {
            hasSCash = true;
            if (currentEconomyType == EconomyType.NONE) {
                currentEconomyType = EconomyType.SCASH;
            }
            plugin.getLogger().info("sCash detectado!");
        }

        if (currentEconomyType == EconomyType.NONE) {
            plugin.getLogger().warning("Nenhum plugin de economia encontrado!");
        }
    }

    public boolean hasEconomy() {
        return currentEconomyType != EconomyType.NONE;
    }

    public double getBalance(Player player) {
        switch (currentEconomyType) {
            case VAULT:
                return hasVault ? vaultEconomy.getBalance(player) : 0.0;
            case BETTER_ECONOMY:
                return getBetterEconomyBalance(player);
            case SCASH:
                return getSCashBalance(player);
            default:
                return 0.0;
        }
    }

    public boolean depositPlayer(Player player, double amount) {
        switch (currentEconomyType) {
            case VAULT:
                if (hasVault) {
                    return vaultEconomy.depositPlayer(player, amount).transactionSuccess();
                }
                break;
            case BETTER_ECONOMY:
                return depositBetterEconomy(player, amount);
            case SCASH:
                return depositSCash(player, amount);
        }
        return false;
    }

    public boolean withdrawPlayer(Player player, double amount) {
        switch (currentEconomyType) {
            case VAULT:
                if (hasVault) {
                    return vaultEconomy.withdrawPlayer(player, amount).transactionSuccess();
                }
                break;
            case BETTER_ECONOMY:
                return withdrawBetterEconomy(player, amount);
            case SCASH:
                return withdrawSCash(player, amount);
        }
        return false;
    }

    public String formatMoney(double amount) {
        switch (currentEconomyType) {
            case VAULT:
                return hasVault ? vaultEconomy.format(amount) : String.format("%.2f", amount);
            case BETTER_ECONOMY:
                return "$" + String.format("%.2f", amount);
            case SCASH:
                return String.format("%.0f", amount) + " Cash";
            default:
                return String.format("%.2f", amount);
        }
    }

    // Métodos para BetterEconomy (usando comandos)
    private double getBetterEconomyBalance(Player player) {
        try {
            // Usar integração direta com BetterEconomy
            BetterEconomyIntegration betterEco = new BetterEconomyIntegration(plugin);
            if (betterEco.isEnabled()) {
                return betterEco.getBalance(player);
            }
        } catch (Exception e) {
            // Fallback para 0 se não conseguir obter
        }
        return 0.0;
    }

    private boolean depositBetterEconomy(Player player, double amount) {
        try {
            // Executar comando como console
            String command = "eco give " + player.getName() + " " + amount;
            return Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean withdrawBetterEconomy(Player player, double amount) {
        try {
            // Executar comando como console
            String command = "eco take " + player.getName() + " " + amount;
            return Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            return false;
        }
    }

    // Métodos para sCash (usando comandos)
    private double getSCashBalance(Player player) {
        try {
            // Usar integração direta com sCash
            SCashIntegration sCash = new SCashIntegration(plugin);
            if (sCash.isEnabled()) {
                return sCash.getBalance(player);
            }
        } catch (Exception e) {
            // Fallback para 0 se não conseguir obter
        }
        return 0.0;
    }

    private boolean depositSCash(Player player, double amount) {
        try {
            // Executar comando como console
            String command = "scash give " + player.getName() + " " + (int) amount;
            return Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            return false;
        }
    }

    private boolean withdrawSCash(Player player, double amount) {
        try {
            // Executar comando como console
            String command = "scash take " + player.getName() + " " + (int) amount;
            return Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            return false;
        }
    }

    public EconomyType getCurrentEconomyType() {
        return currentEconomyType;
    }

    public String getEconomyName() {
        switch (currentEconomyType) {
            case VAULT:
                return "Vault Economy";
            case BETTER_ECONOMY:
                return "BetterEconomy";
            case SCASH:
                return "sCash";
            default:
                return "Nenhuma";
        }
    }
}
