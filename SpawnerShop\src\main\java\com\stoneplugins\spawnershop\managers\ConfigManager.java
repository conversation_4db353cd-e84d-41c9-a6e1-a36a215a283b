package com.stoneplugins.spawnershop.managers;

import com.stoneplugins.spawnershop.SpawnerShop;
import org.bukkit.configuration.file.FileConfiguration;

import java.util.List;

public class ConfigManager {

    private final SpawnerShop plugin;
    private FileConfiguration config;

    public ConfigManager(SpawnerShop plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }

    public void reloadConfig() {
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }

    // Database Settings
    public String getDatabaseType() {
        return config.getString("General.sqlType", "SQLITE");
    }

    public String getMySQLHost() {
        return config.getString("MySQL.host", "localhost");
    }

    public String getMySQLUser() {
        return config.getString("MySQL.user", "root");
    }

    public String getMySQLPassword() {
        return config.getString("MySQL.password", "");
    }

    public String getMySQLDatabase() {
        return config.getString("MySQL.database", "db");
    }

    public String getSQLiteFile() {
        return config.getString("SQLite.file", "database.db");
    }

    // General Settings
    public String getEconomyPlugin() {
        return config.getString("General.economyPlugin", "VAULT");
    }

    public String getPermissionsPlugin() {
        return config.getString("General.permissionsPlugin", "PERMISSIONSEX");
    }

    public boolean isLimitEnabled() {
        return config.getBoolean("General.useLimite", true);
    }

    public int getDefaultLimit() {
        return config.getInt("General.limitePadrao", 64);
    }

    // Limit Item Settings
    public String getLimitItem() {
        return config.getString("General.itemLimite.item", "NETHER_STAR:0");
    }

    public boolean isLimitItemGlow() {
        return config.getBoolean("General.itemLimite.glow", true);
    }

    public String getLimitItemName() {
        return config.getString("General.itemLimite.name", "&aLimite de compra");
    }

    public List<String> getLimitItemLore() {
        return config.getStringList("General.itemLimite.lore");
    }

    // Discount Settings
    public List<String> getDiscounts() {
        return config.getStringList("Descontos");
    }

    // Messages
    public String getMessage(String key) {
        return config.getString("Mensagens." + key, "&cMensagem não encontrada: " + key);
    }

    public List<String> getMessageList(String key) {
        return config.getStringList("Mensagens." + key);
    }

    // Menu Settings
    public String getMenuName(String menu) {
        return config.getString("Menus." + menu + ".name", "Menu");
    }

    public int getMenuSize(String menu) {
        return config.getInt("Menus." + menu + ".size", 54);
    }

    public int getMenuArrowBackPage(String menu) {
        return config.getInt("Menus." + menu + ".arrowBackPage", 45);
    }

    public int getMenuArrowNextPage(String menu) {
        return config.getInt("Menus." + menu + ".arrowNextPage", 53);
    }

    public String getMenuSlots(String menu) {
        return config.getString("Menus." + menu + ".slots", "");
    }

    public boolean isMenuAutoComplete(String menu) {
        return config.getBoolean("Menus." + menu + ".autoComplete", true);
    }

    public List<String> getSpawnerLore() {
        return config.getStringList("Menus.main.spawners.lore");
    }

    public String getUnavailableSpawnerItem() {
        return config.getString("Menus.main.spawnerUnavailable.item", "");
    }

    public String getUnavailableSpawnerName() {
        return config.getString("Menus.main.spawnerUnavailable.name", "&cIndisponivel");
    }

    public List<String> getUnavailableSpawnerLore() {
        return config.getStringList("Menus.main.spawnerUnavailable.lore");
    }

    // Profile Settings
    public int getProfileSlot() {
        return config.getInt("Menus.main.profile.slot", 3);
    }

    public String getProfileName() {
        return config.getString("Menus.main.profile.name", "&eSeu usuário");
    }

    public List<String> getProfileLore() {
        return config.getStringList("Menus.main.profile.lore");
    }

    // Ranking Settings
    public int getRankingSlot() {
        return config.getInt("Menus.main.ranking.slot", 5);
    }

    public String getRankingItem() {
        return config.getString("Menus.main.ranking.item", "");
    }

    public String getRankingName() {
        return config.getString("Menus.main.ranking.name", "&eTop compradores");
    }

    public List<String> getRankingLore() {
        return config.getStringList("Menus.main.ranking.lore");
    }

    // Ranking Menu Settings
    public int getRankingBackArrow() {
        return config.getInt("Menus.ranking.backArrow", 40);
    }

    public String getRankingSlots() {
        return config.getString("Menus.ranking.slots", "");
    }

    public String getRankingPlayerName() {
        return config.getString("Menus.ranking.rank.name", "&f{pos}º {grupo}&7{jogador}");
    }

    public List<String> getRankingPlayerLore() {
        return config.getStringList("Menus.ranking.rank.lore");
    }

    public FileConfiguration getConfig() {
        return config;
    }
}
