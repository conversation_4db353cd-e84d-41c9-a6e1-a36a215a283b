# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# Translate by Protocodyne Korean Translate team 
# <AUTHOR> <EMAIL>, 2015.
confirm:
  failed_confirm: $2이 작업이 완료되기 위한 행동이 당신에게 존재하지 않습니다!
  requires_confirm: '$2정말로 실행되길 원하나요: $1%s$2?&-$2이 작업은 되돌릴 수 없습니다! 확실하다면 다음 명령어를 입력하세요:
    $1/plot confirm'
  expired_confirm: $2확인이 만료되었습니다. 명령을 다시 실행하십시오.!
move:
  move_success: $4성공적으로 땅이 옮겨졌습니다.
  copy_success: $4성공적으로 땅이 복사되었습니다.
  requires_unowned: $2이 지역은 이미 사용되고 있습니다.
web:
  generating_link: $1땅을 생성중입니다...
  generating_link_failed: $2다운로드 링크 생성에 실패하였습니다!
  save_failed: $2저장에 실패하였습니다.
  load_null: $2/plot load $4명령어를 사용하여 $2schematics 리스트를 가져오십시오
  load_failed: $2schematic 로드를 실패하였습니다.
  load_list: '$2schematic로드를 원한다면, 다음명령어를 사용하세요 $1/plot load #'
  save_success: $1성공적으로 저장되었습니다!
compass:
  compass_target: $4나침반과 함께 음모를 성공적으로 지정했습니다.
cluster:
  cluster_available_args: '$1다음 하위명령어가 사용 가능합니다.: $4list$2, $4create$2, $4delete$2,
    $4resize$2, $4invite$2, $4kick$2, $4leave$2, $4members$2, $4info$2, $4tp$2, $4sethome'
  cluster_list_heading: $2이 월드에 $1%s$2 cluster가 있습니다
  cluster_list_element: $2 - $1%s&-
  cluster_intersection: $2제안 된 영역이 겹칩니다 와 $1%s$2 기존의 cluster
  cluster_added: $4cluster가 성공적으로 생성되었습니다.
  cluster_deleted: $4cluster가 성공적으로 삭제되었습니다.
  cluster_resized: $4cluster가 성공적으로 재조정 되었습니다.
  cluster_added_user: $4cluster에 성공적으로 유저가 추가되었습니다.
  cannot_kick_player: $2당신은 해당 플레이어를 추방 할 수 없습니다.
  cluster_invited: '$1당신은 다음 cluster으로 초대받았습니다: $2%s'
  cluster_removed: '$1당신은 다음cluster에서 삭제되었습니다: $2%s'
  cluster_kicked_user: $4성공적으로 해당 유저를 추방 하였습니다
  invalid_cluster: '$1올바르지 않은 cluster 이름입니다: $2%s'
  cluster_not_added: $2해당 플레이어는  plot cluster 추가되지 않았습니다
  cluster_cannot_leave: $1당신은 떠나기전에 cluster를 반드시 삭제하거나 소유권을 이전해야합니다
  cluster_added_helper: $4helper가 성공적으로 cluster에 추가되었습니다
  cluster_removed_helper: $4helper가 성공적으로 cluster에서 제거되었습니다
  cluster_regenerated: $4성공적으로 cluster가 재생성 되었습니다
  cluster_teleporting: $4이동중...
  cluster_info: '$1현재 cluster: $2%id%&-$1이름: $2%name%&-$1소유자: $2%owner%&-$1크기: $2%size%&-$1Rights:
    $2%rights%'
  cluster_outside: '$2제안 된 영역이 플롯 영역 밖에 있습니다: %s0'
border:
  border: $2당신은 현재 지도의 경계 밖에 있습니다
unclaim:
  unclaim_success: $4성공적으로 땅을 판매하였습니다.
  unclaim_failed: $2땅을 판매할 수 없습니다
worldedit masks:
  worldedit_delayed: $2당신의 WorldEdit 행동을 처리중이니 기다려주시기 바랍니다...
  worldedit_run: '$2처리가 지연되어 죄송합니다. 해당 명령어를 실행합니다: %s'
  require_selection_in_mask: $2%s 선택한 마스크가 플롯 마스크 안에 있지 않습니다..
    플롯 내에서만 편집 할 수 있습니다..
  worldedit_volume: $2당신은 a volume of %current%를 선택 할 수 없습니다. 당신이  수정할 수 있는 최대부피는 %max% 입니다.
  worldedit_iterations: $2당신은 %current%번 반복 적용시킬 수 없습니다. 당신이 수정가능한 최대 부피는 %max% 입니다.
  worldedit_unsafe: $2해당 명령어는 실행 될 수 없습니다.
  worldedit_bypass: $2&o당신의 제한을 우회하고 싶다면 $4/plot wea를 사용하십시오
  worldedit_bypassed: $2현재 WorldEdit의 제한을 수정하는 중입니다.
  worldedit_unmasked: $1당신의  WorldEdit 제한이 제거되었습니다.
  worldedit_restricted: $1당신의 WorldEdit은 제한됩니다.
gamemode:
  gamemode_was_bypassed: $1당신은 gamemode를 무시합니다 ($2{gamemode}$1) $1set for $2{plot}
height limit:
  height_limit: $1이 Plot World는 $2{limit}만큼 제한이 있습니다
records:
  record_play: $2%player가 $2플레이 기록을 시작합니다 $1%name
  notify_enter: $2%player가 $2당신의 plot에 입장합니다 ($1%plot$2)
  notify_leave: $2%player가 $2당신의 plot를 떠납니다 ($1%plot$2)
swap:
  swap_overlap: $2해당 구역은 덮어쓰기가 허용되지 않습니다.
  swap_dimensions: $2해당 지역은 반드시 비교 가능한 차원이 있어야 합니다.
  swap_syntax: $2/plot swap <plot id>
  swap_success: $4성공적으로 땅이 교환되었습니다
  started_swap: $2땅 교환이 시작되었습니다. 작업 완료시 장신에게 통보 될 예정입니다.
comment:
  inbox_notification: '%s 읽지않은 메세지가 있습니다. 다음 명령어를 사용해주세요 /plot inbox'
  not_valid_inbox_index: $2메일함에 comment가 존재하지 않습니다 %s
  inbox_item: $2 - $4%s
  comment_syntax: $2다음 명령어를 사용해주세요 /plot comment [X;Z] <%s> <comment>
  invalid_inbox: '$2올바르지 않은 메일함 입니다..&-$1Accepted values: %s'
  no_perm_inbox: $2당신은 해당 메일함에 권한이 없습니다
  no_perm_inbox_modify: $2당신은 해당 메일함을 수정할 권한이 없습니다
  no_plot_inbox: $2당신은 서 있거나 줄거리를 제공해야합니다
  comment_removed: $4comment가 성공적으로 삭제되었습니다/s:n$2 - '$3%s$2'
  comment_added: $4A comment가 남아있습니다
  comment_header: $2&m---------&r $1댓글 $2&m---------&r
  inbox_empty: $2comment가 없습니다
console:
  not_console: $2보안상의 이유로, 이 명령어는 Console에서만 입력 가능합니다.
  is_console: $2해당 명령어는 Player만 사용 가능합니다.
inventory:
  inventory_usage: '&c시용: &6{usage}'
  inventory_desc: '&c설명: &6{desc}'
  inventory_category: '&c카테고리: &6{category}'
clipboard:
  clipboard_set: $2현재 땅은 당신의 클리보드로 복사되었습니다, 다음명령어를 사용하여 $1/plot paste$2 붙여넣으세요.
  pasted: $4해당 땅은 성공적으로 붙여넣어졌습니다. 해당 땅은 클립보드로부터 제거됩니다.
  paste_failed: '$2선택된 땅의 복사가 실패했습니다. 사유: $2%s'
  no_clipboard: $2클립보드에 복사된 땅이 존재하지 않습니다
  clipboard_info: '$2현재 선택 - Plot ID: $1%id$2, Width: $1%width$2, Total Blocks: $1%total$2'
toggle:
  toggle_enabled: '$2가능한 세팅: %s'
  toggle_disabled: '$2불가능한 세팅: %s'
blocked command:
  command_blocked: $2해당 명령어는 이 plot에서 허가되지 않았습니다
done:
  done_already_done: $2이 땅은 이미 완료로 표기 되어 있습니다.
  done_not_done: $2이 땅은 완료로 표기되어 있지 않습니다.
  done_insufficient_complexity: $2이 땅은 너무 간단합니다. 이 명령어를 사용하기전 더 많은 정보를 입력해주시기 바랍니다.
  done_success: $1성공적으로 이 땅에 완료표시를 하였습니다.
  done_removed: $1이 땅에 건축을 진행해도 좋습니다.
ratings:
  rating_not_valid: $2당신은 1 과 10 사이의 숫자를 입력해야 합니다
  rating_already_exists: $2당신은 이미 이 땅에 투표하였습니다 $2%s
  rating_applied: $4성공적으로 투표되었습니다 $2%s
  rating_not_your_own: $2당신은 자신의 땅에 투표 할 수 없습니다
  rating_not_done: $2당신은 완료된 땅에만 투표 할 수 있습니다.
  rating_not_owned: $2당신은 소유자가 없는 땅에 투표 할 수 없습니다
  ratings_purged: $2Purged ratings for this plot
tutorial:
  rate_this: $2이 땅에 투표해보세요!
  comment_this: '$2이땅에 피드백을 남겨보세요: %s'
economy:
  econ_disabled: $2Economy 가 활성화 되있지 않습니다
  cannot_afford_plot: $2당신은 이 땅을 구입 할 수 없습니다. 이 땅의 가격은 $1%s 입니다
  not_for_sale: $2이 땅은 판매 불가능합니다
  cannot_buy_own: $2당신은 본인의 땅을 구입 불가능 합니다
  plot_sold: $4당신의 땅; $1%s0$4, 판매 된 $1%s1$4 에 대한 $1$%s2
  cannot_afford_merge: $2당신은 이 땅을 함칠 수 없습니다. 이 땅의 가격은 $1%s 입니다
  added_balance: $1%s $2이 당신의 통장에 추가되었습니다
  removed_balance: $1%s $2이 당신의 통장에서 빠져나갔습니다
  removed_granted_plot: $2너는 사용했다 %s 계획 교부금 (들), 당신이있어 $1%s $2왼쪽
setup:
  setup_init: '$1용법: $2/plot setup <value>'
  setup_step: '$3[$1Step %s0$3] $1%s1 $2- $1기대: $2%s2 $1태만: $2%s3'
  setup_invalid_arg: '$2%s0 는 올바르지 않은 값입니다  %s1. 설정 취소를 원하시면 다음 명령어를 사용하십시오: $1/plot
    setup cancel'
  setup_valid_arg: $2Value $1%s0 $2set to %s1
  setup_finished: $4당신은 만들어진 World로 이동되있어야 합니다. 그렇지 않으면 당신은 bukkit.yml를 통하여 워드를 설정하거나
    다중월드 플러그인을 사용하여 월드를 생성하여야 합니다.
  setup_world_taken: $2%s 는 이미 등록된 Plot World 입니다
  setup_missing_world: $2당신은 월드의 이름을 지정해야 합니다 ($1/plot setup &l<world>$1 <generator>$2)&-$1Additional
    commands:&-$2 - $1/plot setup <value>&-$2 - $1/plot setup back&-$2 - $1/plot setup
    cancel
  setup_missing_generator: $2당신은 생성기를 지정해야 합니다 ($1/plot setup <world> &l<generator>&r$2)&-$1Additional
    commands:&-$2 - $1/plot setup <value>&-$2 - $1/plot setup back&-$2 - $1/plot setup
    cancel
  setup_invalid_generator: '$2올바르지 않은 생성기 입니다. 가능한 옵션: %s'
schematics:
  schematic_missing_arg: '$2당신은 인수를 지정해야 합니다. 가능한 값: $1test <name>$2 , $1save$2 ,
    $1paste $2, $1exportall'
  schematic_invalid: '$2이것은 올바르지 않은 schematic파일 입니다. 사유: $2%s'
  schematic_valid: $2이것은 올바른 schematic파일 입니다
  schematic_paste_failed: $2schematic 적용에 실패하엿습니다
  schematic_paste_success: $4schematic이 성공적으로 적용되었습니다
  schematic_too_large: $2플롯이 너무 커서이 작업을 수행 할 수 없습니다!
titles:
  title_entered_plot: '$1Plot: %world%;%x%;%z%'
  title_entered_plot_sub: $4에게 소유 된 %s
  prefix_greeting: '$1%id%$2> '
  prefix_farewell: '$1%id%$2> '
core:
  task_start: 작업을 시작합니다...
  prefix: $3[$1P2$3] $2
  enabled: $1PlotSquared 가 실행됩니다
reload:
  reloaded_configs: $1번역 파일 및 월드 설정 파일이 성공적으로 리로드 되었습니다
  reload_failed: $2설정파일의 리로드에 실패하였습니다
desc:
  desc_set: $2Plot 설명 설정
  desc_unset: $2Plot 설명 설정취소
  missing_desc: $2당신은 설명을 지정해야 합니다
alias:
  alias_set_to: $2땅의 별명을 $1%alias%로 성정합니다
  missing_alias: $2당신은 별명을 설정해야 합니다
  alias_too_long: $2별명은 50자 이내여야 합니다
  alias_is_taken: $2이 별명은 이미 존재합니다
  alias_removed: $2플롯 별칭이 제거되었습니다
position:
  missing_position: '$2당신은 위치를 지정해야합니다. 가능한 값: $1none'
  position_set: $1Home 위치를 현재 위치로 지정합니다
  position_unset: $1Home 위치를 기본위치로 초기화 합니다
  home_argument: $2다음 명령어를 사용하십시오 /plot set home [none]
  invalid_position: $2해당 위치는 유효하지 않습니다
time:
  time_format: $1%시간%, %분%, %초%
permission:
  no_schematic_permission: $2당신은 schematic을 이용하기 위한 권한이 없습니다 $1%s
  no_permission: '$2당신은 해당 퍼미션 노드가 존재하지 않습니다: $1%s'
  no_permission_event: '$2당신은 해당 퍼미션 노드가 존재하지 않습니다: $1%s'
  no_plot_perms: $2이 행동을 하기 위해선 반드시 땅의 소유자여야 합니다
  cant_claim_more_plots: $2당신은 더 많은 땅들을 소유할 수 없습니다
  cant_transfer_more_plots: $2당신은 해당 유저에게 더 많은 땅을 보낼 수 없습니다
  cant_claim_more_plots_num: $2당신은 한번에 $1%s $2plots보다 더 많이 소유할 수 없습니다
  you_be_denied: $2당신은 이 땅에 들어갈 수 없습니다
  merge_request_confirm: 병합요청이 들어왔습니다 %s
  cant_claim_more_clusters: $2더 많은 클러스터를 청구 할 수 없습니다.
merge:
  merge_not_valid: $2이 병합 요청은 더이상 유효하지 않습니다
  merge_accepted: $2병합 요청이 수락되었습니다
  success_merge: $2땅이 병합되었습니다
  merge_requested: $2병합요청이 성공적으로 전송되었습니다
  no_perm_merge: '$2당신은 이 땅의 소유자가 아닙니다: $1%plot%'
  no_available_automerge: $2당신은 지정한 방향으로 인접한 plots를 소유하지 않았거나 plots를 필요한 크기로 합병 할
    수 없습니다.
  unlink_required: $2An unlink는 이것을 하는데 요구 됩니다.
  unlink_impossible: $2당신은 오직 mega-plot만 unlink 할 수 있습니다
  unlink_success: $2성공적으로 plots을 unlink 하였습니다.
errors:
  invalid_player_wait: '$2플레이어를 찾지 못했습니다: $1%s$2, fetching it. 잠시 후 시도해주세요.'
  invalid_player_offline: '$2플레이어는 반드시 접속 중이어야 합니다: $1%s.'
  invalid_player: '$2플레이어를 찾지 못했습니다: $1%s.'
  command_went_wrong: $2해당 명령어를 실행 중 오류가 발생 하였습니다...
  no_free_plots: $2그곳에는 사용가능한 Free Plot이 없습니다
  not_in_plot: $2당신은 땅에 있지 않습니다
  not_in_cluster: $2당신은 그 행동을 하기 위해서 plot cluster 내부에 있어야만 합니다.
  not_in_plot_world: $2당신은 plot world에 있지 않습니다.
  plotworld_incompatible: $2해당 월드는 호환되지 않습니다
  not_valid_world: $2옳바르지 않은 World 입니다
  not_valid_plot_world: $2올바르지 않은 plot world 입니다 (case sensitive)
  no_plots: $2당신은 어떤 땅도 가지고 있지 않습니다
  wait_for_timer: $2A setblock 타이머는 현재의 땅 또는 당신에게 묶여있습니다. 완료까지 잠시 기다려주시기 바랍니다
  invalid_command_flag: '$2잘못된 명령 플래그: %s0'
  error: '$2오류가 발생했습니다: %s'
  not_loaded: $2플롯을로드 할 수 없습니다
paste:
  debug_report_created: '$1전체 디버그를에 업로드했습니다.: $1%url%'
purge:
  purge_success: $4%s 땅들을 성공적으로 제거하였습니다
trim:
  trim_in_progress: 최적화 작업이 이미 진행중입니다!
  not_valid_hybrid_plot_world: 이 행동을 위하여 hybrid plot manager가 필요합니다.
block list:
  block_list_separater: '$1,$2 '
biome:
  need_biome: $2당신은 유효한 생태계를 지정해야 합니다.
  biome_set_to: $2플롯 생물 설정 $2
teleport:
  teleported_to_plot: $1당신은 이동 되었습니다.
  teleported_to_road: $2당신은 길로 이동 되었습니다.
  teleport_in_seconds: $1%s초 후 이동됩니다. 움직이지 마세요...
  teleport_failed: $2이동이 움직임 또는 데미지 때문에 취소되었습니다.
set block:
  set_block_action_finished: $1마지막 setblock 행동이 이제 끝났습니다.
unsafe:
  debugallowunsafe_on: $2안전하지 않은 행동을 허가합니다
  debugallowunsafe_off: $2안전하지 않은 행동을 차단합니다
debug:
  debug_header: $1Debug Information&-
  debug_section: $2>> $1&l%val%
  debug_line: $2>> $1%var%$2:$1 %val%&-
invalid:
  not_valid_data: $2그것은 유효한 데이터 ID가 아닙니다.
  not_valid_block: '$2그것은 유효한 블록이 아닙니다.: %s 그것은 유효한 블록이 아닙니다.'
  not_allowed_block: '$2그 블록은 허락되지 않았습니다 : %s'
  not_valid_number: $2그것은 %s범위내에서 유효하지 않은 숫자입니다.
  not_valid_plot_id: $2그 땅의 ID는 유효하지 않습니다.
  plot_id_form: '$2땅의 ID는 반드시 형식에 있어야 합니다: $1X;Y $2e.g. $1-5;7'
  not_your_plot: $2그것은 당신의 땅이 아닙니다.
  no_such_plot: $2거기엔 어떤 땅도 없습니다.
  player_has_not_been_on: $2해당 플레이어는 plotworld에 없습니다.
  found_no_plots: $2당신의 검색에서 아무 땅도 발견하지 못했습니다
  found_no_plots_for_player: '$2No plots found for player: %s'
camera:
  camera_started: $2당신은 땅을 위한 카메라 모드에 입장했습니다 $1%s
  camera_stopped: $2카메라 모드를 종료합니다
need:
  need_plot_number: $2당신은 땅의 번호 또는 별칭을 지정해야합니다.
  need_block: $2당신은 블록을 지정해야합니다
  need_plot_id: $2당신은 Plot ID를 지정할 수 있습니다
  need_plot_world: $2당신은 Plot world를 지정할 수 있습니다
  need_user: $2당신은 유저이름을 지정할 필요가 있습니다
info:
  none: None
  unknown: Unknown
  everyone: Everyone
  plot_unowned: $2이 행동을 실행하기 위해선 현재 plot에 소유자가 있어야 합니다
  plot_info_unclaimed: $2땅 $1%s$2 이 아직 판매되지 않았습니다
  plot_info_header: $3&m---------&r $1INFO $3&m---------
  plot_info: '$1ID: $2%id%$1&-$1Alias: $2%alias%$1&-$1Owner: $2%owner%$1&-$1Biome:
    $2%biome%$1&-$1Can Build: $2%build%$1&-$1Rating: $2%rating%&-$1Trusted: $2%trusted%$1&-$1Members:
    $2%members%$1&-$1Denied: $2%denied%$1&-$1Flags: $2%flags%'
  plot_info_footer: $3&m---------&r $1INFO $3&m---------
  plot_info_trusted: $1Trusted:$2 %trusted%
  plot_info_members: $1Members:$2 %members%
  plot_info_denied: $1Denied:$2 %denied%
  plot_info_flags: $1Flags:$2 %flags%
  plot_info_biome: $1Biome:$2 %biome%
  plot_info_rating: $1Rating:$2 %rating%
  plot_info_owner: $1Owner:$2 %owner%
  plot_info_id: $1ID:$2 %id%
  plot_info_alias: $1Alias:$2 %alias%
  plot_info_size: $1Size:$2 %size%
  plot_user_list: ' $1%user%$2,'
  info_syntax_console: $2/plot info X;Y
  now: 지금
  never: 못
  plot_info_seen: $1Seen:$2 %seen%
  plot_flag_list: $1%s0:%s1$2
working:
  generating_component: $1당신의 설정값으로부터 component 생성을 시작합니다
  clearing_plot: $2플롯 비동기 지우기.
  clearing_done: $4명확한 완료. 했다 %sms.
  plot_not_claimed: $2땅이 판매되지 않았습니다
  plot_is_claimed: $2이 땅은 이미 판매되었습니다
  claimed: $4성공적으로 땅을 판매하였습니다
  deleting_done: $4삭제가 완료되었습니다! 가져왔다 %sms.
list:
  comment_list_header_paged: $2(페이지 $1%cur$2/$1%max$2) $1목록 %amount% 코멘트
  clickable: ' (상호 작용하는)'
  plot_list_header_paged: $2(페이지 $1%cur$2/$1%max$2) $1목록 %amount% plots
  plot_list_header: $1목록 %word% plots
  plot_list_item: $2>> $1%id$2:$1%world $2- $1%owner
  plot_list_item_ordered: $2[$1%in$2] >> $1%id$2:$1%world $2- $1%owner
  plot_list_footer: $2>> $1%word% 총 $2%num% $1claimed %plot%.
  area_list_header_paged: $2(페이지 $1%cur$2/$1%max$2) $1목록f %amount% areas
left:
  left_plot: $2당신은 땅을 떠났습니다.
chat:
  plot_chat_format: '$2[$1Plot Chat$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
  plot_chat_forced: $이 월드는 모두가 plot chat을 쓰도록 강제합니다
  plot_chat_on: $4플롯 채팅 사용 설정 됨.
  plot_chat_off: $4플롯 채팅 사용 중지됨.
  plot_chat_spy_format: '$2[$1Plot Spy$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
deny:
  denied_removed: $4당신은 이 땅으로부터 플레이어를 차단 해제 했습니다.
  denied_added: $4당신은 이 땅으로부터 플레이어를 성공적으로 차단 했습니다.
  denied_need_argument: $2변수가 빠졌습니다. $1/plot denied add <name> $2or $1/plot denied
    remove <name>
  was_not_denied: $2해당 플레이어는 이 땅에서 차단되지 않았습니다.
  you_got_denied: $4당신은 해당 땅으로부터 차단되었습니다 따라서 spawn으로 자동 이동 되었습니다.
rain:
  need_on_off: '$2당신은 올바른 값을 선택하여야 합니다. 가능한 값: $1on$2, $1off'
  setting_updated: $4당신은 설정을 성공적으로 업데이트 했습니다
flag:
  flag_key: '$2Key: %s'
  flag_type: '$2Type: %s'
  flag_desc: '$2Desc: %s'
  not_valid_flag: $2그것은 유효한 깃발이 아닙니다
  not_valid_value: $2깃발 값은 무조건 문자 숫자여야만 한다
  flag_not_in_plot: $2그땅은 그 깃발을 가지고 있지 않습니다
  flag_not_removed: $2그 깃발은 제거될 수 없습니다
  flag_not_added: $2그 깃발은 추가될 수 없습니다
  flag_removed: $4깃발이 성공적으로 제거되었습니다
  flag_added: $4깃발이 성공적으로 추가되었습니다
  not_valid_flag_suggested: '$2That is not a valid flag. Did you mean: $1%s'
trusted:
  trusted_added: $4당신은 그 땅에 유저를 성공적으로 위탁했습니다
  trusted_removed: $4당신은 그 땅으로부터 신용(위탁)받은 유저를 성공적으로 제거했습니다.
  was_not_added: $2그 플레이어는 이 땅에서 신용(위탁)받지 못했습니다.
  plot_removed_user: $1당신이 추가되었던 땅 %s가 소유자의 비활성의 이유로 삭제되었습니다.
member:
  removed_players: $2이 땅으로부터 %s 플레이어가 제거되었습니다
  already_owner: $2해당 유저는 이미 땅의 주인입니다
  already_added: $2그 유저는 그 카테고리에 이미 추가되었습니다.
  member_added: $4그 유저는 이제 그 땅의 소유자가 온라인에 있는 동안 지을 수 있습니다.
  member_removed: $1당신은 땅으로부터 유저를 성공적으로 제거되었습니다.
  member_was_not_added: $2그 플레이어는 이 땅에 유저로서 추가되지 않았습니다
  plot_max_members: $2당신은 이 땅에 어떤 플레이어를 더 추가 할 수 없습니다
owner:
  set_owner: $4당신은 땅의 소유자를 성공적으로 설정했습니다
  now_owner: $4당신은 이제 %s땅의 소유자입니다
  set_owner_cancelled: $2The setowner action was cancelled
signs:
  owner_sign_line_1: '$1ID: $1%id%'
  owner_sign_line_2: '$1소유자:'
  owner_sign_line_3: $2%plr%
  owner_sign_line_4: $3Claimed
help:
  help_header: $3&m---------&r $1Plot² 도움말 $3&m---------
  help_page_header: '$1범주: $2%category%$2,$1 페이지: $2%current%$3/$2%max%$2'
  help_footer: $3&m---------&r $1Plot² 도움말 $3&m---------
  help_info_item: $1/plot help %category% $3- $2%category_desc%
  help_item: $1%usage% [%alias%]&- $3- $2%desc%&-
  direction: '$1현재 방향: %dir%'
'-':
  custom_string: '-'
commandconfig:
  not_valid_subcommand: $2이것은 유효하지 않은 하위 명령어 입니다
  did_you_mean: '$2이것을 명령어를 의미하나요: $1%s'
  name_little: $2%s0 아름이 너무 짧습니다, $1%s1$2<$1%s3
  no_commands: $2죄송합니다 당신은 하위명령어 사용을 허가받지 못했습니다.
  subcommand_set_options_header: '$2Possible Values: '
  command_syntax: '$1Usage: $2%s'
  flag_tutorial_usage: '$1관리자에게 국기를 설정하게하십시오: $2%s'
bar api: {}
set:
  set_attribute: $4설정 완료 %s0 로 설정 %s1
cap:
  entity_cap: $2더 많은 몹을 스폰하는 것은 허용되지 않습니다.
near:
  plot_near: '$1선수: %s0'
kick:
  you_got_kicked: $4너는 쫓겨났다
grants:
  granted_plots: '$1결과: $2%s $1grants left'
  granted_plot: $1You granted %s0 plot to $2%s1
  granted_plot_failed: '$1Grant failed: $2%s'
