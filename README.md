# Pescaria - Plugin de Minecraft 1.8.9

Plugin de pescaria com sistema de missões para servidores Minecraft 1.8.9, inspirado no estilo Atlas Plugins.

## Dependências

- [Vault](https://www.spigotmc.org/resources/vault.34315/)
- [LegendChat](https://dev.bukkit.org/projects/legendchat)
- [Multiverse-Core](https://www.spigotmc.org/resources/multiverse-core.390/) (recomendado para criação do mundo de pesca)

## Instalação

1. Baixe o arquivo JAR do plugin
2. Coloque o arquivo na pasta `plugins` do seu servidor
3. Reinicie o servidor
4. Configure o plugin conforme as instruções abaixo

## Configuração

1. Crie um mundo para a área de pesca usando o Multiverse-Core:
   ```
   /mv create pescaria normal
   ```

2. Configure o nome do mundo no arquivo `config.yml`:
   ```yaml
   PescariaWorldName: 'pescaria'
   ```

3. Defina o spawn do servidor:
   ```
   /pesca spawn
   ```

4. Vá até o mundo de pesca e defina o spawn da área de pesca:
   ```
   /mv tp pescaria
   /pesca definir
   ```

## Comandos

### Comandos para Jogadores

- `/pesca` - Mostra o menu principal do plugin
- `/pesca tp` - Teleporta para a área de pesca
- `/pesca voltar` - Volta para o spawn do servidor
- `/pesca info` - Mostra informações sobre o plugin

### Comandos para Administradores

- `/pesca definir` - Define o spawn da área de pesca
- `/pesca spawn` - Define o spawn do servidor

## Permissões

- `pescaria.usar` - Permite usar o comando /pesca (padrão: todos)
- `pescaria.admin` - Permite usar comandos administrativos (padrão: op)
- `pescaria.definir` - Permite definir o spawn da área de pesca (padrão: op)
- `pescaria.spawn` - Permite definir o spawn do servidor (padrão: op)

## Missões

O plugin inclui um sistema de missões configurável no arquivo `missoes.yml`. Cada missão requer um número específico de peixes para ser completada e oferece recompensas personalizáveis.

Exemplo de configuração de missão:

```yaml
Missao01:
  Ordem: 1
  Name: 'Missão #1'
  PeixesToComplete: 10
  RecompensasAtivar: true
  Recompensas:
    01:
      Nome: '15M de Cash'
      Command: 'givecash @player 15000000'
```

## Scoreboard

O plugin inclui uma scoreboard personalizável que mostra informações sobre o progresso do jogador. A configuração da scoreboard pode ser encontrada no arquivo `config.yml`.

## Compilação

Para compilar o plugin a partir do código-fonte:

1. Clone o repositório
2. Execute o comando `mvn clean package`
3. O arquivo JAR será gerado na pasta `target`

## Suporte

Para obter suporte, entre em contato através do site: loja.atlasplugins.com