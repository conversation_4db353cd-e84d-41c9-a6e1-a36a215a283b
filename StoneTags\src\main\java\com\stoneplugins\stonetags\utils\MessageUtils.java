package com.stoneplugins.stonetags.utils;

import com.stoneplugins.stonetags.StoneTags;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class MessageUtils {

    private final StoneTags plugin;
    private static final Pattern HEX_PATTERN = Pattern.compile("&#([A-Fa-f0-9]{6})");

    public MessageUtils(StoneTags plugin) {
        this.plugin = plugin;
    }

    public void reloadMessages() {
        // Método para recarregar mensagens se necessário
    }

    /**
     * Coloriza uma string com códigos de cor do Minecraft
     */
    public String colorize(String message) {
        if (message == null)
            return "";

        // Suporte para cores hex (1.16+)
        if (isHexSupported()) {
            message = translateHexColorCodes(message);
        }

        // Traduzir códigos de cor padrão
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * Envia uma mensagem colorizada para um CommandSender
     */
    public void sendMessage(CommandSender sender, String message) {
        if (message == null || message.isEmpty())
            return;
        sender.sendMessage(colorize(message));
    }

    /**
     * Envia uma mensagem com prefixo para um CommandSender
     */
    public void sendPrefixedMessage(CommandSender sender, String message) {
        String prefix = plugin.getConfigManager().getPrefix();
        sendMessage(sender, prefix + message);
    }

    /**
     * Envia uma mensagem do config para um CommandSender
     */
    public void sendConfigMessage(CommandSender sender, String key) {
        String message = plugin.getConfigManager().getMessage(key);
        sendPrefixedMessage(sender, message);
    }

    /**
     * Envia uma mensagem do config com placeholders
     */
    public void sendConfigMessage(CommandSender sender, String key, String... placeholders) {
        String message = plugin.getConfigManager().getMessage(key);

        // Substituir placeholders
        for (int i = 0; i < placeholders.length; i += 2) {
            if (i + 1 < placeholders.length) {
                message = message.replace(placeholders[i], placeholders[i + 1]);
            }
        }

        sendPrefixedMessage(sender, message);
    }

    /**
     * Substitui placeholders em uma mensagem
     */
    public String replacePlaceholders(String message, Player player) {
        if (message == null || player == null)
            return message;

        // Placeholders básicos
        message = message.replace("{player}", player.getName());
        message = message.replace("{displayname}", player.getDisplayName());

        // Integração com PlaceholderAPI se disponível
        if (plugin.getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            message = me.clip.placeholderapi.PlaceholderAPI.setPlaceholders(player, message);
        }

        return message;
    }

    /**
     * Substitui placeholders relacionados a tags
     */
    public String replaceTagPlaceholders(String message, String tagId, String tagName, String playerName) {
        if (message == null)
            return "";

        message = message.replace("{tag}", tagName != null ? tagName : "");
        message = message.replace("{tag_id}", tagId != null ? tagId : "");
        message = message.replace("{player}", playerName != null ? playerName : "");

        return message;
    }

    /**
     * Verifica se o servidor suporta cores hex (1.16+)
     */
    private boolean isHexSupported() {
        try {
            // Tentar acessar método que só existe na 1.16+
            ChatColor.class.getMethod("of", String.class);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }

    /**
     * Traduz códigos de cor hex para o formato do Minecraft
     */
    private String translateHexColorCodes(String message) {
        Matcher matcher = HEX_PATTERN.matcher(message);
        StringBuffer buffer = new StringBuffer(message.length() + 4 * 8);

        while (matcher.find()) {
            String group = matcher.group(1);
            matcher.appendReplacement(buffer, ChatColor.COLOR_CHAR + "x"
                    + ChatColor.COLOR_CHAR + group.charAt(0) + ChatColor.COLOR_CHAR + group.charAt(1)
                    + ChatColor.COLOR_CHAR + group.charAt(2) + ChatColor.COLOR_CHAR + group.charAt(3)
                    + ChatColor.COLOR_CHAR + group.charAt(4) + ChatColor.COLOR_CHAR + group.charAt(5));
        }

        return matcher.appendTail(buffer).toString();
    }

    /**
     * Remove todas as cores de uma string
     */
    public String stripColor(String message) {
        if (message == null)
            return "";
        return ChatColor.stripColor(colorize(message));
    }

    /**
     * Formata uma lista de strings
     */
    public String[] colorizeArray(String[] messages) {
        if (messages == null)
            return new String[0];

        String[] colorized = new String[messages.length];
        for (int i = 0; i < messages.length; i++) {
            colorized[i] = colorize(messages[i]);
        }

        return colorized;
    }
}
