package com.stoneplugins.stonecactos.listeners;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

public class InventoryListener implements Listener {

    private final StoneCactos plugin;

    public InventoryListener(StoneCactos plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent e) {
        if (!(e.getWhoClicked() instanceof Player))
            return;
        if (e.getClickedInventory() == null)
            return;

        Player player = (Player) e.getWhoClicked();
        String title = e.getView().getTitle();

        // Verificar se é um menu do plugin e cancelar IMEDIATAMENTE
        if (title.equals("§2§lCACTOS") || title.equals("§6§lDEPOSITO") ||
                title.equals("§2§lARMAZEM") || title.equals("§a§lBOOSTERS") ||
                title.equals("§a§lAMIGOS") || title.equals("§e§lUPGRADE") ||
                title.equals("§a§lPERMISSOES DO AMIGO")) {
            e.setCancelled(true);
        } else {
            return; // Não é menu do plugin
        }

        ItemStack item = e.getCurrentItem();
        if (item == null || !item.hasItemMeta())
            return;

        String itemName = item.getItemMeta().getDisplayName();

        // DEBUG TEMPORÁRIO
        player.sendMessage("§e[DEBUG] Item clicado: '" + itemName + "'");
        player.sendMessage("§e[DEBUG] Contém VOLTAR? " + itemName.contains("VOLTAR"));

        // Sistema simples por detecção de nome - MUITO MAIS PRÁTICO!
        if (itemName.contains("VOLTAR")) {
            player.sendMessage("§a[DEBUG] VOLTAR detectado! Chamando handleBackToMain...");
            // Qualquer botão com "VOLTAR" volta ao menu principal
            handleBackToMain(player);
            player.sendMessage("§a[DEBUG] handleBackToMain executado!");
        } else if (itemName.contains("DEPOSITAR TORRES")) {
            if (e.getClick() == org.bukkit.event.inventory.ClickType.LEFT) {
                handleDepositClick(player);
            } else if (e.getClick() == org.bukkit.event.inventory.ClickType.RIGHT) {
                handleUpgradeClick(player);
            }
        } else if (itemName.contains("ARMAZEM")) {
            handleWarehouseClick(player);
        } else if (itemName.contains("AMIGOS")) {
            if (e.getClick() == org.bukkit.event.inventory.ClickType.LEFT) {
                handleFriendsClick(player);
            } else if (e.getClick() == org.bukkit.event.inventory.ClickType.RIGHT) {
                handleAddFriendClick(player);
            }
        } else if (itemName.contains("BOOSTERS")) {
            handleBoostersClick(player);
        } else if (itemName.contains("DEPOSITAR TORRE")) {
            handleDepositAction(player, title);
        } else if (itemName.contains("ECONOMIA DE ENERGIA")) {
            handleEconomyToggle(player);
        } else if (itemName.contains("PLANTACAO")) {
            if (e.getClick() == org.bukkit.event.inventory.ClickType.SHIFT_RIGHT) {
                handleRemoveFarm(player);
            }
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();
        String inventoryTitle = event.getInventory().getTitle();

        // Processar fechamento de menu se necessário
        if (plugin.getMenuManager().isPluginMenu(inventoryTitle)) {
            plugin.getMenuManager().handleMenuClose(player);
        }
    }

    private void handleBackToMain(Player player) {
        player.sendMessage("§e[DEBUG] handleBackToMain iniciado");

        // Encontrar o gerador do jogador
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§c[DEBUG] Nenhum gerador encontrado!");
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        player.sendMessage("§a[DEBUG] Gerador encontrado!");

        // Pegar o primeiro gerador do jogador
        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        player.sendMessage("§a[DEBUG] Abrindo menu principal...");

        // Abrir menu principal diretamente
        plugin.getMenuManager().openMainMenu(player, generator);

        player.sendMessage("§a[DEBUG] Menu principal aberto!");
    }

    private void handleDepositClick(Player player) {
        // Encontrar gerador do jogador
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        plugin.getMenuManager().openDepositMenu(player, generator);
    }

    private void handleWarehouseClick(Player player) {
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        plugin.getMenuManager().openWarehouseMenu(player, generator);
    }

    private void handleFriendsClick(Player player) {
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        plugin.getMenuManager().openFriendsMenu(player, generator);
    }

    private void handleBoostersClick(Player player) {
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        plugin.getMenuManager().openBoostersMenu(player, generator);
    }

    private void handleDepositAction(Player player, String title) {
        // Lógica para depositar torres (implementar conforme necessário)
        player.sendMessage("§aFuncionalidade de depósito em desenvolvimento!");
    }

    private void handleUpgradeClick(Player player) {
        // Encontrar gerador do jogador
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            player.closeInventory();
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        plugin.getMenuManager().openUpgradeMenu(player, generator);
    }

    private void handleAddFriendClick(Player player) {
        player.sendMessage("§eFuncionalidade de adicionar amigo em desenvolvimento!");
        player.sendMessage("§7Use o chat para digitar o nome do jogador.");
    }

    private void handleEconomyToggle(Player player) {
        // Encontrar gerador do jogador
        if (plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId()).isEmpty()) {
            player.sendMessage("§cVocê não possui geradores!");
            return;
        }

        java.util.Set<com.stoneplugins.stonecactos.data.CactusGenerator> generators = plugin.getGeneratorManager()
                .getPlayerGenerators(player.getUniqueId());
        com.stoneplugins.stonecactos.data.CactusGenerator generator = generators.iterator().next();

        // Alternar economia de energia
        boolean currentState = generator.isEconomyMode();
        generator.setEconomyMode(!currentState);

        if (generator.isEconomyMode()) {
            player.sendMessage("§a[StoneCactos] §fEconomia de energia §aATIVADA§f!");
            player.sendMessage("§7Produção e consumo reduzidos pela metade.");
        } else {
            player.sendMessage("§c[StoneCactos] §fEconomia de energia §cDESATIVADA§f!");
            player.sendMessage("§7Produção e consumo normais.");
        }

        // Reabrir menu para atualizar o status
        plugin.getMenuManager().openMainMenu(player, generator);
    }

    private void handleRemoveFarm(Player player) {
        player.sendMessage("§cFuncionalidade de remover farm em desenvolvimento!");
        player.sendMessage("§7Shift+Clique Direito para confirmar remoção.");
    }
}
