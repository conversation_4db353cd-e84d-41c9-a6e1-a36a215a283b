package com.stoneplugins.spawnershop;

import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

public class SpawnerShopSimple extends JavaPlugin implements Listener {
    
    private Map<String, SpawnerData> spawners = new HashMap<>();
    
    @Override
    public void onEnable() {
        // Registrar eventos
        getServer().getPluginManager().registerEvents(this, this);
        
        // Carregar spawners padrão
        loadDefaultSpawners();
        
        // <PERSON>var config padrão
        saveDefaultConfig();
        
        getLogger().info("SpawnerShop habilitado com sucesso!");
        getLogger().info("Use /spawners para abrir a loja!");
    }
    
    @Override
    public void onDisable() {
        getLogger().info("SpawnerShop desabilitado!");
    }
    
    private void loadDefaultSpawners() {
        spawners.put("cow", new SpawnerData("&eVaca", 30000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Cow}}"));
        spawners.put("pig", new SpawnerData("&ePorco", 25000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Pig}}"));
        spawners.put("chicken", new SpawnerData("&eGalinha", 20000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Chicken}}"));
        spawners.put("sheep", new SpawnerData("&eOvelha", 28000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Sheep}}"));
        spawners.put("zombie", new SpawnerData("&eZombie", 60000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Zombie}}"));
        spawners.put("skeleton", new SpawnerData("&eEsqueleto", 65000, Material.MOB_SPAWNER, "give {player} mob_spawner 1 0 {EntityTag:{id:Skeleton}}"));
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Este comando só pode ser usado por jogadores!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (command.getName().equalsIgnoreCase("spawners")) {
            if (args.length == 0) {
                openSpawnerShop(player);
                return true;
            }
            
            if (args[0].equalsIgnoreCase("reload") && player.hasPermission("spawnershop.admin")) {
                reloadConfig();
                loadDefaultSpawners();
                player.sendMessage(ChatColor.GREEN + "Plugin recarregado com sucesso!");
                return true;
            }
            
            if (args[0].equalsIgnoreCase("help")) {
                player.sendMessage(ChatColor.YELLOW + "=== SpawnerShop Comandos ===");
                player.sendMessage(ChatColor.WHITE + "/spawners" + ChatColor.GRAY + " - Abrir loja de spawners");
                player.sendMessage(ChatColor.WHITE + "/spawnercomprar <spawner> <quantidade>" + ChatColor.GRAY + " - Comprar spawner via chat");
                if (player.hasPermission("spawnershop.admin")) {
                    player.sendMessage(ChatColor.RED + "=== Comandos Admin ===");
                    player.sendMessage(ChatColor.WHITE + "/spawners reload" + ChatColor.GRAY + " - Recarregar plugin");
                }
                return true;
            }
        }
        
        if (command.getName().equalsIgnoreCase("spawnercomprar")) {
            if (args.length < 2) {
                player.sendMessage(ChatColor.RED + "Uso: /spawnercomprar <spawner> <quantidade>");
                player.sendMessage(ChatColor.RED + "Spawners disponíveis: cow, pig, chicken, sheep, zombie, skeleton");
                return true;
            }
            
            String spawnerType = args[0].toLowerCase();
            int quantity;
            
            try {
                quantity = Integer.parseInt(args[1]);
                if (quantity <= 0) {
                    player.sendMessage(ChatColor.RED + "A quantidade deve ser maior que 0!");
                    return true;
                }
            } catch (NumberFormatException e) {
                player.sendMessage(ChatColor.RED + "Quantidade inválida!");
                return true;
            }
            
            if (!spawners.containsKey(spawnerType)) {
                player.sendMessage(ChatColor.RED + "Spawner não encontrado!");
                player.sendMessage(ChatColor.RED + "Spawners disponíveis: cow, pig, chicken, sheep, zombie, skeleton");
                return true;
            }
            
            SpawnerData spawner = spawners.get(spawnerType);
            double totalPrice = spawner.price * quantity;
            
            // Verificar se tem Vault para economia
            if (getServer().getPluginManager().getPlugin("Vault") != null) {
                // Aqui você pode integrar com Vault se necessário
                player.sendMessage(ChatColor.GREEN + "Compra simulada realizada!");
                player.sendMessage(ChatColor.GREEN + "Spawner: " + ChatColor.stripColor(ChatColor.translateAlternateColorCodes('&', spawner.name)));
                player.sendMessage(ChatColor.GREEN + "Quantidade: " + quantity);
                player.sendMessage(ChatColor.GREEN + "Preço total: $" + totalPrice);
                
                // Executar comando
                String cmd = spawner.command.replace("{player}", player.getName()).replace("{quantidade}", String.valueOf(quantity));
                Bukkit.dispatchCommand(Bukkit.getConsoleSender(), cmd);
            } else {
                player.sendMessage(ChatColor.RED + "Sistema de economia não encontrado!");
            }
            
            return true;
        }
        
        return false;
    }
    
    private void openSpawnerShop(Player player) {
        Inventory inv = Bukkit.createInventory(null, 54, ChatColor.translateAlternateColorCodes('&', "&8Loja de Spawners"));
        
        int slot = 10;
        for (Map.Entry<String, SpawnerData> entry : spawners.entrySet()) {
            SpawnerData spawner = entry.getValue();
            ItemStack item = new ItemStack(spawner.material);
            ItemMeta meta = item.getItemMeta();
            
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', spawner.name));
            meta.setLore(Arrays.asList(
                "",
                ChatColor.GRAY + "Preço: " + ChatColor.GREEN + "$" + spawner.price,
                ChatColor.GRAY + "Tipo: " + ChatColor.YELLOW + entry.getKey(),
                "",
                ChatColor.GREEN + "Clique para comprar!"
            ));
            
            item.setItemMeta(meta);
            inv.setItem(slot, item);
            
            slot++;
            if (slot == 17) slot = 19;
            if (slot == 26) slot = 28;
            if (slot == 35) break;
        }
        
        // Adicionar item de informações
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.YELLOW + "Informações");
        infoMeta.setLore(Arrays.asList(
            "",
            ChatColor.GRAY + "Use " + ChatColor.WHITE + "/spawnercomprar <tipo> <quantidade>",
            ChatColor.GRAY + "para comprar spawners via chat!",
            "",
            ChatColor.GRAY + "Exemplo: " + ChatColor.WHITE + "/spawnercomprar cow 5"
        ));
        info.setItemMeta(infoMeta);
        inv.setItem(4, info);
        
        player.openInventory(inv);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        
        if (event.getInventory().getTitle().equals(ChatColor.translateAlternateColorCodes('&', "&8Loja de Spawners"))) {
            event.setCancelled(true);
            
            ItemStack item = event.getCurrentItem();
            if (item == null || item.getType() == Material.AIR) return;
            
            if (item.getType() == Material.BOOK) {
                player.sendMessage(ChatColor.YELLOW + "Use /spawnercomprar <tipo> <quantidade> para comprar spawners!");
                return;
            }
            
            if (item.getType() == Material.MOB_SPAWNER) {
                player.closeInventory();
                player.sendMessage(ChatColor.YELLOW + "Digite no chat: /spawnercomprar <tipo> <quantidade>");
                player.sendMessage(ChatColor.YELLOW + "Exemplo: /spawnercomprar cow 1");
                player.sendMessage(ChatColor.GRAY + "Tipos disponíveis: cow, pig, chicken, sheep, zombie, skeleton");
            }
        }
    }
    
    private static class SpawnerData {
        String name;
        double price;
        Material material;
        String command;
        
        SpawnerData(String name, double price, Material material, String command) {
            this.name = name;
            this.price = price;
            this.material = material;
            this.command = command;
        }
    }
}
