plugins {
    id 'java'
    id 'com.github.johnrengelman.shadow' version '7.1.2'
}

group = 'com.stoneplugins'
version = '1.0-SNAPSHOT'
sourceCompatibility = '1.8'
targetCompatibility = '1.8'

repositories {
    mavenCentral()
    
    // Spigot Repository
    maven {
        name = 'spigotmc-repo'
        url = 'https://hub.spigotmc.org/nexus/content/repositories/snapshots/'
    }
    
    // Vault Repository
    maven {
        name = 'jitpack'
        url = 'https://jitpack.io'
    }
    
    // PlaceholderAPI Repository
    maven {
        name = 'placeholderapi'
        url = 'https://repo.extendedclip.com/content/repositories/placeholderapi/'
    }
}

dependencies {
    // Spigot API
    compileOnly 'org.spigotmc:spigot-api:1.8.8-R0.1-SNAPSHOT'
    
    // Vault
    compileOnly 'com.github.MilkBowl:VaultAPI:1.7'
    
    // PlaceholderAPI
    compileOnly 'me.clip:placeholderapi:2.11.2'
    
    // SQLite
    implementation 'org.xerial:sqlite-jdbc:********'
    
    // MySQL
    implementation 'mysql:mysql-connector-java:8.0.33'
}

shadowJar {
    archiveClassifier.set('')
    
    relocate 'org.sqlite', 'com.stoneplugins.spawnershop.libs.sqlite'
    relocate 'com.mysql', 'com.stoneplugins.spawnershop.libs.mysql'
    
    minimize()
}

build {
    dependsOn shadowJar
}

jar {
    enabled = false
}

tasks.withType(JavaCompile) {
    options.encoding = 'UTF-8'
}

processResources {
    from(sourceSets.main.resources.srcDirs) {
        filter org.apache.tools.ant.filters.ReplaceTokens, tokens: [version: version]
    }
}
