package com.stoneplugins.stonetags.listeners;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import com.stoneplugins.stonetags.data.Suffix;
import com.stoneplugins.stonetags.gui.TagsMenu;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

import java.util.List;

public class MenuListener implements Listener {

    private final StoneTags plugin;

    public MenuListener(StoneTags plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player))
            return;
        Player player = (Player) event.getWhoClicked();

        String title = event.getView().getTitle();

        // Verificar se é um menu do StoneTags
        if (!title.contains("TAGS") && !title.contains("SUFIXOS"))
            return;

        event.setCancelled(true);

        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null)
            return;

        int slot = event.getSlot();

        // Determinar se está vendo sufixos ou tags baseado no título
        // O título processado fica: "✦ SUFIXOS ✦ Página X/Y" ou "✦ TAGS ✦ Página X/Y"
        boolean showingSuffixes = title.contains("SUFIXOS") && !title.contains("TAGS");

        // Extrair página do título
        int currentPage = 1;
        try {
            String[] parts = title.split("Página ");
            if (parts.length > 1) {
                String pageInfo = parts[1].split("/")[0];
                currentPage = Integer.parseInt(pageInfo);
            }
        } catch (Exception e) {
            // Usar página 1 como padrão
        }

        // Botões de navegação entre abas
        if (slot == 0) {
            // Botão TAGS
            if (showingSuffixes) {
                // Trocar para tags REABRINDO menu
                player.closeInventory();
                new TagsMenu(plugin, player, 1, false).open();
            }
            return;
        }

        if (slot == 8) {
            // Botão SUFIXOS
            if (!showingSuffixes) {
                // Trocar para sufixos REABRINDO menu
                player.closeInventory();
                new TagsMenu(plugin, player, 1, true).open();
            }
            return;
        }

        // Botões de navegação de páginas
        if (slot == 45) {
            // Página anterior
            if (currentPage > 1) {
                player.closeInventory();
                new TagsMenu(plugin, player, currentPage - 1, showingSuffixes).open();
            }
            return;
        }

        if (slot == 53) {
            // Próxima página
            player.closeInventory();
            new TagsMenu(plugin, player, currentPage + 1, showingSuffixes).open();
            return;
        }

        // Botão remover
        if (slot == 49) {
            if (showingSuffixes) {
                handleRemoveSuffix(player);
            } else {
                handleRemoveTag(player);
            }
            return;
        }

        // Botão fechar
        if (slot == 50) {
            player.closeInventory();
            return;
        }

        // Clique em item (tag ou suffix)
        handleItemClick(player, slot, showingSuffixes, currentPage);
    }

    private void handleItemClick(Player player, int slot, boolean showingSuffixes, int currentPage) {

        String[] slots = plugin.getConfigManager().getTagSlots().split(",");

        // Verificar se o slot clicado é válido
        boolean isValidSlot = false;
        int itemIndex = -1;

        for (int i = 0; i < slots.length; i++) {
            if (Integer.parseInt(slots[i].trim()) == slot) {
                isValidSlot = true;
                itemIndex = i;
                break;
            }
        }

        if (!isValidSlot)
            return;

        if (showingSuffixes) {
            handleSuffixClick(player, itemIndex, currentPage);
        } else {
            handleTagClick(player, itemIndex, currentPage);
        }
    }

    private void handleTagClick(Player player, int itemIndex, int currentPage) {
        List<Tag> availableTags = plugin.getTagManager().getAvailableTags(player);
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");

        int startIndex = (currentPage - 1) * slots.length;
        int tagIndex = startIndex + itemIndex;

        if (tagIndex >= availableTags.size())
            return;

        Tag tag = availableTags.get(tagIndex);

        // Verificar permissão
        if (!plugin.getTagManager().canUseTag(player, tag)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não tem permissão para usar esta tag!"));
            return;
        }

        // Verificar se já está usando
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
        if (currentTag != null && currentTag.getId().equals(tag.getId())) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê já está usando esta tag!"));
            return;
        }

        // Definir tag
        plugin.getPlayerDataManager().setSelectedTag(player, tag.getId());

        String tagDisplay = plugin.getMessageUtils().colorize(tag.getCurrentPrefix(player));
        player.sendMessage(plugin.getMessageUtils().colorize("&aTag alterada para: " + tagDisplay));

        player.closeInventory();
    }

    private void handleSuffixClick(Player player, int itemIndex, int currentPage) {
        List<Suffix> availableSuffixes = plugin.getSuffixManager().getAvailableSuffixes(player);
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");

        int startIndex = (currentPage - 1) * slots.length;
        int suffixIndex = startIndex + itemIndex;

        if (suffixIndex >= availableSuffixes.size())
            return;

        Suffix suffix = availableSuffixes.get(suffixIndex);

        // Verificar permissão
        if (!plugin.getSuffixManager().canUseSuffix(player, suffix)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não tem permissão para usar este suffix!"));
            return;
        }

        // Verificar se já está usando
        Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
        if (currentSuffix != null && currentSuffix.getId().equals(suffix.getId())) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê já está usando este suffix!"));
            return;
        }

        // Definir suffix
        plugin.getPlayerDataManager().setSelectedSuffix(player, suffix.getId());

        String suffixDisplay = plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
        player.sendMessage(plugin.getMessageUtils().colorize("&aSuffix alterado para: " + suffixDisplay));

        player.closeInventory();
    }

    private void handleRemoveTag(Player player) {
        if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedTag(player);
        player.sendMessage(plugin.getMessageUtils().colorize("&aSua tag foi removida!"));
        player.closeInventory();
    }

    private void handleRemoveSuffix(Player player) {
        if (!plugin.getPlayerDataManager().hasSelectedSuffix(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhum suffix!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedSuffix(player);
        player.sendMessage(plugin.getMessageUtils().colorize("&aSeu suffix foi removido!"));
        player.closeInventory();
    }

    private void updateInventoryContent(org.bukkit.inventory.Inventory inventory, Player player, int page,
            boolean showingSuffixes) {
        // Limpar inventário
        inventory.clear();

        // Criar menu temporário para pegar os itens
        TagsMenu menu = new TagsMenu(plugin, player, page, showingSuffixes);

        // Criar inventário temporário
        org.bukkit.inventory.Inventory tempInventory = org.bukkit.Bukkit.createInventory(null, 54, "temp");
        menu.populateInventory(tempInventory);

        // Copiar todos os itens
        for (int i = 0; i < tempInventory.getSize(); i++) {
            inventory.setItem(i, tempInventory.getItem(i));
        }
    }

}
