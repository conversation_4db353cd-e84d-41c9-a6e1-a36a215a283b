package com.atlasplugins.pescaria.config;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;

public class ConfigManager {

    private final Pescaria plugin;
    private FileConfiguration config;
    private File configFile;
    private FileConfiguration missionsConfig;
    private File missionsFile;
    private FileConfiguration dataConfig;
    private File dataFile;

    public ConfigManager(Pescaria plugin) {
        this.plugin = plugin;
    }

    public void setupFiles() {
        // Configuração principal
        if (configFile == null) {
            configFile = new File(plugin.getDataFolder(), "config.yml");
        }
        config = YamlConfiguration.loadConfiguration(configFile);
        plugin.saveDefaultConfig();

        // Configuração de missões
        if (missionsFile == null) {
            missionsFile = new File(plugin.getDataFolder(), "missoes.yml");
        }
        if (!missionsFile.exists()) {
            plugin.saveResource("missoes.yml", false);
        }
        missionsConfig = YamlConfiguration.loadConfiguration(missionsFile);

        // Configuração de dados dos jogadores
        if (dataFile == null) {
            dataFile = new File(plugin.getDataFolder(), "data.yml");
        }
        if (!dataFile.exists()) {
            try {
                dataFile.createNewFile();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }

    public FileConfiguration getConfig() {
        return config;
    }

    public FileConfiguration getMissionsConfig() {
        return missionsConfig;
    }

    public FileConfiguration getDataConfig() {
        return dataConfig;
    }

    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Não foi possível salvar config.yml: " + e.getMessage());
        }
    }

    public void saveMissionsConfig() {
        try {
            missionsConfig.save(missionsFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Não foi possível salvar missoes.yml: " + e.getMessage());
        }
    }

    public void saveDataConfig() {
        try {
            dataConfig.save(dataFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Não foi possível salvar data.yml: " + e.getMessage());
        }
    }

    public void reloadConfigs() {
        config = YamlConfiguration.loadConfiguration(configFile);
        missionsConfig = YamlConfiguration.loadConfiguration(missionsFile);
        dataConfig = YamlConfiguration.loadConfiguration(dataFile);
    }

    public String getPescariaWorldName() {
        // Pegar o primeiro mundo da lista de mundos permitidos
        java.util.List<String> mundos = config.getStringList("mundosPermitidos");
        if (mundos != null && !mundos.isEmpty()) {
            // Procurar por "pescaria" primeiro
            for (String mundo : mundos) {
                if (mundo.equalsIgnoreCase("pescaria")) {
                    return mundo;
                }
            }
            // Se não encontrar "pescaria", retornar o primeiro da lista
            return mundos.get(0);
        }
        return "pescaria"; // fallback
    }

    // Métodos para a nova configuração
    public boolean isXpInRodEnabled() {
        return config.getBoolean("Geral.ativarXpNaVara", true);
    }

    public int getXpPerFish() {
        return config.getInt("Geral.xpPorPeixe", 100);
    }

    public int getDefaultEvolutionXp() {
        return config.getInt("Geral.xpPadraoDeEvolucao", 1000);
    }

    public int getXpPerLevel() {
        return config.getInt("Geral.xpACadaNivel", 100);
    }

    public boolean isFishShopEnabled() {
        return config.getBoolean("Geral.lojaPeixes", true);
    }

    public boolean canSellFishForCoins() {
        return config.getBoolean("Geral.venderPeixesCoins", true);
    }

    public boolean canSellFishForCash() {
        return config.getBoolean("Geral.venderPeixesCash", true);
    }

    public double getFishPriceCoins() {
        return config.getDouble("Geral.precoPeixesCoins", 100.0);
    }

    public double getFishPriceCash() {
        return config.getDouble("Geral.precoPeixesCash", 1.0);
    }

    public int getFishingRadius() {
        return config.getInt("Geral.raioPesca", 10);
    }

    public double getFishCatchChance() {
        return config.getDouble("Geral.chancePescarPeixe", 1.5);
    }

    public double getLevelUpChanceConfig() {
        return config.getDouble("Geral.chanceEvoluirNivel", 1.0);
    }

    public double getCoinsChance() {
        return config.getDouble("Geral.chanceGanharCoins", 1.0);
    }

    public double getCoinsPerFish() {
        return config.getDouble("Geral.coinsAoPescar", 100.0);
    }

    public int getFishingTime() {
        return config.getInt("Geral.tempoPesca", 5);
    }

    public int getMaxPeixesPorUpdate() {
        return config.getInt("Geral.MaxPeixesPorUpdate", 5);
    }

    public boolean isWorldAllowed(String worldName) {
        java.util.List<String> mundos = config.getStringList("mundosPermitidos");
        if (mundos != null) {
            for (String mundo : mundos) {
                if (mundo.equalsIgnoreCase(worldName)) {
                    return true;
                }
            }
        }
        return false;
    }

    public boolean isChanceSystemEnabled() {
        return config.getBoolean("Geral.SistemaChance", true);
    }

    public boolean areEnchantsEnabled() {
        return config.getBoolean("Geral.EncantamentosAtivados", true);
    }

    // Métodos para Chances
    public double getCommonFishChance() {
        return config.getDouble("Chances.PeixeComum", 70.0);
    }

    public double getRareFishChance() {
        return config.getDouble("Chances.PeixeRaro", 20.0);
    }

    public double getEpicFishChance() {
        return config.getDouble("Chances.PeixeEpico", 8.0);
    }

    public double getLegendaryFishChance() {
        return config.getDouble("Chances.PeixeLendario", 2.0);
    }

    public double getLevelUpChance() {
        return config.getDouble("Chances.ChanceLevelUp", 15.0);
    }

    // Métodos para Boosters
    public boolean areBoostersEnabled() {
        return config.getBoolean("Boosters.Ativado", true);
    }

    public double getXPMultiplier() {
        return config.getDouble("Boosters.MultiplicadorXP", 2.0);
    }

    public double getFishMultiplier() {
        return config.getDouble("Boosters.MultiplicadorPeixes", 1.5);
    }

    public int getBoosterDuration() {
        return config.getInt("Boosters.DuracaoMinutos", 30);
    }

    // Métodos para Mensagens
    public String getMessage(String key) {
        return ChatColor.translateAlternateColorCodes('&',
                config.getString("Mensagens." + key, "&cMensagem não encontrada: " + key));
    }

    // Métodos para Formatação
    public boolean useNumberFormatting() {
        return config.getBoolean("Formatacao.UsarFormatacao", true);
    }

    public String formatNumber(long number) {
        if (!useNumberFormatting()) {
            return String.valueOf(number);
        }

        if (number >= 1_000_000_000_000L) {
            return String.format("%.1f%s", number / 1_000_000_000_000.0,
                    config.getString("Formatacao.Separadores.Trilhoes", "T"));
        } else if (number >= 1_000_000_000L) {
            return String.format("%.1f%s", number / 1_000_000_000.0,
                    config.getString("Formatacao.Separadores.Bilhoes", "B"));
        } else if (number >= 1_000_000L) {
            return String.format("%.1f%s", number / 1_000_000.0,
                    config.getString("Formatacao.Separadores.Milhoes", "M"));
        } else if (number >= 1_000L) {
            return String.format("%.1f%s", number / 1_000.0,
                    config.getString("Formatacao.Separadores.Milhares", "k"));
        }
        return String.valueOf(number);
    }

    public Location getFishingSpawnLocation() {
        if (!dataConfig.contains("Locations.FishingSpawn")) {
            return null;
        }

        World world = Bukkit.getWorld(getPescariaWorldName());
        if (world == null) {
            return null;
        }

        double x = dataConfig.getDouble("Locations.FishingSpawn.X");
        double y = dataConfig.getDouble("Locations.FishingSpawn.Y");
        double z = dataConfig.getDouble("Locations.FishingSpawn.Z");
        float yaw = (float) dataConfig.getDouble("Locations.FishingSpawn.Yaw");
        float pitch = (float) dataConfig.getDouble("Locations.FishingSpawn.Pitch");

        return new Location(world, x, y, z, yaw, pitch);
    }

    public Location getServerSpawnLocation() {
        if (!dataConfig.contains("Locations.ServerSpawn")) {
            return null;
        }

        World world = Bukkit.getWorld(dataConfig.getString("Locations.ServerSpawn.World"));
        if (world == null) {
            return null;
        }

        double x = dataConfig.getDouble("Locations.ServerSpawn.X");
        double y = dataConfig.getDouble("Locations.ServerSpawn.Y");
        double z = dataConfig.getDouble("Locations.ServerSpawn.Z");
        float yaw = (float) dataConfig.getDouble("Locations.ServerSpawn.Yaw");
        float pitch = (float) dataConfig.getDouble("Locations.ServerSpawn.Pitch");

        return new Location(world, x, y, z, yaw, pitch);
    }

    public void setFishingSpawnLocation(Location location) {
        dataConfig.set("Locations.FishingSpawn.X", location.getX());
        dataConfig.set("Locations.FishingSpawn.Y", location.getY());
        dataConfig.set("Locations.FishingSpawn.Z", location.getZ());
        dataConfig.set("Locations.FishingSpawn.Yaw", location.getYaw());
        dataConfig.set("Locations.FishingSpawn.Pitch", location.getPitch());
        saveDataConfig();
    }

    public void setServerSpawnLocation(Location location) {
        dataConfig.set("Locations.ServerSpawn.World", location.getWorld().getName());
        dataConfig.set("Locations.ServerSpawn.X", location.getX());
        dataConfig.set("Locations.ServerSpawn.Y", location.getY());
        dataConfig.set("Locations.ServerSpawn.Z", location.getZ());
        dataConfig.set("Locations.ServerSpawn.Yaw", location.getYaw());
        dataConfig.set("Locations.ServerSpawn.Pitch", location.getPitch());
        saveDataConfig();
    }
}