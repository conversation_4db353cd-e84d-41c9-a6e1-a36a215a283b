package com.stoneplugins.stonecactos;

import com.stoneplugins.stonecactos.commands.GiveBoosterCommand;
import com.stoneplugins.stonecactos.commands.GiveCactoCommand;
import com.stoneplugins.stonecactos.config.ConfigManager;
// import com.stoneplugins.stonecactos.database.DatabaseManager;
import com.stoneplugins.stonecactos.data.MemoryDataManager;
import com.stoneplugins.stonecactos.economy.EconomyManager;
import com.stoneplugins.stonecactos.economy.SimpleEconomyManager;
import com.stoneplugins.stonecactos.generators.CactusGeneratorManager;
import com.stoneplugins.stonecactos.holograms.HologramManager;
import com.stoneplugins.stonecactos.integrations.PlotSquaredIntegration;
import com.stoneplugins.stonecactos.listeners.BlockListener;
import com.stoneplugins.stonecactos.listeners.InventoryListener;
import com.stoneplugins.stonecactos.listeners.PlayerListener;
import com.stoneplugins.stonecactos.managers.*;
import com.stoneplugins.stonecactos.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.plugin.java.JavaPlugin;

public class StoneCactos extends JavaPlugin {

    private static StoneCactos instance;

    // Managers
    private ConfigManager configManager;
    // private DatabaseManager databaseManager;
    private MemoryDataManager memoryDataManager;
    private EconomyManager economyManager;
    private SimpleEconomyManager simpleEconomyManager;
    private CactusGeneratorManager generatorManager;
    private HologramManager hologramManager;
    private ItemManager itemManager;
    private ProductionManager productionManager;
    private UpgradeManager upgradeManager;
    private BoosterManager boosterManager;
    private FriendsManager friendsManager;
    // private RewardManager rewardManager;
    private MenuManager menuManager;
    private MessageUtils messageUtils;

    // Integrações
    private PlotSquaredIntegration plotSquaredIntegration;

    @Override
    public void onEnable() {
        instance = this;

        getLogger().info("");
        getLogger().info("  _____ _______ ____  _   _ ______    _____          _____ _______ ____   _____ ");
        getLogger().info(" / ____|__   __/ __ \\| \\ | |  ____|  / ____|   /\\   / ____|__   __/ __ \\ / ____|");
        getLogger().info("| (___    | | | |  | |  \\| | |__    | |       /  \\ | |       | | | |  | | (___  ");
        getLogger().info(" \\___ \\   | | | |  | | . ` |  __|   | |      / /\\ \\| |       | | | |  | |\\___ \\ ");
        getLogger().info(" ____) |  | | | |__| | |\\  | |____  | |____ / ____ \\ |____   | | | |__| |____) |");
        getLogger().info("|_____/   |_|  \\____/|_| \\_|______|  \\_____/_/    \\_\\_____|  |_|  \\____/|_____/ ");
        getLogger().info("");
        getLogger().info("StoneCactos v" + getDescription().getVersion() + " - Stone Plugins");
        getLogger().info("Iniciando plugin...");

        try {
            // Inicializar configurações
            initializeConfig();

            // Inicializar sistema de dados em memória
            initializeMemoryData();

            // Inicializar economia
            initializeEconomy();

            // Inicializar managers
            initializeManagers();

            // Registrar comandos
            registerCommands();

            // Registrar listeners
            registerListeners();

            // Inicializar sistema de produção
            initializeProduction();

            getLogger().info("StoneCactos v" + getDescription().getVersion() + " habilitado com sucesso!");

        } catch (Exception e) {
            getLogger().severe("Erro ao inicializar o plugin: " + e.getMessage());
            e.printStackTrace();
            Bukkit.getPluginManager().disablePlugin(this);
        }
    }

    @Override
    public void onDisable() {
        getLogger().info("Desabilitando StoneCactos...");

        try {
            // Parar sistema de produção
            if (productionManager != null) {
                productionManager.shutdown();
            }

            // Salvar dados dos geradores
            if (generatorManager != null) {
                generatorManager.saveAllGenerators();
            }

            // Fechar conexão com banco de dados
            // if (databaseManager != null) {
            // databaseManager.closeConnection();
            // getLogger().info("Conexão com banco de dados fechada!");
            // }

        } catch (Exception e) {
            getLogger().severe("Erro ao desabilitar o plugin: " + e.getMessage());
            e.printStackTrace();
        }

        getLogger().info("StoneCactos desabilitado!");
    }

    private void initializeConfig() {
        getLogger().info("Carregando configurações...");
        configManager = new ConfigManager(this);
        configManager.loadConfigs();
        getLogger().info("Configurações carregadas com sucesso!");
    }

    private void initializeDatabase() {
        getLogger().info("Inicializando banco de dados...");
        // TODO: DatabaseManager será implementado futuramente
        // databaseManager = new DatabaseManager(this);
        // if (!databaseManager.initialize()) {
        // getLogger().warning("Falha ao inicializar banco de dados! Usando sistema em
        // memória como fallback.");
        // } else {
        // getLogger().info("Banco de dados SQLite inicializado com sucesso!");
        // }
        getLogger().info("Usando sistema de dados em memória temporariamente!");
    }

    private void initializeMemoryData() {
        getLogger().info("Inicializando sistema de dados em memória...");
        memoryDataManager = new MemoryDataManager(this);
        memoryDataManager.initialize();
        getLogger().info("Sistema de dados em memória inicializado com sucesso!");
    }

    private void initializeEconomy() {
        getLogger().info("Configurando economia...");
        economyManager = new EconomyManager(this);
        economyManager.setupEconomy();
        simpleEconomyManager = new SimpleEconomyManager(this);
        simpleEconomyManager.setupEconomy();
        getLogger().info("Sistema de economia configurado!");
    }

    private void initializeManagers() {
        getLogger().info("Inicializando managers...");

        messageUtils = new MessageUtils(this);
        itemManager = new ItemManager(this);
        hologramManager = new HologramManager(this);
        generatorManager = new CactusGeneratorManager(this);
        productionManager = new ProductionManager(this);
        upgradeManager = new UpgradeManager(this);
        boosterManager = new BoosterManager(this);
        friendsManager = new FriendsManager(this);
        // rewardManager = new RewardManager(this);
        menuManager = new MenuManager(this);

        // Inicializar integrações
        plotSquaredIntegration = new PlotSquaredIntegration(this);

        getLogger().info("Managers inicializados com sucesso!");
    }

    private void registerCommands() {
        getLogger().info("Registrando comandos...");

        getCommand("givecacto").setExecutor(new GiveCactoCommand(this));
        getCommand("givebooster").setExecutor(new GiveBoosterCommand(this));

        getLogger().info("Comandos registrados com sucesso!");
    }

    private void registerListeners() {
        getLogger().info("Registrando listeners...");

        Bukkit.getPluginManager().registerEvents(new BlockListener(this), this);
        Bukkit.getPluginManager().registerEvents(new InventoryListener(this), this);
        Bukkit.getPluginManager().registerEvents(new PlayerListener(this), this);

        getLogger().info("Listeners registrados com sucesso!");
    }

    private void initializeProduction() {
        getLogger().info("Iniciando sistema de produção...");
        productionManager.startProduction();
        getLogger().info("Sistema de produção iniciado!");
    }

    // Getters para os managers
    public static StoneCactos getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    // public DatabaseManager getDatabaseManager() {
    // return databaseManager;
    // }

    public MemoryDataManager getMemoryDataManager() {
        return memoryDataManager;
    }

    public EconomyManager getEconomyManager() {
        return economyManager;
    }

    public SimpleEconomyManager getSimpleEconomyManager() {
        return simpleEconomyManager;
    }

    public CactusGeneratorManager getGeneratorManager() {
        return generatorManager;
    }

    public HologramManager getHologramManager() {
        return hologramManager;
    }

    public ItemManager getItemManager() {
        return itemManager;
    }

    public ProductionManager getProductionManager() {
        return productionManager;
    }

    public UpgradeManager getUpgradeManager() {
        return upgradeManager;
    }

    public BoosterManager getBoosterManager() {
        return boosterManager;
    }

    public FriendsManager getFriendsManager() {
        return friendsManager;
    }

    // public RewardManager getRewardManager() {
    // return rewardManager;
    // }

    public PlotSquaredIntegration getPlotSquaredIntegration() {
        return plotSquaredIntegration;
    }

    public MenuManager getMenuManager() {
        return menuManager;
    }

    public MessageUtils getMessageUtils() {
        return messageUtils;
    }

    // public Economy getEconomy() {
    // return economy;
    // }
}
