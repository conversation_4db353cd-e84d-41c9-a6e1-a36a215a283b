package com.stoneplugins.stonetags.listeners;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

/**
 * Listener para integração com chat
 */
public class ChatListener implements Listener {

    private final StoneTags plugin;

    public ChatListener(StoneTags plugin) {
        this.plugin = plugin;
    }

    @EventHandler(priority = EventPriority.HIGHEST)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        if (event.isCancelled()) {
            return;
        }

        // Verificar se a integração com chat está habilitada
        if (!plugin.getConfigManager().enableChatIntegration()) {
            return;
        }

        // Obter tag e suffix do jogador
        Tag selectedTag = plugin.getPlayerDataManager().getSelectedTag(event.getPlayer());
        com.stoneplugins.stonetags.data.Suffix selectedSuffix = plugin.getPlayerDataManager()
                .getSelectedSuffix(event.getPlayer());

        if (selectedTag == null || selectedTag.isInvisible()) {
            return;
        }

        // Obter formato do chat da configuração
        String chatFormat = plugin.getConfigManager().getChatFormat();
        if (chatFormat == null || chatFormat.isEmpty()) {
            chatFormat = "%tag_prefix%%player%%suffix_display%: %message%";
        }

        // Construir suffix display
        String suffixDisplay = "";
        if (selectedSuffix != null && !selectedSuffix.isInvisible()) {
            suffixDisplay = " " + selectedSuffix.getDisplaySuffix();
        }

        // Substituir placeholders
        String formattedMessage = chatFormat
                .replace("%tag_prefix%", selectedTag.getChatPrefix())
                .replace("%tag_suffix%", selectedTag.getChatSuffix())
                .replace("%tag_name%", selectedTag.getName())
                .replace("%suffix_display%", suffixDisplay)
                .replace("%player%", event.getPlayer().getName())
                .replace("%message%", event.getMessage());

        // Colorizar
        formattedMessage = plugin.getMessageUtils().colorize(formattedMessage);

        // Substituir placeholders do PlaceholderAPI se disponível
        if (plugin.getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            formattedMessage = me.clip.placeholderapi.PlaceholderAPI.setPlaceholders(event.getPlayer(),
                    formattedMessage);
        }

        // Definir novo formato
        event.setFormat(formattedMessage);

        plugin.getLogger().info("Chat formatado para " + event.getPlayer().getName() + ": " + formattedMessage);
    }
}
