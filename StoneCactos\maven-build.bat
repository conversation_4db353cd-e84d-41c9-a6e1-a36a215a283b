@echo off
echo ========================================
echo    Compilando StoneCactos com Maven
echo ========================================

REM Usar o Maven que existe no codebase
set "MAVEN_CMD=..\apache-maven-3.9.5\bin\mvn.cmd"

if not exist "%MAVEN_CMD%" (
    echo ERRO: Maven nao encontrado em %MAVEN_CMD%
    pause
    exit /b 1
)

echo Usando Maven: %MAVEN_CMD%
echo.

REM Compilar com Maven
echo Executando: mvn clean package
"%MAVEN_CMD%" clean package

if %errorlevel% neq 0 (
    echo ERRO: Falha na compilacao Maven!
    pause
    exit /b 1
)

echo.
echo ========================================
echo    COMPILACAO CONCLUIDA!
echo ========================================

REM Verificar se o JAR foi gerado
if not exist "target\stonecactos-1.0.jar" (
    echo ERRO: Arquivo JAR nao foi gerado!
    pause
    exit /b 1
)

echo Arquivo gerado: target\stonecactos-1.0.jar
for %%I in (target\stonecactos-1.0.jar) do echo Tamanho: %%~zI bytes

echo.
echo Copiando para o servidor...

REM Copiar para o servidor
set "SERVER_PLUGINS=..\servidor\plugins"
set "TARGET_FILE=%SERVER_PLUGINS%\stonecactos-1.0.jar"

REM Fazer backup se existir
if exist "%TARGET_FILE%" (
    set "BACKUP_FILE=%SERVER_PLUGINS%\stonecactos-1.0-backup-%date:~-4,4%%date:~-10,2%%date:~-7,2%-%time:~0,2%%time:~3,2%%time:~6,2%.jar"
    set "BACKUP_FILE=!BACKUP_FILE: =0!"
    copy "%TARGET_FILE%" "!BACKUP_FILE!" >nul
    echo Backup criado: !BACKUP_FILE:~0,-4!
)

REM Copiar novo plugin
copy "target\stonecactos-1.0.jar" "%TARGET_FILE%" >nul

if exist "%TARGET_FILE%" (
    echo Plugin copiado para o servidor com sucesso!
    echo Localizacao: %TARGET_FILE%
    echo.
    echo ========================================
    echo    PLUGIN ATUALIZADO NO SERVIDOR!
    echo ========================================
) else (
    echo ERRO: Falha ao copiar plugin para o servidor.
    echo Plugin disponivel em: target\stonecactos-1.0.jar
)

pause
