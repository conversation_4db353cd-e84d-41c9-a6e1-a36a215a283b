package com.stoneplugins.stonecactos.listeners;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;

public class PlayerListener implements Listener {

    private final StoneCactos plugin;

    public PlayerListener(StoneCactos plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        // Inicializar dados do jogador se necessário
        // TODO: Carregar dados do jogador do banco de dados

        plugin.getLogger().info("Jogador " + player.getName() + " entrou no servidor");
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Salvar dados do jogador se necessário
        // TODO: Salvar dados do jogador no banco de dados

        plugin.getLogger().info("Jogador " + player.getName() + " saiu do servidor");
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        if (item == null || !item.hasItemMeta()) {
            return;
        }

        // Verificar se é um gerador de cactos
        boolean isGenerator = plugin.getItemManager().isCactusGenerator(item);

        if (isGenerator) {
            handleCactusGeneratorInteract(event);
            return;
        }

        // Verificar se é um booster
        for (String boosterId : plugin.getBoosterManager().getAvailableBoosters()) {
            if (plugin.getItemManager().isBooster(item, boosterId)) {
                handleBoosterInteract(event, boosterId);
                return;
            }
        }

        // Verificar se é um item de bateria
        if (plugin.getBatteryManager().isBatteryItem(item)) {
            handleBatteryInteract(event);
            return;
        }

    }

    /**
     * Processa interação com item gerador de cactos
     */
    private void handleCactusGeneratorInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // Verificar se é clique direito
        if (!event.getAction().name().contains("RIGHT_CLICK")) {
            return;
        }

        // Verificar permissão
        if (!player.hasPermission("stonecactos.use")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Verificar se clicou em um bloco
        if (event.getClickedBlock() == null) {
            player.sendMessage(
                    "§c[StoneCactos] §fVocê deve §eclicar com botão direito EM UM BLOCO§f para colocar o gerador!");
            player.sendMessage("§7Dica: Mire em terra, pedra ou grama e clique com botão direito.");
            event.setCancelled(true);
            return;
        }

        plugin.getLogger().info("Jogador " + player.getName() + " tentando colocar gerador em bloco: "
                + event.getClickedBlock().getType());

        // NÃO cancelar o evento - deixar o BlockPlaceEvent ser chamado
    }

    /**
     * Processa interação com item booster
     */
    private void handleBoosterInteract(PlayerInteractEvent event, String boosterId) {
        Player player = event.getPlayer();

        // Verificar se é clique direito
        if (!event.getAction().name().contains("RIGHT_CLICK")) {
            return;
        }

        // Verificar permissão
        if (!player.hasPermission("stonecactos.use")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Procurar gerador próximo
        CactusGenerator nearestGenerator = plugin.getGeneratorManager().getNearestGenerator(player.getLocation(), 10.0);

        if (nearestGenerator == null) {
            player.sendMessage("§cNenhum gerador de cactos encontrado próximo!");
            event.setCancelled(true);
            return;
        }

        // Verificar se o jogador pode usar o gerador
        if (!nearestGenerator.isOwner(player) && !nearestGenerator.isFriend(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Tentar ativar o booster
        if (plugin.getBoosterManager().activateBooster(player, nearestGenerator, boosterId)) {
            // Remover item do inventário
            ItemStack item = event.getItem();
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                player.getInventory().setItemInHand(new ItemStack(Material.AIR));
            }
        }

        event.setCancelled(true);
    }

    /**
     * Processa interação com item de bateria
     */
    private void handleBatteryInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();

        // Verificar se é clique direito
        if (!event.getAction().name().contains("RIGHT_CLICK")) {
            return;
        }

        // Verificar permissão
        if (!player.hasPermission("stonecactos.use")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Procurar gerador próximo
        CactusGenerator nearestGenerator = plugin.getGeneratorManager().getNearestGenerator(player.getLocation(), 10.0);

        if (nearestGenerator == null) {
            player.sendMessage("§cNenhum gerador de cactos encontrado próximo!");
            event.setCancelled(true);
            return;
        }

        // Verificar se o jogador pode usar o gerador
        if (!nearestGenerator.isOwner(player) && !nearestGenerator.isFriend(player)) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Tentar usar a bateria
        if (plugin.getBatteryManager().useBattery(player, nearestGenerator, event.getItem())) {
            // Item já foi consumido pelo BatteryManager
        }

        event.setCancelled(true);
    }

    /**
     * Obtém o nome de um jogador
     */
    private String getPlayerName(java.util.UUID uuid) {
        Player player = plugin.getServer().getPlayer(uuid);
        if (player != null) {
            return player.getName();
        }
        return plugin.getServer().getOfflinePlayer(uuid).getName();
    }
}
