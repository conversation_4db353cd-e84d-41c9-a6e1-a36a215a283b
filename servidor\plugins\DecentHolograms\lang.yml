line:
  insert_failed: '{prefix}&cFailed to insert line.'
  align_self: '{prefix}Cannot align a Line to itself!'
  flag_removed: '{prefix}Flag &b"%1$s"&7 has been removed!'
  permission_removed: '{prefix}Permission has been removed!'
  swapped: '{prefix}Lines has been swapped!'
  added: '{prefix}Line has been added!'
  offsetx_set: '{prefix}Line OffsetX has been set!'
  swap_self: '{prefix}&cCannot swap a line with itself!'
  add_failed: '{prefix}&cFailed to add line.'
  flag_added: '{prefix}Flag &b"%1$s"&7 has been added!'
  edit: '{prefix}&a&l&nClick to edit the line!'
  height_set: '{prefix}Line height has been set!'
  swap_failed: '{prefix}&cFailed to swap lines.'
  removed: '{prefix}Line has been removed!'
  offsetz_set: '{prefix}Line OffsetZ has been set!'
  facing_set: '{prefix}Facing has been set!'
  edit_hover: '&r%1$s'
  aligned: '{prefix}Line has been aligned!'
  inserted: '{prefix}Line has been inserted!'
  set: '{prefix}Line has been set!'
  align_axis: '{prefix}That axis does not exist!'
  does_not_exist: '{prefix}&cThat line doesn''t exist.'
  permission_set: '{prefix}Permission has been set!'
hologram:
  display_range_set: '{prefix}Display range has been set!'
  cloned: '{prefix}Hologram has been cloned!'
  down_origin_set: '{prefix}Origin has been set to &b''%1$s''&7!'
  update_interval_set: '{prefix}Update interval has been set!'
  down_origin_does_not_exist: '{prefix}Down origin value must be either true or false!'
  renamed: '{prefix}Hologram has been renamed! &7(&b%1$s&7 -> &b%2$s&7)'
  already_disabled: '{prefix}Hologram is already disabled!'
  align_self: '{prefix}Cannot align a Hologram to itself!'
  already_exists: '{prefix}&cHologram with that name already exists.'
  moved: '{prefix}Hologram has been moved!'
  teleported: '{prefix}Teleported!'
  facing_set: '{prefix}Facing has been set!'
  disabled: '{prefix}Hologram has been disabled!'
  enabled: '{prefix}Hologram has been enabled!'
  flag_remove: '{prefix}Flag &b"%1$s"&7 has been removed!'
  update_range_set: '{prefix}Update range has been set!'
  permission_removed: '{prefix}Permission has been removed!'
  aligned: '{prefix}Hologram has been aligned!'
  flag_add: '{prefix}Flag &b"%1$s"&7 has been added!'
  created: '{prefix}Hologram has been created!'
  align_axis: '{prefix}That axis does not exist!'
  deleted: '{prefix}Hologram has been deleted!'
  updated: '{prefix}Hologram has been updated!'
  permission_set: '{prefix}Permission has been set!'
  does_not_exist: '{prefix}&cHologram with that name doesn''t exist.'
  invalid_name: '{prefix}&cInvalid name ''%1$s'', only use alphanumerical characters,
    underscore and dash.'
  already_enabled: '{prefix}Hologram is already enabled!'
page:
  does_not_exist: '{prefix}&cThat page doesn''t exist.'
  swapped: '{prefix}Pages swapped!'
  swap_self: '{prefix}&cCannot swap a page with itself!'
  added: '{prefix}Page has been added!'
  insert_failed: '{prefix}Page has been inserted!'
  swap_failed: '{prefix}&cFailed to swap pages.'
  inserted: '{prefix}Page has been inserted!'
  add_failed: '{prefix}Page has been added!'
  deleted: '{prefix}Page has been deleted!'
action:
  cleared: '{prefix}Actions have been cleared.'
  added: '{prefix}Action has been added.'
  no_actions: '{prefix}There are no actions set on that click type.'
  removed: '{prefix}Action has been removed.'
  does_not_exist: '{prefix}&cThat action doesn''t exist.'
  click_type_does_not_exist: '{prefix}&cThat click type doesn''t exist.'
only_player: '{prefix}&cThis action can only be executed by player.'
command:
  use_help: '{prefix}Use &b/holograms help&7 to view possible commands.'
  unknown_sub_command: '{prefix}Unknown sub command.'
  usage: '{prefix}Usage: &b%1$s'
feature:
  enabled: '{prefix}Feature &b"%1$s"&7 has been enabled!'
  already_enabled: '{prefix}&cFeature "%1$s" is already enabled!'
  disabled: '{prefix}Feature &b"%1$s"&7 has been disabled!'
  reloaded: '{prefix}Feature &b"%1$s"&7 has been reloaded!'
  already_disabled: '{prefix}&cFeature "%1$s" is already disabled!'
  does_not_exist: '{prefix}&cFeature "%1$s" does not exist.'
reloaded: '{prefix}&aSuccessfully reloaded in %1$d ms!'
new_version_available: '&fA newer version of &3DecentHolograms &fis available. Download
  it from:'
prefix: '&8[&3DecentHolograms&8] &7'
no_perm: '{prefix}&cYou are not allowed to use this.'
