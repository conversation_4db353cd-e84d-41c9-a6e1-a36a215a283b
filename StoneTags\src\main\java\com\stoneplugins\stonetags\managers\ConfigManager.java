package com.stoneplugins.stonetags.managers;

import com.stoneplugins.stonetags.StoneTags;
import org.bukkit.configuration.file.FileConfiguration;

public class ConfigManager {

    private final StoneTags plugin;
    private FileConfiguration config;

    public ConfigManager(StoneTags plugin) {
        this.plugin = plugin;
        loadConfig();
    }

    public void loadConfig() {
        plugin.saveDefaultConfig();
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }

    public void reloadConfig() {
        plugin.reloadConfig();
        this.config = plugin.getConfig();
    }

    // Database
    public String getDatabaseType() {
        return config.getString("Database.type", "SQLITE");
    }

    public String getMySQLHost() {
        return config.getString("Database.mysql.host", "localhost");
    }

    public int getMySQLPort() {
        return config.getInt("Database.mysql.port", 3306);
    }

    public String getMySQLDatabase() {
        return config.getString("Database.mysql.database", "stonetags");
    }

    public String getMySQLUsername() {
        return config.getString("Database.mysql.username", "root");
    }

    public String getMySQLPassword() {
        return config.getString("Database.mysql.password", "");
    }

    public String getSQLiteFile() {
        return config.getString("Database.sqlite.file", "stonetags.db");
    }

    // General
    public boolean useMenu() {
        return config.getBoolean("General.useMenu", true);
    }

    public int getAboveNameDistance() {
        return config.getInt("General.aboveNameDistance", 10);
    }

    public boolean autoUpdateOnPermissionChange() {
        return config.getBoolean("General.autoUpdateOnPermissionChange", true);
    }

    public boolean enableAnimations() {
        return config.getBoolean("General.enableAnimations", true);
    }

    /**
     * Verifica se a integração com chat está habilitada
     */
    public boolean enableChatIntegration() {
        return config.getBoolean("Chat.enabled", true);
    }

    /**
     * Obtém o formato do chat
     */
    public String getChatFormat() {
        return config.getString("Chat.format", "%tag_prefix%%player%%suffix_display%&f: %message%");
    }

    public int getAnimationInterval() {
        return config.getInt("General.animationInterval", 20);
    }

    // Menu
    public String getMenuTitle() {
        return config.getString("Menu.title", "&8Tags Disponíveis");
    }

    public int getMenuSize() {
        return config.getInt("Menu.size", 54);
    }

    public String getDecorationMaterial() {
        return config.getString("Menu.decoration.material", "STAINED_GLASS_PANE:7");
    }

    public String getDecorationName() {
        return config.getString("Menu.decoration.name", " ");
    }

    public String getTagSlots() {
        return config.getString("Menu.tagSlots", "10,11,12,13,14,15,16,19,20,21,22,23,24,25,28,29,30,31,32,33,34");
    }

    public int getPreviousPageSlot() {
        return config.getInt("Menu.previousPage.slot", 45);
    }

    public String getPreviousPageMaterial() {
        return config.getString("Menu.previousPage.material", "ARROW");
    }

    public String getPreviousPageName() {
        return config.getString("Menu.previousPage.name", "&aPágina Anterior");
    }

    public int getNextPageSlot() {
        return config.getInt("Menu.nextPage.slot", 53);
    }

    public String getNextPageMaterial() {
        return config.getString("Menu.nextPage.material", "ARROW");
    }

    public String getNextPageName() {
        return config.getString("Menu.nextPage.name", "&aPróxima Página");
    }

    public int getRemoveTagSlot() {
        return config.getInt("Menu.removeTag.slot", 49);
    }

    public String getRemoveTagMaterial() {
        return config.getString("Menu.removeTag.material", "BARRIER");
    }

    public String getRemoveTagName() {
        return config.getString("Menu.removeTag.name", "&cRemover Tag");
    }

    // Integrations
    public boolean isTabEnabled() {
        return config.getBoolean("Integrations.tab.enabled", true);
    }

    public boolean updateTabOnTagChange() {
        return config.getBoolean("Integrations.tab.updateTabOnTagChange", true);
    }

    public boolean isPermissionsEnabled() {
        return config.getBoolean("Integrations.permissions.enabled", true);
    }

    public boolean updateTagOnRankChange() {
        return config.getBoolean("Integrations.permissions.updateTagOnRankChange", true);
    }

    public boolean isPlaceholderAPIEnabled() {
        return config.getBoolean("Integrations.placeholderapi.enabled", true);
    }

    // Messages
    public String getPrefix() {
        return config.getString("Messages.prefix", "&8[&6Stone&eTags&8] ");
    }

    public String getMessage(String key) {
        return config.getString("Messages." + key, "&cMensagem não encontrada: " + key);
    }

    public FileConfiguration getConfig() {
        return config;
    }
}
