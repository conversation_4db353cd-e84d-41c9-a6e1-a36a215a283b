# ========================================
#     TAB CONFIG PARA STONE TAGS
# ========================================
# Copie este conteúdo para o config.yml do TAB
# Substitua o conteúdo existente ou adapte conforme necessário

# Configuração principal do TAB
tablist-name-formatting:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%%stonetags_current_tag_suffix%"

# Header e Footer do TAB
header-footer:
  enabled: true
  header:
    - "&6&lSERVIDOR MINECRAFT"
    - "&7Sistema de Tags by StoneTags"
    - ""
  footer:
    - ""
    - "&7Sua tag: &f%stonetags_current_tag_name%"
    - "&7Posição: &f%stonetags_current_tag_position%"

# Configuração de grupos baseada nas tags do StoneTags
groups:
  staff:
    tabprefix: "&c[STAFF] "
    tabsuffix: ""
    sortpriority: 1000
  mvp:
    tabprefix: "&b[MVP] "
    tabsuffix: ""
    sortpriority: 800
  vip:
    tabprefix: "&6[VIP] "
    tabsuffix: ""
    sortpriority: 600
  default:
    tabprefix: ""
    tabsuffix: ""
    sortpriority: 100

# Configuração de sorting automático
sorting:
  enabled: true
  sorting-types:
    # Ordenar por posição da tag (maior prioridade primeiro)
    - "PLACEHOLDER_HIGH_TO_LOW:%stonetags_current_tag_position%"
    # Depois por nome alfabético
    - "PLACEHOLDER_A_TO_Z:%player%"

# Configuração de nametags (tags acima dos jogadores)
nametags:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%"

# Configuração de belowname (texto abaixo do nome)
belowname:
  enabled: true
  text: "%stonetags_current_tag_name%"

# Configuração de playerlist
playerlist:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%%stonetags_current_tag_suffix%"

# Configuração de scoreboard
scoreboard:
  enabled: false  # Desabilitar se usar o sistema do StoneTags

# Configuração de bossbar
bossbar:
  enabled: false

# Placeholders do StoneTags disponíveis:
# %stonetags_current_tag_id% - ID da tag atual
# %stonetags_current_tag_name% - Nome da tag atual  
# %stonetags_current_tag_prefix% - Prefix da tag atual
# %stonetags_current_tag_suffix% - Suffix da tag atual
# %stonetags_current_tag_color% - Cor da tag atual
# %stonetags_current_tag_position% - Posição da tag atual
# %stonetags_highest_tag_id% - ID da tag de maior prioridade
# %stonetags_highest_tag_name% - Nome da tag de maior prioridade
# %stonetags_highest_tag_position% - Posição da tag de maior prioridade
# %stonetags_has_tag% - Se o jogador tem uma tag selecionada
# %stonetags_tag_count% - Quantidade de tags disponíveis
# %stonetags_formatted_name% - Nome formatado com a tag

# Configuração avançada para usar propriedades temporárias do StoneTags
# O StoneTags define automaticamente estas propriedades no TAB:
# - tagprefix: Prefix da tag atual
# - tagsuffix: Suffix da tag atual  
# - sortpriority: Prioridade de ordenação baseada na posição da tag

# Exemplo de uso das propriedades temporárias:
# format: "%tagprefix%%player%%tagsuffix%"
