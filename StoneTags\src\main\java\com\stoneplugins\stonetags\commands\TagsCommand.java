package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import com.stoneplugins.stonetags.gui.TagsMenu;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class TagsCommand implements CommandExecutor {

    private final StoneTags plugin;

    public TagsCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerOnly");
            return true;
        }

        Player player = (Player) sender;

        // Verificar permissão
        if (!player.hasPermission("stonetags.use")) {
            plugin.getMessageUtils().sendConfigMessage(player, "noPermission");
            return true;
        }

        // Se não há argumentos, abrir menu ou listar tags
        if (args.length == 0) {
            if (plugin.getConfigManager().useMenu()) {
                openTagsMenu(player);
            } else {
                listAvailableTags(player);
            }
            return true;
        }

        // Comandos com argumentos
        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "menu":
            case "gui":
                openTagsMenu(player);
                break;

            case "list":
            case "listar":
                listAvailableTags(player);
                break;

            case "set":
            case "usar":
            case "selecionar":
                if (args.length < 2) {
                    player.sendMessage(plugin.getMessageUtils().colorize("&cUso: /tags set <tag>"));
                    return true;
                }
                setTag(player, args[1]);
                break;

            case "remove":
            case "remover":
            case "clear":
                removeTag(player);
                break;

            case "info":
                if (args.length < 2) {
                    showCurrentTag(player);
                } else {
                    showTagInfo(player, args[1]);
                }
                break;

            case "help":
            case "ajuda":
                showHelp(player);
                break;

            default:
                // Tentar usar como nome de tag
                setTag(player, args[0]);
                break;
        }

        return true;
    }

    private void openTagsMenu(Player player) {
        TagsMenu menu = new TagsMenu(plugin, player, 1);
        menu.open();
    }

    private void listAvailableTags(Player player) {
        List<Tag> availableTags = plugin.getTagManager().getAvailableTags(player);

        if (availableTags.isEmpty()) {
            plugin.getMessageUtils().sendConfigMessage(player, "noTagsAvailable");
            return;
        }

        player.sendMessage(plugin.getMessageUtils().colorize("&6&lTags Disponíveis:"));
        player.sendMessage("");

        for (Tag tag : availableTags) {
            String preview = plugin.getMessageUtils().replacePlaceholders(tag.getPreview(), player);
            preview = plugin.getMessageUtils().replaceTagPlaceholders(preview, tag.getId(), tag.getName(),
                    player.getName());

            player.sendMessage(plugin.getMessageUtils().colorize("&8• &f" + tag.getId() + " &8- " + preview));
        }

        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&7Use &f/tags set <tag> &7para selecionar uma tag"));
    }

    private void setTag(Player player, String tagId) {
        Tag tag = plugin.getTagManager().getTag(tagId);

        if (tag == null) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagNotFound");
            return;
        }

        if (!plugin.getTagManager().canUseTag(player, tag)) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagNoPermission");
            return;
        }

        // Verificar se já está usando esta tag
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
        if (currentTag != null && currentTag.getId().equals(tagId)) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagAlreadySelected");
            return;
        }

        // Definir tag
        plugin.getPlayerDataManager().setSelectedTag(player, tagId);

        // Mensagem de sucesso
        plugin.getMessageUtils().sendConfigMessage(player, "tagSelected",
                "{tag}", plugin.getMessageUtils().colorize(tag.getName()));
    }

    private void removeTag(Player player) {
        if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedTag(player);
        plugin.getMessageUtils().sendConfigMessage(player, "tagRemoved");
    }

    private void showCurrentTag(Player player) {
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);

        if (currentTag == null) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        player.sendMessage(plugin.getMessageUtils().colorize("&6&lSua Tag Atual:"));
        player.sendMessage("");
        showTagDetails(player, currentTag);
    }

    private void showTagInfo(Player player, String tagId) {
        Tag tag = plugin.getTagManager().getTag(tagId);

        if (tag == null) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagNotFound");
            return;
        }

        player.sendMessage(plugin.getMessageUtils().colorize("&6&lInformações da Tag:"));
        player.sendMessage("");
        showTagDetails(player, tag);
    }

    private void showTagDetails(Player player, Tag tag) {
        String preview = plugin.getMessageUtils().replacePlaceholders(tag.getPreview(), player);
        preview = plugin.getMessageUtils().replaceTagPlaceholders(preview, tag.getId(), tag.getName(),
                player.getName());

        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fID: &7" + tag.getId()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: " + tag.getName()));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPreview: " + preview));
        player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPosição: &7" + tag.getPosition()));

        if (tag.hasPermissionRequirement()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7" + tag.getPermission()));
        }

        if (tag.isInvisible()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fTipo: &cInvisível"));
        }

        if (tag.isAnimated()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fAnimada: &aSim"));
        }

        if (tag.hasDescription()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&8• &fDescrição:"));
            for (String line : tag.getDescription()) {
                player.sendMessage(plugin.getMessageUtils().colorize("  &7" + line));
            }
        }
    }

    private void showHelp(Player player) {
        player.sendMessage(plugin.getMessageUtils().colorize("&6&lStoneTags - Comandos:"));
        player.sendMessage("");
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags &8- &7Abrir menu de tags"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags list &8- &7Listar tags disponíveis"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags set <tag> &8- &7Selecionar uma tag"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags remove &8- &7Remover tag atual"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags info [tag] &8- &7Ver informações da tag"));
        player.sendMessage(plugin.getMessageUtils().colorize("&f/tags menu &8- &7Forçar abertura do menu"));
        player.sendMessage("");
    }
}
