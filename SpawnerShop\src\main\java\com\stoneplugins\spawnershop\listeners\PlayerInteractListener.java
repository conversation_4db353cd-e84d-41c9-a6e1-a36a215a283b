package com.stoneplugins.spawnershop.listeners;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

public class PlayerInteractListener implements Listener {

    private final SpawnerShop plugin;

    public PlayerInteractListener(SpawnerShop plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_AIR && event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Player player = event.getPlayer();
        ItemStack item = player.getItemInHand();

        if (item == null || item.getType() == Material.AIR) {
            return;
        }

        // Verificar se é um item de limite
        if (isLimitItem(item)) {
            event.setCancelled(true);

            // Extrair quantidade do item
            int limitAmount = extractLimitAmount(item);
            if (limitAmount > 0) {
                // Ativar o limite
                PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
                int oldLimit = playerData.getPurchaseLimit();
                int newLimit = oldLimit + limitAmount;
                playerData.setPurchaseLimit(newLimit);

                // Salvar no banco de dados
                plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                    plugin.getDatabaseManager().savePlayerData(playerData);
                });

                // Remover item do inventário
                if (item.getAmount() > 1) {
                    item.setAmount(item.getAmount() - 1);
                } else {
                    player.setItemInHand(new ItemStack(Material.AIR));
                }

                // Enviar mensagem de sucesso
                String message = plugin.getConfigManager().getMessage("ativoulimite")
                        .replace("{limite}", String.valueOf(limitAmount));
                player.sendMessage(plugin.getMessageUtils().colorize(message));

                player.sendMessage(
                        plugin.getMessageUtils().colorize("&a&lLIMITE ATUALIZADO! &f" + oldLimit + " → " + newLimit));
            }
        }
    }

    private boolean isLimitItem(ItemStack item) {
        if (item.getType() != Material.PAPER) {
            return false;
        }

        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasDisplayName()) {
            return false;
        }

        String displayName = meta.getDisplayName();
        return displayName.contains("Limite de compra") || displayName.contains("LIMITE")
                || displayName.contains("limite");
    }

    private int extractLimitAmount(ItemStack item) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null || !meta.hasLore()) {
            return 0;
        }

        List<String> lore = meta.getLore();
        for (String line : lore) {
            // Procurar por padrões como "+64", "Quantidade: 64", etc.
            String cleanLine = line.replaceAll("§[0-9a-fk-or]", ""); // Remove color codes

            if (cleanLine.contains("+")) {
                try {
                    String[] parts = cleanLine.split("\\+");
                    if (parts.length > 1) {
                        String numberPart = parts[1].replaceAll("[^0-9]", "");
                        if (!numberPart.isEmpty()) {
                            return Integer.parseInt(numberPart);
                        }
                    }
                } catch (NumberFormatException ignored) {
                }
            }

            if (cleanLine.toLowerCase().contains("quantidade")) {
                try {
                    String numberPart = cleanLine.replaceAll("[^0-9]", "");
                    if (!numberPart.isEmpty()) {
                        return Integer.parseInt(numberPart);
                    }
                } catch (NumberFormatException ignored) {
                }
            }
        }

        return 0;
    }
}
