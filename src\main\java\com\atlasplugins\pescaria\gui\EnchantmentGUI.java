package com.atlasplugins.pescaria.gui;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.enchants.CustomEnchantments;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.UUID;

public class EnchantmentGUI {

    private final Pescaria plugin;
    private final CustomEnchantments customEnchantments;

    public EnchantmentGUI(Pescaria plugin) {
        this.plugin = plugin;
        this.customEnchantments = new CustomEnchantments(plugin);
    }

    public void openEnchantmentMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 45, "§dEncantamentos da Vara");

        UUID uuid = player.getUniqueId();
        customEnchantments.loadPlayerEnchants(uuid);

        // Encantamento Sorte
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.SORTE,
                Material.FEATHER, 20);

        // Encantamento Velocidade
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.VELOCIDADE,
                Material.POTION, 22);

        // Encantamento Fortuna
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.FORTUNA,
                Material.GOLD_INGOT, 24);

        // Encantamento Experiência
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.EXPERIENCIA,
                Material.EXP_BOTTLE, 29);

        // Encantamento Magnetismo
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.MAGNETISMO,
                Material.COMPASS, 31);

        // Encantamento Resistência
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.RESISTENCIA,
                Material.DIAMOND, 33);

        // NOVOS ENCANTAMENTOS
        // Encantamento Redemoinho
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.REDEMOINHO,
                Material.WATER_BUCKET, 11);

        // Encantamento Peixe Duplicado
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.PEIXE_DUPLICADO,
                Material.RAW_FISH, 13);

        // Encantamento Experiência Aprimorada
        createEnchantmentItem(gui, player, CustomEnchantments.EnchantType.EXPERIENCIA_APRIMORADA,
                Material.EMERALD, 15);

        // Item para voltar
        ItemStack backItem = new ItemStack(Material.ARROW);
        ItemMeta backMeta = backItem.getItemMeta();
        backMeta.setDisplayName("§cVoltar");
        backMeta.setLore(Arrays.asList("§7Clique para voltar ao menu principal"));
        backItem.setItemMeta(backMeta);
        gui.setItem(40, backItem);

        player.openInventory(gui);
    }

    private void createEnchantmentItem(Inventory gui, Player player, CustomEnchantments.EnchantType type,
            Material material, int slot) {
        UUID uuid = player.getUniqueId();
        int currentLevel = customEnchantments.getEnchantLevel(uuid, type);
        int maxLevel = customEnchantments.getMaxLevel(type);
        int cost = customEnchantments.getUpgradeCost(type, currentLevel + 1);
        int playerFishes = plugin.getPlayerManager().getPlayerFishes(uuid);

        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();

        // Nome do encantamento
        String displayName = "§e" + type.getDisplayName();
        if (currentLevel > 0) {
            displayName += " §7[Nível " + currentLevel + "]";
        }
        meta.setDisplayName(displayName);

        // Lore do encantamento
        java.util.List<String> lore = new java.util.ArrayList<>();
        lore.add("§7" + type.getDescription());
        lore.add("");
        lore.add("§fNível atual: §e" + currentLevel);
        lore.add("§fNível máximo: §e" + maxLevel);
        lore.add("");

        if (currentLevel >= maxLevel) {
            lore.add("§a§lNÍVEL MÁXIMO ATINGIDO!");
        } else {
            lore.add("§fCusto para evoluir: §e" + cost + " peixes");
            lore.add("§fSeus peixes: §e" + playerFishes);
            lore.add("");

            if (playerFishes >= cost) {
                lore.add("§a§lClique para evoluir!");
            } else {
                lore.add("§c§lPeixes insuficientes!");
            }
        }

        meta.setLore(lore);
        item.setItemMeta(meta);

        // Adicionar brilho se tiver o encantamento
        if (currentLevel > 0) {
            item.addUnsafeEnchantment(org.bukkit.enchantments.Enchantment.DURABILITY, 1);
        }

        gui.setItem(slot, item);
    }

    public boolean handleEnchantmentClick(Player player, String itemName) {
        UUID uuid = player.getUniqueId();

        // Identificar qual encantamento foi clicado
        CustomEnchantments.EnchantType clickedType = null;

        if (itemName.contains("Sorte")) {
            clickedType = CustomEnchantments.EnchantType.SORTE;
        } else if (itemName.contains("Velocidade")) {
            clickedType = CustomEnchantments.EnchantType.VELOCIDADE;
        } else if (itemName.contains("Fortuna")) {
            clickedType = CustomEnchantments.EnchantType.FORTUNA;
        } else if (itemName.contains("Experiência")) {
            clickedType = CustomEnchantments.EnchantType.EXPERIENCIA;
        } else if (itemName.contains("Magnetismo")) {
            clickedType = CustomEnchantments.EnchantType.MAGNETISMO;
        } else if (itemName.contains("Resistência")) {
            clickedType = CustomEnchantments.EnchantType.RESISTENCIA;
        } else if (itemName.contains("Redemoinho")) {
            clickedType = CustomEnchantments.EnchantType.REDEMOINHO;
        } else if (itemName.contains("Peixe Duplicado")) {
            clickedType = CustomEnchantments.EnchantType.PEIXE_DUPLICADO;
        } else if (itemName.contains("Experiência Aprimorada")) {
            clickedType = CustomEnchantments.EnchantType.EXPERIENCIA_APRIMORADA;
        }

        if (clickedType != null) {
            if (customEnchantments.upgradeEnchant(player, clickedType)) {
                // Reabrir menu para mostrar mudanças
                openEnchantmentMenu(player);

                // Atualizar vara no inventário
                updatePlayerFishingRod(player);

                return true;
            }
        }

        return false;
    }

    private void updatePlayerFishingRod(Player player) {
        // Procurar vara de pesca no inventário e atualizar
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && item.getType() == Material.FISHING_ROD) {
                if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                    String displayName = item.getItemMeta().getDisplayName();
                    if (displayName.contains("Vara de Pesca")) {
                        // Atualizar lore com novos encantamentos
                        customEnchantments.updateFishingRodLore(player, item);
                        break;
                    }
                }
            }
        }
    }
}
