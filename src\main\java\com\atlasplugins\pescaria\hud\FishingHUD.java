package com.atlasplugins.pescaria.hud;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class FishingHUD {

    private final Pescaria plugin;
    private final Map<UUID, Boolean> activeFishers;
    private final Map<UUID, Integer> fishingTime;

    public FishingHUD(Pescaria plugin) {
        this.plugin = plugin;
        this.activeFishers = new HashMap<>();
        this.fishingTime = new HashMap<>();
        startHUDUpdater();
    }

    public void startFishing(Player player) {
        UUID uuid = player.getUniqueId();
        activeFishers.put(uuid, true);
        fishingTime.put(uuid, 0);

        // Mostrar mensagem de início
        player.sendMessage("§a§lPESCA! §aVocê começou a pescar, colete o máximo de");
        player.sendMessage("§apeixes que puder, isso irá te ajudar a receber recompensas.");
    }

    public void stopFishing(Player player) {
        UUID uuid = player.getUniqueId();
        activeFishers.remove(uuid);
        fishingTime.remove(uuid);

        // Mostrar mensagem de fim
        player.sendMessage("§c§lPESCA! §cVocê parou de pescar, para mais informações");
        player.sendMessage("§cuse o comando §f/pesca §ce veja os resultados que teve pescando.");
    }

    public boolean isFishing(UUID uuid) {
        return activeFishers.getOrDefault(uuid, false);
    }

    private void startHUDUpdater() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (UUID uuid : activeFishers.keySet()) {
                    Player player = plugin.getServer().getPlayer(uuid);
                    if (player != null && player.isOnline()) {
                        updateHUD(player);

                        // Incrementar tempo de pesca
                        int currentTime = fishingTime.getOrDefault(uuid, 0);
                        fishingTime.put(uuid, currentTime + 1);
                    } else {
                        // Remover jogador offline
                        activeFishers.remove(uuid);
                        fishingTime.remove(uuid);
                    }
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // Atualizar a cada segundo
    }

    private void updateHUD(Player player) {
        UUID uuid = player.getUniqueId();

        // Verificar se está no mundo de pesca
        String currentWorld = player.getWorld().getName();
        String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();

        if (!currentWorld.equalsIgnoreCase(pescariaWorld)) {
            stopFishing(player);
            return;
        }

        // Obter informações do jogador
        int fishes = plugin.getPlayerManager().getPlayerFishes(uuid);
        int fishingTimeSeconds = fishingTime.getOrDefault(uuid, 0);

        // Obter informações da vara
        int rodXP = plugin.getConfigManager().getDataConfig()
                .getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
        int rodLevel = calculateLevel(rodXP);

        // Verificar se tem booster ativo (TODO: implementar sistema de boosters)
        boolean hasBooster = false;
        String boosterText = hasBooster ? "§d⚡ Ativo" : "§7⚡ Inativo";

        // Criar HUD
        StringBuilder hud = new StringBuilder();
        hud.append("§a§lPESCANDO... §f").append(formatTime(fishingTimeSeconds)).append("\n");
        hud.append("§e§l✧ §fPeixes: §e").append(fishes).append("\n");
        hud.append("§b§l⛁ §fXP Vara: §b").append(rodXP).append("\n");
        hud.append("§6§l⭐ §fNível: §6").append(rodLevel).append("\n");
        hud.append(boosterText);

        // Enviar como action bar (título na parte inferior da tela)
        sendActionBar(player, hud.toString());
    }

    private void sendActionBar(Player player, String message) {
        // Para Minecraft 1.8.8, usar mensagem normal
        player.sendMessage(message);
    }

    private String formatTime(int seconds) {
        int minutes = seconds / 60;
        int remainingSeconds = seconds % 60;

        if (minutes > 0) {
            return String.format("%dm %ds", minutes, remainingSeconds);
        } else {
            return String.format("%ds", remainingSeconds);
        }
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10;
    }

    // Métodos para integração com outros sistemas
    public void showFishCaughtHUD(Player player, int fishAmount, int xpGained) {
        String message = String.format("§a§l+%d 🐟 §b§l+%d ⛁", fishAmount, xpGained);
        sendActionBar(player, message);
    }

    public void showLevelUpHUD(Player player, int newLevel) {
        String message = "§6§l⭐ LEVEL UP! ⭐ §fNível " + newLevel;
        sendActionBar(player, message);

        // Também mostrar como mensagem especial
        player.sendMessage("§6§l⭐ LEVEL UP! ⭐");
        player.sendMessage("§fVara de Pesca Nível §e" + newLevel);
    }

    public void showBoosterActivated(Player player, String boosterType, int duration) {
        String message = "§d§l⚡ BOOSTER ATIVADO! §fTipo: " + boosterType + " §7(" + duration + "min)";
        sendActionBar(player, message);
    }

    public void showCoinsEarned(Player player, double amount) {
        String message = "§a§l+$" + String.format("%.0f", amount) + " 💰";
        sendActionBar(player, message);
    }

    // Limpar HUD quando jogador sair
    public void clearHUD(UUID uuid) {
        activeFishers.remove(uuid);
        fishingTime.remove(uuid);
    }
}
