package com.stoneplugins.stonetags.gui;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import com.stoneplugins.stonetags.utils.ItemUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class TagsMenu {

    private final StoneTags plugin;
    private final Player player;
    private final int page;
    private final int maxPage;
    private final List<Tag> availableTags;
    private final boolean showingSuffixes; // true = sufixos, false = tags

    public TagsMenu(StoneTags plugin, Player player, int page) {
        this(plugin, player, page, false); // Por padrão, mostrar tags
    }

    public TagsMenu(StoneTags plugin, Player player, int page, boolean showingSuffixes) {
        this.plugin = plugin;
        this.player = player;
        this.page = page;
        this.showingSuffixes = showingSuffixes;
        this.availableTags = plugin.getTagManager().getAvailableTags(player);

        // Calcular slots disponíveis
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");
        int itemsPerPage = slots.length;

        this.maxPage = plugin.getTagManager().getMaxPages(player, itemsPerPage);
    }

    public void open() {
        int size = plugin.getConfigManager().getMenuSize();

        // Título melhorado com cores e informações
        String menuType = showingSuffixes ? "&d&lSUFIXOS" : "&6&lTAGS";
        String title = plugin.getMessageUtils()
                .colorize("&8&l✦ " + menuType + " &8&l✦ &7Página " + page + "/" + maxPage);

        Inventory inventory = Bukkit.createInventory(null, size, title);

        // Preencher com vidro decorativo
        fillWithGlass(inventory);

        // Adicionar abas de navegação entre tags e sufixos
        addTabButtons(inventory);

        if (showingSuffixes) {
            // Adicionar sufixos
            addSuffixes(inventory);
        } else {
            // Adicionar tags
            addTags(inventory);
        }

        // Adicionar itens de navegação
        addNavigationItems(inventory);

        // Adicionar item de remover
        addRemoveItem(inventory);

        player.openInventory(inventory);
    }

    private void fillWithGlass(Inventory inventory) {
        String materialData = plugin.getConfigManager().getDecorationMaterial();
        String name = plugin.getConfigManager().getDecorationName();

        ItemStack glass = ItemUtils.createItem(materialData, name);

        for (int i = 0; i < inventory.getSize(); i++) {
            inventory.setItem(i, glass);
        }
    }

    private void addTags(Inventory inventory) {
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");
        int itemsPerPage = slots.length;

        List<Tag> tagsForPage = plugin.getTagManager().getTagsForPage(player, page, itemsPerPage);

        for (int i = 0; i < tagsForPage.size() && i < slots.length; i++) {
            Tag tag = tagsForPage.get(i);
            int slot = Integer.parseInt(slots[i].trim());

            ItemStack tagItem = createTagItem(tag);
            inventory.setItem(slot, tagItem);
        }
    }

    private ItemStack createTagItem(Tag tag) {
        // Determinar material do ícone
        Material material = ItemUtils.getMaterialFromString(tag.getIcon());
        ItemStack item = new ItemStack(material);

        ItemMeta meta = item.getItemMeta();
        if (meta == null)
            return item;

        // Nome do item - usar cor da tag + nome limpo
        String tagColor = tag.getColor() != null ? tag.getColor() : "&f";
        String cleanName = tag.getName().replaceAll("&[0-9a-fk-or]", ""); // Remove códigos de cor
        String displayName = plugin.getMessageUtils().colorize(tagColor + "&l" + cleanName);
        meta.setDisplayName(displayName);

        // Lore do item
        List<String> lore = new ArrayList<>();

        // Mostrar a tag como aparecerá
        String tagPrefix = plugin.getMessageUtils().colorize(tag.getCurrentPrefix(player));
        lore.add(plugin.getMessageUtils().colorize("&7Tag: " + tagPrefix));
        lore.add("");

        // Preview completo
        String playerNameWithColor = tagColor + player.getName();
        String fullPreview = tagPrefix + playerNameWithColor;
        lore.add(plugin.getMessageUtils().colorize("&7Preview: " + fullPreview));
        lore.add("");

        // Adicionar descrição se existir
        if (tag.hasDescription()) {
            lore.add(plugin.getMessageUtils().colorize("&7Descrição:"));
            for (String line : tag.getDescription()) {
                lore.add(plugin.getMessageUtils().colorize("&8• " + line));
            }
            lore.add("");
        }
        lore.add("");

        // Informações da tag
        lore.add(plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));
        lore.add(plugin.getMessageUtils().colorize("&7Prioridade: &f" + tag.getPosition()));

        // Permissão
        if (tag.hasPermissionRequirement()) {
            boolean hasPermission = player.hasPermission(tag.getPermission());
            String permissionStatus = hasPermission ? "&aVocê tem" : "&cVocê não tem";
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: " + permissionStatus));
        } else {
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: &aNenhuma necessária"));
        }

        // Características especiais
        if (tag.isInvisible()) {
            lore.add(plugin.getMessageUtils().colorize("&7Efeito: &8Invisível"));
        }

        if (tag.isAnimated()) {
            lore.add(plugin.getMessageUtils().colorize("&7Tipo: &dAnimada &7(frames: " +
                    (tag.getAnimationFrames() != null ? tag.getAnimationFrames().size() : 0) + ")"));
        }

        lore.add(plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));

        // Status da tag
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
        if (currentTag != null && currentTag.getId().equals(tag.getId())) {
            lore.add(plugin.getMessageUtils().colorize("&a&l✓ TAG ATUAL"));
            lore.add(plugin.getMessageUtils().colorize("&7Você está usando esta tag"));
        } else {
            // Verificar se pode usar
            if (!tag.hasPermissionRequirement() || player.hasPermission(tag.getPermission())) {
                lore.add(plugin.getMessageUtils().colorize("&e&l➤ CLIQUE PARA USAR"));
                lore.add(plugin.getMessageUtils().colorize("&7Clique para selecionar esta tag"));
            } else {
                lore.add(plugin.getMessageUtils().colorize("&c&l✗ SEM PERMISSÃO"));
                lore.add(plugin.getMessageUtils().colorize("&7Você não pode usar esta tag"));
            }
        }

        meta.setLore(lore);
        item.setItemMeta(meta);

        return item;
    }

    private void addTabButtons(Inventory inventory) {
        // Botão para Tags (slot 0)
        ItemStack tagsButton = new ItemStack(Material.NAME_TAG);
        ItemMeta tagsMeta = tagsButton.getItemMeta();
        if (!showingSuffixes) {
            tagsMeta.setDisplayName(plugin.getMessageUtils().colorize("&6&l✦ TAGS ✦"));
            tagsMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&a&l✓ SEÇÃO ATUAL"),
                    plugin.getMessageUtils().colorize("&7Você está visualizando as tags"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Clique em &d&lSUFIXOS &7para trocar"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        } else {
            tagsMeta.setDisplayName(plugin.getMessageUtils().colorize("&6&l✦ TAGS ✦"));
            tagsMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&e&l➤ CLIQUE PARA TROCAR"),
                    plugin.getMessageUtils().colorize("&7Clique para ver as tags"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Prefixos para seu nome"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        }
        tagsButton.setItemMeta(tagsMeta);
        inventory.setItem(0, tagsButton);

        // Botão para Sufixos (slot 8)
        ItemStack suffixButton = new ItemStack(Material.PAPER);
        ItemMeta suffixMeta = suffixButton.getItemMeta();
        if (showingSuffixes) {
            suffixMeta.setDisplayName(plugin.getMessageUtils().colorize("&d&l✦ SUFIXOS ✦"));
            suffixMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&a&l✓ SEÇÃO ATUAL"),
                    plugin.getMessageUtils().colorize("&7Você está visualizando os sufixos"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Clique em &6&lTAGS &7para trocar"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        } else {
            suffixMeta.setDisplayName(plugin.getMessageUtils().colorize("&d&l✦ SUFIXOS ✦"));
            suffixMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&e&l➤ CLIQUE PARA TROCAR"),
                    plugin.getMessageUtils().colorize("&7Clique para ver os sufixos"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Texto após seu nome"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        }
        suffixButton.setItemMeta(suffixMeta);
        inventory.setItem(8, suffixButton);
    }

    private void addSuffixes(Inventory inventory) {
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");
        int itemsPerPage = slots.length;

        List<com.stoneplugins.stonetags.data.Suffix> availableSuffixes = plugin.getSuffixManager()
                .getAvailableSuffixes(player);

        // Calcular sufixos para esta página
        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, availableSuffixes.size());

        for (int i = startIndex; i < endIndex && (i - startIndex) < slots.length; i++) {
            com.stoneplugins.stonetags.data.Suffix suffix = availableSuffixes.get(i);
            int slot = Integer.parseInt(slots[i - startIndex].trim());

            ItemStack suffixItem = createSuffixItem(suffix);
            inventory.setItem(slot, suffixItem);
        }
    }

    private ItemStack createSuffixItem(com.stoneplugins.stonetags.data.Suffix suffix) {
        ItemStack item = new ItemStack(Material.PAPER);
        ItemMeta meta = item.getItemMeta();

        // Nome do item
        String suffixDisplay = plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
        meta.setDisplayName(plugin.getMessageUtils().colorize("&d&l" + suffix.getName()));

        // Lore do item
        java.util.List<String> lore = new java.util.ArrayList<>();

        // Mostrar o suffix como aparecerá
        lore.add(plugin.getMessageUtils().colorize("&7Suffix: " + suffixDisplay));
        lore.add("");

        // Preview completo
        String fullPreview = player.getName() + suffixDisplay;
        lore.add(plugin.getMessageUtils().colorize("&7Preview: " + fullPreview));
        lore.add("");

        // Informações do suffix
        lore.add(plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));
        lore.add(plugin.getMessageUtils().colorize("&7Prioridade: &f" + suffix.getPriority()));

        // Permissão
        if (suffix.requiresPermission()) {
            boolean hasPermission = player.hasPermission(suffix.getPermission());
            String permissionStatus = hasPermission ? "&aVocê tem" : "&cVocê não tem";
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: " + permissionStatus));
        } else {
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: &aNenhuma necessária"));
        }

        // Características especiais
        if (suffix.isInvisible()) {
            lore.add(plugin.getMessageUtils().colorize("&7Efeito: &8Invisível"));
        }

        if (suffix.isAnimated()) {
            lore.add(plugin.getMessageUtils().colorize("&7Tipo: &dAnimado"));
        }

        lore.add(plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"));

        // Status do suffix
        com.stoneplugins.stonetags.data.Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
        if (currentSuffix != null && currentSuffix.getId().equals(suffix.getId())) {
            lore.add(plugin.getMessageUtils().colorize("&a&l✓ SUFFIX ATUAL"));
            lore.add(plugin.getMessageUtils().colorize("&7Você está usando este suffix"));
        } else {
            // Verificar se pode usar
            if (!suffix.requiresPermission() || player.hasPermission(suffix.getPermission())) {
                lore.add(plugin.getMessageUtils().colorize("&e&l➤ CLIQUE PARA USAR"));
                lore.add(plugin.getMessageUtils().colorize("&7Clique para selecionar este suffix"));
            } else {
                lore.add(plugin.getMessageUtils().colorize("&c&l✗ SEM PERMISSÃO"));
                lore.add(plugin.getMessageUtils().colorize("&7Você não pode usar este suffix"));
            }
        }

        meta.setLore(lore);
        item.setItemMeta(meta);

        return item;
    }

    private void addNavigationItems(Inventory inventory) {
        // Página anterior
        if (page > 1) {
            int slot = plugin.getConfigManager().getPreviousPageSlot();
            String material = plugin.getConfigManager().getPreviousPageMaterial();
            String name = plugin.getConfigManager().getPreviousPageName();

            ItemStack prevItem = ItemUtils.createItem(material, name);
            ItemMeta meta = prevItem.getItemMeta();
            if (meta != null) {
                List<String> lore = new ArrayList<>();
                lore.add(plugin.getMessageUtils().colorize("&7Página atual: &f" + page));
                lore.add(plugin.getMessageUtils().colorize("&7Clique para voltar"));
                meta.setLore(lore);
                prevItem.setItemMeta(meta);
            }

            inventory.setItem(slot, prevItem);
        }

        // Próxima página
        if (page < maxPage) {
            int slot = plugin.getConfigManager().getNextPageSlot();
            String material = plugin.getConfigManager().getNextPageMaterial();
            String name = plugin.getConfigManager().getNextPageName();

            ItemStack nextItem = ItemUtils.createItem(material, name);
            ItemMeta meta = nextItem.getItemMeta();
            if (meta != null) {
                List<String> lore = new ArrayList<>();
                lore.add(plugin.getMessageUtils().colorize("&7Página atual: &f" + page));
                lore.add(plugin.getMessageUtils().colorize("&7Clique para avançar"));
                meta.setLore(lore);
                nextItem.setItemMeta(meta);
            }

            inventory.setItem(slot, nextItem);
        }
    }

    private void addRemoveTagItem(Inventory inventory) {
        // Só adicionar se o jogador tem uma tag selecionada
        if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
            return;
        }

        int slot = plugin.getConfigManager().getRemoveTagSlot();
        String material = plugin.getConfigManager().getRemoveTagMaterial();
        String name = plugin.getConfigManager().getRemoveTagName();

        ItemStack removeItem = ItemUtils.createItem(material, name);
        ItemMeta meta = removeItem.getItemMeta();
        if (meta != null) {
            List<String> lore = new ArrayList<>();

            Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
            if (currentTag != null) {
                lore.add(plugin.getMessageUtils().colorize("&7Tag atual: " + currentTag.getName()));
                lore.add("");
            }

            lore.addAll(plugin.getConfig().getStringList("Menu.removeTag.lore"));
            lore.add("");
            lore.add(plugin.getMessageUtils().colorize("&cClique para remover"));

            // Colorizar lore
            for (int i = 0; i < lore.size(); i++) {
                lore.set(i, plugin.getMessageUtils().colorize(lore.get(i)));
            }

            meta.setLore(lore);
            removeItem.setItemMeta(meta);
        }

        inventory.setItem(slot, removeItem);
    }

    public void handleClick(int slot) {
        // Verificar se é um slot de tag
        String[] tagSlots = plugin.getConfigManager().getTagSlots().split(",");
        for (int i = 0; i < tagSlots.length; i++) {
            int tagSlot = Integer.parseInt(tagSlots[i].trim());
            if (slot == tagSlot) {
                handleTagClick(i);
                return;
            }
        }

        // Verificar navegação
        if (slot == plugin.getConfigManager().getPreviousPageSlot() && page > 1) {
            TagsMenu prevMenu = new TagsMenu(plugin, player, page - 1);
            prevMenu.open();
            return;
        }

        if (slot == plugin.getConfigManager().getNextPageSlot() && page < maxPage) {
            TagsMenu nextMenu = new TagsMenu(plugin, player, page + 1);
            nextMenu.open();
            return;
        }

        // Verificar remover tag
        if (slot == plugin.getConfigManager().getRemoveTagSlot()) {
            handleRemoveTag();
            return;
        }
    }

    private void handleTagClick(int index) {
        String[] slots = plugin.getConfigManager().getTagSlots().split(",");
        int itemsPerPage = slots.length;

        List<Tag> tagsForPage = plugin.getTagManager().getTagsForPage(player, page, itemsPerPage);

        if (index >= tagsForPage.size()) {
            return;
        }

        Tag tag = tagsForPage.get(index);

        // Verificar se pode usar a tag
        if (!plugin.getTagManager().canUseTag(player, tag)) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagNoPermission");
            return;
        }

        // Verificar se já está usando esta tag
        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(player);
        if (currentTag != null && currentTag.getId().equals(tag.getId())) {
            plugin.getMessageUtils().sendConfigMessage(player, "tagAlreadySelected");
            return;
        }

        // Selecionar tag
        plugin.getPlayerDataManager().setSelectedTag(player, tag.getId());

        // Mensagem de sucesso
        plugin.getMessageUtils().sendConfigMessage(player, "tagSelected",
                "{tag}", plugin.getMessageUtils().colorize(tag.getName()));

        // Fechar menu
        player.closeInventory();
    }

    private void handleRemoveTag() {
        if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
            player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhuma tag!"));
            return;
        }

        plugin.getPlayerDataManager().removeSelectedTag(player);
        plugin.getMessageUtils().sendConfigMessage(player, "tagRemoved");

        // Fechar menu
        player.closeInventory();
    }

    private void addRemoveItem(Inventory inventory) {
        ItemStack removeItem = new ItemStack(Material.BARRIER);
        ItemMeta removeMeta = removeItem.getItemMeta();

        if (showingSuffixes) {
            removeMeta.setDisplayName(plugin.getMessageUtils().colorize("&c&l✗ REMOVER SUFFIX"));
            removeMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&c&lREMOVER SUFFIX ATUAL"),
                    plugin.getMessageUtils().colorize("&7Clique para remover seu suffix"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Seu nome ficará sem suffix"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        } else {
            removeMeta.setDisplayName(plugin.getMessageUtils().colorize("&c&l✗ REMOVER TAG"));
            removeMeta.setLore(java.util.Arrays.asList(
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬"),
                    plugin.getMessageUtils().colorize("&c&lREMOVER TAG ATUAL"),
                    plugin.getMessageUtils().colorize("&7Clique para remover sua tag"),
                    plugin.getMessageUtils().colorize(""),
                    plugin.getMessageUtils().colorize("&7Seu nome ficará sem prefix"),
                    plugin.getMessageUtils().colorize("&8&l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬")));
        }

        removeItem.setItemMeta(removeMeta);
        inventory.setItem(49, removeItem); // Slot 49 (centro da linha inferior)
    }

    public void populateInventory(Inventory inventory) {
        // Adicionar abas de navegação entre tags e sufixos
        addTabButtons(inventory);

        if (showingSuffixes) {
            // Adicionar sufixos
            addSuffixes(inventory);
        } else {
            // Adicionar tags
            addTags(inventory);
        }

        // Adicionar itens de navegação
        addNavigationItems(inventory);
    }

}
