@echo off
echo Compilando SpawnerShop Plugin...

if exist "target" rmdir /s /q "target"
mkdir "target"
mkdir "target\classes"

echo Copiando recursos...
xcopy /s /y "src\main\resources\*" "target\classes\"

echo Compilando classes Java...
javac -cp "lib\*" -d "target\classes" -sourcepath "src\main\java" "src\main\java\com\stoneplugins\spawnershop\*.java" "src\main\java\com\stoneplugins\spawnershop\**\*.java"

if %errorlevel% neq 0 (
    echo Erro na compilacao!
    pause
    exit /b 1
)

echo Criando JAR...
cd "target\classes"
jar cf "..\SpawnerShop-1.0.jar" *
cd "..\..\"

echo Plugin compilado com sucesso!
echo Arquivo gerado: target\SpawnerShop-1.0.jar
pause
