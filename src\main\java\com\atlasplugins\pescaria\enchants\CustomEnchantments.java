package com.atlasplugins.pescaria.enchants;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class CustomEnchantments {

    private final Pescaria plugin;
    private final Map<UUID, Map<String, Integer>> playerEnchants;

    public CustomEnchantments(Pescaria plugin) {
        this.plugin = plugin;
        this.playerEnchants = new HashMap<>();
    }

    // Encantamentos customizados
    public enum EnchantType {
        SORTE("Sorte", "Aumenta a chance de pescar peixes em 10% por nível"),
        VELOCIDADE("Velocidade", "Diminui o tempo de pesca em 1 segundo por nível"),
        FORTUNA("Fortuna", "Aumenta a quantidade de peixes ganhos em 10% por nível"),
        EXPERIENCIA("Experiência", "Aumenta o XP ganho da vara em 25% por nível"),
        MAGNETISMO("Magnetismo", "Atrai peixes em um raio maior"),
        RESISTENCIA("Resistência", "Vara tem chance de não quebrar"),
        REDEMOINHO("Redemoinho", "Atraia peixes em segundos - reduz tempo de espera"),
        PEIXE_DUPLICADO("Peixe Duplicado", "Pega dois, paga um - chance de pescar 2x peixes"),
        EXPERIENCIA_APRIMORADA("Experiência Aprimorada", "Aprende até com peixe - mais XP ao pescar");

        private final String displayName;
        private final String description;

        EnchantType(String displayName, String description) {
            this.displayName = displayName;
            this.description = description;
        }

        public String getDisplayName() {
            return displayName;
        }

        public String getDescription() {
            return description;
        }
    }

    public void loadPlayerEnchants(UUID uuid) {
        Map<String, Integer> enchants = new HashMap<>();

        for (EnchantType type : EnchantType.values()) {
            int level = plugin.getConfigManager().getDataConfig()
                    .getInt("Players." + uuid.toString() + ".Enchants." + type.name(), 0);
            enchants.put(type.name(), level);
        }

        playerEnchants.put(uuid, enchants);
    }

    public void savePlayerEnchants(UUID uuid) {
        Map<String, Integer> enchants = playerEnchants.get(uuid);
        if (enchants == null)
            return;

        for (Map.Entry<String, Integer> entry : enchants.entrySet()) {
            plugin.getConfigManager().getDataConfig()
                    .set("Players." + uuid.toString() + ".Enchants." + entry.getKey(), entry.getValue());
        }
        plugin.getConfigManager().saveDataConfig();
    }

    public int getEnchantLevel(UUID uuid, EnchantType type) {
        Map<String, Integer> enchants = playerEnchants.get(uuid);
        if (enchants == null) {
            loadPlayerEnchants(uuid);
            enchants = playerEnchants.get(uuid);
        }
        return enchants.getOrDefault(type.name(), 0);
    }

    public void setEnchantLevel(UUID uuid, EnchantType type, int level) {
        Map<String, Integer> enchants = playerEnchants.get(uuid);
        if (enchants == null) {
            loadPlayerEnchants(uuid);
            enchants = playerEnchants.get(uuid);
        }
        enchants.put(type.name(), level);
        savePlayerEnchants(uuid);
    }

    public boolean upgradeEnchant(Player player, EnchantType type) {
        UUID uuid = player.getUniqueId();
        loadPlayerEnchants(uuid); // Carregar dados primeiro

        int currentLevel = getEnchantLevel(uuid, type);
        int maxLevel = getMaxLevel(type);

        // Debug para verificar níveis
        plugin.getLogger().info("Player: " + player.getName() + ", Enchant: " + type + ", Current: " + currentLevel
                + ", Max: " + maxLevel);

        if (currentLevel >= maxLevel) {
            player.sendMessage("§c§lPESCARIA §fEste encantamento já está no nível máximo! (§e" + currentLevel + "/"
                    + maxLevel + "§f)");
            return false;
        }

        int cost = getUpgradeCost(type, currentLevel + 1);
        int playerFishes = plugin.getPlayerManager().getPlayerFishes(uuid);

        if (playerFishes < cost) {
            player.sendMessage("§c§lPESCARIA §fVocê precisa de §e" + cost + " §fpeixes para este upgrade!");
            return false;
        }

        // Remover peixes e aumentar nível
        plugin.getPlayerManager().setPlayerFishes(uuid, playerFishes - cost);
        setEnchantLevel(uuid, type, currentLevel + 1);
        savePlayerEnchants(uuid); // Salvar dados

        player.sendMessage("§a§lPESCARIA §fVocê evoluiu §e" + type.getDisplayName() + " §fpara o nível §e"
                + (currentLevel + 1) + "§f!");

        return true;
    }

    public int getMaxLevel(EnchantType type) {
        switch (type) {
            case SORTE:
            case VELOCIDADE:
            case FORTUNA:
            case REDEMOINHO:
                return 5;
            case EXPERIENCIA:
            case MAGNETISMO:
                return 3;
            case RESISTENCIA:
            case PEIXE_DUPLICADO:
            case EXPERIENCIA_APRIMORADA:
                return 2;
            default:
                return 1;
        }
    }

    public int getUpgradeCost(EnchantType type, int level) {
        int baseCost;
        switch (type) {
            case SORTE:
                baseCost = 1000;
                break;
            case VELOCIDADE:
                baseCost = 1500;
                break;
            case FORTUNA:
                baseCost = 2500;
                break;
            case EXPERIENCIA:
                baseCost = 2000;
                break;
            case MAGNETISMO:
                baseCost = 3000;
                break;
            case RESISTENCIA:
                baseCost = 5000;
                break;
            case REDEMOINHO:
                baseCost = 2000;
                break;
            case PEIXE_DUPLICADO:
                baseCost = 4000;
                break;
            case EXPERIENCIA_APRIMORADA:
                baseCost = 3500;
                break;
            default:
                baseCost = 1000;
        }

        // Custo aumenta exponencialmente
        return baseCost * level * level;
    }

    // Aplicar efeitos dos encantamentos REAIS
    public double applyLuckBonus(UUID uuid, double baseChance) {
        int luckLevel = getEnchantLevel(uuid, EnchantType.SORTE);
        // Cada nível de sorte adiciona 15% de chance
        return baseChance + (luckLevel * 15.0);
    }

    public int applySpeedBonus(UUID uuid, int baseTime) {
        int speedLevel = getEnchantLevel(uuid, EnchantType.VELOCIDADE);
        // Cada nível reduz 0.5 segundos do tempo de pesca
        double reduction = speedLevel * 0.5;
        return Math.max(1, (int) (baseTime - reduction));
    }

    public int applyFortuneBonus(UUID uuid, int baseFish) {
        int fortuneLevel = getEnchantLevel(uuid, EnchantType.FORTUNA);
        // Cada nível de fortuna adiciona 1 peixe extra com 25% de chance
        int extraFish = 0;
        for (int i = 0; i < fortuneLevel; i++) {
            if (Math.random() < 0.25) { // 25% de chance por nível
                extraFish++;
            }
        }
        return baseFish + extraFish;
    }

    public int applyExperienceBonus(UUID uuid, int baseXP) {
        int expLevel = getEnchantLevel(uuid, EnchantType.EXPERIENCIA);
        // Cada nível adiciona 50% mais XP
        double multiplier = 1.0 + (expLevel * 0.5);
        return (int) Math.ceil(baseXP * multiplier);
    }

    public boolean hasMagnetism(UUID uuid) {
        return getEnchantLevel(uuid, EnchantType.MAGNETISMO) > 0;
    }

    public boolean hasResistance(UUID uuid) {
        return getEnchantLevel(uuid, EnchantType.RESISTENCIA) > 0;
    }

    public double getResistanceChance(UUID uuid) {
        int resistanceLevel = getEnchantLevel(uuid, EnchantType.RESISTENCIA);
        return resistanceLevel * 0.25; // 25% por nível
    }

    public void updateFishingRodLore(Player player, ItemStack fishingRod) {
        if (fishingRod == null || !fishingRod.hasItemMeta())
            return;

        ItemMeta meta = fishingRod.getItemMeta();
        UUID uuid = player.getUniqueId();

        // Atualizar lore com encantamentos
        java.util.List<String> lore = new java.util.ArrayList<>();

        // Informações básicas
        int xp = plugin.getConfigManager().getDataConfig().getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
        int level = calculateLevel(xp);

        lore.add("§7XP: §a" + xp + "§7/" + getXPForNextLevel(level));
        lore.add("");

        // Encantamentos customizados
        for (EnchantType type : EnchantType.values()) {
            int enchantLevel = getEnchantLevel(uuid, type);
            if (enchantLevel > 0) {
                lore.add("§7" + type.getDisplayName() + " §b" + enchantLevel);
            }
        }

        lore.add("");
        lore.add("§8minecraft:fishing_rod");
        lore.add("§8NBT: 1 tag(s)");

        meta.setLore(lore);
        fishingRod.setItemMeta(meta);
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10;
    }

    private int getXPForNextLevel(int currentLevel) {
        switch (currentLevel) {
            case 1:
                return 250;
            case 2:
                return 500;
            case 3:
                return 1000;
            case 4:
                return 2000;
            case 5:
                return 4000;
            case 6:
                return 8000;
            case 7:
                return 16000;
            case 8:
                return 32000;
            case 9:
                return 64000;
            default:
                return 64000;
        }
    }
}
