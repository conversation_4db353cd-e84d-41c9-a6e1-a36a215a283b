package com.stoneplugins.spawnershop.commands;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.gui.RankingMenu;
import com.stoneplugins.spawnershop.utils.ItemUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Arrays;

public class LimiteCommand implements CommandExecutor {

    private final SpawnerShop plugin;

    public LimiteCommand(SpawnerShop plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (args.length == 0) {
            // Jogador quer ver seu próprio limite
            if (sender instanceof Player) {
                Player player = (Player) sender;
                PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
                sender.sendMessage(plugin.getMessageUtils().colorize("&e&lSEU LIMITE:"));
                sender.sendMessage(
                        plugin.getMessageUtils()
                                .colorize("&f&l> &7Limite por compra: &a" + playerData.getPurchaseLimit()));
                sender.sendMessage(plugin.getMessageUtils()
                        .colorize("&f&l> &7Compras realizadas: &b" + playerData.getPurchases()));
                sender.sendMessage(plugin.getMessageUtils().colorize(
                        "&f&l> &7Multiplicador: &d" + playerData.getMultiplicador() + "x"));
            } else {
                sender.sendMessage(plugin.getMessageUtils()
                        .colorize("&c&lApenas jogadores podem usar este comando sem argumentos."));
            }
            return true;
        }

        String subCommand = args[0].toLowerCase();

        // Comandos administrativos
        if (subCommand.equals("set") || subCommand.equals("add") || subCommand.equals("remove")
                || subCommand.equals("giveitem") || subCommand.equals("help")) {
            if (!sender.hasPermission("stormplugins.spawnersshopv2.admin")) {
                sender.sendMessage(plugin.getMessageUtils()
                        .colorize("&c&lSEM PERMISSAO! &fVoce nao tem permissao para usar este comando."));
                return true;
            }
        } else if (subCommand.equals("top")) {
            // Comando top não precisa de permissão
        } else {
            // Verificar limite de outro jogador (apenas admins)
            if (!sender.hasPermission("stormplugins.spawnersshopv2.admin")) {
                sender.sendMessage(plugin.getMessageUtils()
                        .colorize("&c&lSEM PERMISSAO! &fVoce nao tem permissao para ver limite de outros jogadores."));
                return true;
            }
        }

        if (args[0].equalsIgnoreCase("help")) {
            sendHelpMessage(sender);
            return true;
        }

        switch (subCommand) {
            case "help":
                sendHelpMessage(sender);
                break;

            case "top":
                if (sender instanceof Player) {
                    new RankingMenu(plugin, (Player) sender).open();
                } else {
                    sender.sendMessage("§cEste comando só pode ser usado por jogadores!");
                }
                break;

            case "set":
                if (args.length != 3) {
                    sender.sendMessage("§c✖ Uso correto: /limite set <jogador> <quantidade>");
                    return true;
                }
                handleSetCommand(sender, args[1], args[2]);
                break;

            case "add":
                if (args.length != 3) {
                    sender.sendMessage("§c✖ Uso correto: /limite add <jogador> <quantidade>");
                    return true;
                }
                handleAddCommand(sender, args[1], args[2]);
                break;

            case "remove":
                if (args.length != 3) {
                    sender.sendMessage("§c✖ Uso correto: /limite remove <jogador> <quantidade>");
                    return true;
                }
                handleRemoveCommand(sender, args[1], args[2]);
                break;

            case "giveitem":
                if (args.length != 3) {
                    sender.sendMessage("§c✖ Uso correto: /limite giveitem <jogador> <quantidade>");
                    return true;
                }
                handleGiveItemCommand(sender, args[1], args[2]);
                break;

            default:
                // Verificar limite de um jogador
                if (args.length == 1) {
                    handleCheckCommand(sender, args[0]);
                } else {
                    sendHelpMessage(sender);
                }
                break;
        }

        return true;
    }

    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage("§8§l[§6§lLIMITE§8§l] §f§lComandos Disponíveis:");
        sender.sendMessage("§e/limite <jogador> §7- Ver limite de um jogador");
        sender.sendMessage("§e/limite set <jogador> <quantidade> §7- Definir limite");
        sender.sendMessage("§e/limite add <jogador> <quantidade> §7- Adicionar ao limite");
        sender.sendMessage("§e/limite remove <jogador> <quantidade> §7- Remover do limite");
        sender.sendMessage("§e/limite giveitem <jogador> <quantidade> §7- Dar item de limite");
        sender.sendMessage("§e/limite help §7- Mostrar esta ajuda");
    }

    private void handleCheckCommand(CommandSender sender, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage("§c✖ Jogador não encontrado!");
            return;
        }

        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(target);
        sender.sendMessage("§a✓ Limite de §b" + target.getName() + "§a: §f" + playerData.getPurchaseLimit());
        sender.sendMessage("§a✓ Compras realizadas: §f" + playerData.getPurchases());
        sender.sendMessage("§a✓ Limite restante: §f" + (playerData.getPurchaseLimit() - playerData.getPurchases()));
    }

    private void handleSetCommand(CommandSender sender, String playerName, String amountStr) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage("§c✖ Jogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(amountStr);
            if (amount < 0) {
                sender.sendMessage("§c✖ A quantidade deve ser positiva!");
                return;
            }

            PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(target);
            playerData.setPurchaseLimit(amount);

            plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                plugin.getDatabaseManager().savePlayerData(playerData);
            });

            sender.sendMessage("§a✓ Limite de §b" + target.getName() + "§a definido para §f" + amount);
            target.sendMessage("§a✓ Seu limite de compra foi definido para §f" + amount + "§a geradores!");

        } catch (NumberFormatException e) {
            sender.sendMessage("§c✖ Quantidade inválida!");
        }
    }

    private void handleAddCommand(CommandSender sender, String playerName, String amountStr) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage("§c✖ Jogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(amountStr);
            if (amount <= 0) {
                sender.sendMessage("§c✖ A quantidade deve ser positiva!");
                return;
            }

            PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(target);
            int newLimit = playerData.getPurchaseLimit() + amount;
            playerData.setPurchaseLimit(newLimit);

            plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                plugin.getDatabaseManager().savePlayerData(playerData);
            });

            sender.sendMessage("§a✓ Adicionado §f" + amount + "§a ao limite de §b" + target.getName()
                    + "§a. Novo limite: §f" + newLimit);
            target.sendMessage(
                    "§a✓ Seu limite de compra foi aumentado em §f" + amount + "§a! Novo limite: §f" + newLimit);

        } catch (NumberFormatException e) {
            sender.sendMessage("§c✖ Quantidade inválida!");
        }
    }

    private void handleRemoveCommand(CommandSender sender, String playerName, String amountStr) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage("§c✖ Jogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(amountStr);
            if (amount <= 0) {
                sender.sendMessage("§c✖ A quantidade deve ser positiva!");
                return;
            }

            PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(target);
            int newLimit = Math.max(0, playerData.getPurchaseLimit() - amount);
            playerData.setPurchaseLimit(newLimit);

            plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                plugin.getDatabaseManager().savePlayerData(playerData);
            });

            sender.sendMessage("§a✓ Removido §f" + amount + "§a do limite de §b" + target.getName()
                    + "§a. Novo limite: §f" + newLimit);
            target.sendMessage(
                    "§c✖ Seu limite de compra foi reduzido em §f" + amount + "§c! Novo limite: §f" + newLimit);

        } catch (NumberFormatException e) {
            sender.sendMessage("§c✖ Quantidade inválida!");
        }
    }

    private void handleGiveItemCommand(CommandSender sender, String playerName, String amountStr) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage("§c✖ Jogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(amountStr);
            if (amount <= 0) {
                sender.sendMessage("§c✖ A quantidade deve ser positiva!");
                return;
            }

            // Criar item de limite
            ItemStack limitItem = createLimitItem(amount);

            // Dar item ao jogador
            if (target.getInventory().firstEmpty() == -1) {
                target.getWorld().dropItem(target.getLocation(), limitItem);
                target.sendMessage("§e⚠ Seu inventário está cheio! O item foi dropado no chão.");
            } else {
                target.getInventory().addItem(limitItem);
            }

            sender.sendMessage("§a✓ Item de limite (+" + amount + ") dado para §b" + target.getName());
            target.sendMessage("§a✓ Você recebeu um item de limite! Clique com botão direito para usar.");

        } catch (NumberFormatException e) {
            sender.sendMessage("§c✖ Quantidade inválida!");
        }
    }

    private ItemStack createLimitItem(int amount) {
        String name = plugin.getConfigManager().getLimitItemName().replace("{quantidade}", String.valueOf(amount));

        String[] loreArray = plugin.getConfigManager().getLimitItemLore().toArray(new String[0]);
        for (int i = 0; i < loreArray.length; i++) {
            loreArray[i] = loreArray[i].replace("{quantidade}", String.valueOf(amount));
        }

        ItemStack item = ItemUtils.createItem(
                "PAPER",
                name,
                Arrays.asList(loreArray));

        if (plugin.getConfigManager().isLimitItemGlow()) {
            item = ItemUtils.addGlow(item);
        }

        return item;
    }
}
