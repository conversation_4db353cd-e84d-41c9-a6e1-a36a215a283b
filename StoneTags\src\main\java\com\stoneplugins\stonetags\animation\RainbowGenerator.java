package com.stoneplugins.stonetags.animation;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * Gerador de animações RGB Rainbow estilo Lunar Client
 * Funcionalidade Premium - Tags Animadas RGB
 */
public class RainbowGenerator {

    /**
     * Gera frames de animação RGB rainbow para um texto
     * 
     * @param text        Texto base (ex: "STAFF")
     * @param totalFrames Número total de frames da animação
     * @param saturation  Saturação da cor (0.0 - 1.0)
     * @param brightness  Brilho da cor (0.0 - 1.0)
     * @return Lista de frames coloridos
     */
    public static List<String> generateRainbowFrames(String text, int totalFrames, float saturation, float brightness) {
        List<String> frames = new ArrayList<>();

        for (int frame = 0; frame < totalFrames; frame++) {
            StringBuilder coloredText = new StringBuilder();

            for (int i = 0; i < text.length(); i++) {
                char character = text.charAt(i);

                // Calcular hue baseado na posição do caractere e frame atual
                float hue = ((float) (frame * 10 + i * 20) % 360) / 360.0f;

                // Converter HSB para RGB
                Color color = Color.getHSBColor(hue, saturation, brightness);

                // Converter RGB para código de cor Minecraft
                String minecraftColor = rgbToMinecraftColor(color);

                coloredText.append(minecraftColor).append(character);
            }

            frames.add(coloredText.toString());
        }

        return frames;
    }

    /**
     * Gera frames de animação RGB rainbow para uma tag completa
     * 
     * @param prefix      Prefix da tag (ex: "[", "&8[")
     * @param text        Texto da tag (ex: "STAFF")
     * @param suffix      Suffix da tag (ex: "]", "&8]")
     * @param totalFrames Número de frames
     * @return Lista de frames completos
     */
    public static List<String> generateRainbowTag(String prefix, String text, String suffix, int totalFrames) {
        List<String> frames = new ArrayList<>();

        for (int frame = 0; frame < totalFrames; frame++) {
            StringBuilder tagFrame = new StringBuilder();

            // Adicionar prefix
            tagFrame.append(prefix);

            // Adicionar texto com cores rainbow
            for (int i = 0; i < text.length(); i++) {
                char character = text.charAt(i);

                // Calcular hue com offset baseado no frame
                float hue = ((float) (frame * 8 + i * 25) % 360) / 360.0f;

                // Usar saturação e brilho altos para cores vibrantes
                Color color = Color.getHSBColor(hue, 1.0f, 1.0f);
                String minecraftColor = rgbToMinecraftColor(color);

                tagFrame.append(minecraftColor).append(character);
            }

            // Adicionar suffix
            tagFrame.append(suffix);

            frames.add(tagFrame.toString());
        }

        return frames;
    }

    /**
     * Converte cor RGB para código de cor do Minecraft
     * Usa as cores mais próximas disponíveis no Minecraft
     */
    private static String rgbToMinecraftColor(Color color) {
        int red = color.getRed();
        int green = color.getGreen();
        int blue = color.getBlue();

        // Encontrar o componente dominante
        int max = Math.max(red, Math.max(green, blue));

        // Mapear baseado no componente dominante e secundário
        if (max == red) {
            if (green > blue && green > 100)
                return "&6"; // Laranja
            if (blue > green && blue > 100)
                return "&d"; // Rosa
            return "&c"; // Vermelho puro
        } else if (max == green) {
            if (red > blue && red > 100)
                return "&e"; // Amarelo
            if (blue > red && blue > 100)
                return "&b"; // Aqua
            return "&a"; // Verde puro
        } else { // max == blue
            if (red > green && red > 100)
                return "&5"; // Roxo
            if (green > red && green > 100)
                return "&9"; // Azul claro
            return "&1"; // Azul escuro
        }
    }

    /**
     * Gera animação de gradiente entre duas cores
     */
    public static List<String> generateGradientFrames(String text, String startColor, String endColor,
            int totalFrames) {
        List<String> frames = new ArrayList<>();

        for (int frame = 0; frame < totalFrames; frame++) {
            StringBuilder gradientText = new StringBuilder();

            for (int i = 0; i < text.length(); i++) {
                char character = text.charAt(i);

                // Calcular progresso do gradiente
                float progress = (float) (frame + i) / (totalFrames + text.length());
                progress = progress % 1.0f; // Manter entre 0-1

                // Interpolar entre cores
                String interpolatedColor = interpolateColors(startColor, endColor, progress);

                gradientText.append(interpolatedColor).append(character);
            }

            frames.add(gradientText.toString());
        }

        return frames;
    }

    /**
     * Interpola entre duas cores do Minecraft
     */
    private static String interpolateColors(String color1, String color2, float progress) {
        // Simplificado - alterna entre as duas cores baseado no progresso
        if (progress < 0.5f) {
            return color1;
        } else {
            return color2;
        }
    }

    /**
     * Gera frames pré-definidos para efeitos especiais
     */
    public static List<String> generateSpecialEffect(String text, String effectType) {
        List<String> frames = new ArrayList<>();

        switch (effectType.toLowerCase()) {
            case "rainbow":
                return generateRainbowFrames(text, 20, 1.0f, 1.0f);

            case "fire":
                frames.add("&c" + text);
                frames.add("&6" + text);
                frames.add("&e" + text);
                frames.add("&6" + text);
                break;

            case "water":
                frames.add("&9" + text);
                frames.add("&b" + text);
                frames.add("&3" + text);
                frames.add("&b" + text);
                break;

            case "nature":
                frames.add("&a" + text);
                frames.add("&2" + text);
                frames.add("&a" + text);
                frames.add("&e" + text);
                break;

            default:
                frames.add("&f" + text);
        }

        return frames;
    }
}
