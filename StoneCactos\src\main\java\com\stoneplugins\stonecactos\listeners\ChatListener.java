package com.stoneplugins.stonecactos.listeners;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.Set;

public class ChatListener implements Listener {

    private final StoneCactos plugin;

    public ChatListener(StoneCactos plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        String message = event.getMessage();

        // Verificar se o jogador está esperando input de amigo
        if (plugin.getMenuManager().isWaitingForFriendInput(player.getUniqueId())) {
            event.setCancelled(true); // Cancelar a mensagem no chat

            // Processar a mensagem em uma thread síncrona
            Bukkit.getScheduler().runTask(plugin, () -> {
                handleFriendInput(player, message);
            });
        }
    }

    private void handleFriendInput(Player player, String message) {
        // Remover o estado de espera
        plugin.getMenuManager().setWaitingForFriendInput(player.getUniqueId(), false);

        // Verificar se o jogador cancelou
        if (message.equalsIgnoreCase("cancelar")) {
            player.sendMessage("§c[StoneCactos] §fOperação cancelada.");
            return;
        }

        // Verificar se o jogador tem geradores
        Set<CactusGenerator> generators = plugin.getGeneratorManager().getPlayerGenerators(player.getUniqueId());
        if (generators.isEmpty()) {
            player.sendMessage("§c[StoneCactos] §fVocê não possui geradores!");
            return;
        }

        // Pegar o primeiro gerador
        CactusGenerator generator = generators.iterator().next();

        // Verificar se é o dono do gerador
        if (!generator.isOwner(player)) {
            player.sendMessage("§c[StoneCactos] §fVocê não é o dono deste gerador!");
            return;
        }

        // Buscar o jogador pelo nome
        Player targetPlayer = Bukkit.getPlayer(message);
        if (targetPlayer == null) {
            // Tentar buscar por jogador offline
            player.sendMessage("§c[StoneCactos] §fJogador não encontrado ou não está online!");
            return;
        }

        // Verificar se não está tentando adicionar a si mesmo
        if (targetPlayer.getUniqueId().equals(player.getUniqueId())) {
            player.sendMessage("§c[StoneCactos] §fVocê não pode adicionar a si mesmo como amigo!");
            return;
        }

        // Verificar se já é amigo
        if (generator.getFriends().containsKey(targetPlayer.getUniqueId())) {
            player.sendMessage("§c[StoneCactos] §fEste jogador já é seu amigo!");
            return;
        }

        // Adicionar como amigo com permissões padrão
        boolean success = plugin.getFriendsManager().addFriend(generator, targetPlayer.getUniqueId(), 
                false, false, false); // Sem permissões por padrão

        if (success) {
            player.sendMessage("§a[StoneCactos] §fJogador §e" + targetPlayer.getName() + " §fadicionado como amigo!");
            targetPlayer.sendMessage("§a[StoneCactos] §fVocê foi adicionado como amigo do gerador de §e" + player.getName() + "§f!");
            
            // Salvar o gerador
            plugin.getGeneratorManager().saveGenerator(generator);
        } else {
            player.sendMessage("§c[StoneCactos] §fErro ao adicionar amigo!");
        }
    }
}