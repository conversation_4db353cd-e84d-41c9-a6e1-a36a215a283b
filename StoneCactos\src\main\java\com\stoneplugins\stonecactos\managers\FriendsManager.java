package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.data.FriendPermissions;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.Set;
import java.util.UUID;

public class FriendsManager {
    
    private final StoneCactos plugin;
    
    public FriendsManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Adiciona um amigo a um gerador
     */
    public boolean addFriend(CactusGenerator generator, UUID friendUuid, boolean canAddTowers, 
                           boolean canRemoveTowers, boolean canSellCactus) {
        // Verificar se já é amigo
        if (generator.getFriends().containsKey(friendUuid)) {
            return false;
        }
        
        // Adicionar amigo
        generator.addFriend(friendUuid, canAddTowers, canRemoveTowers, canSellCactus);
        
        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);
        
        return true;
    }
    
    /**
     * Remove um amigo de um gerador
     */
    public boolean removeFriend(CactusGenerator generator, UUID friendUuid) {
        // Verificar se é amigo
        if (!generator.getFriends().containsKey(friendUuid)) {
            return false;
        }
        
        // Remover amigo
        generator.removeFriend(friendUuid);
        
        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);
        
        return true;
    }
    
    /**
     * Atualiza as permissões de um amigo
     */
    public boolean updateFriendPermissions(CactusGenerator generator, UUID friendUuid, 
                                         boolean canAddTowers, boolean canRemoveTowers, boolean canSellCactus) {
        // Verificar se é amigo
        if (!generator.getFriends().containsKey(friendUuid)) {
            return false;
        }
        
        // Atualizar permissões
        FriendPermissions permissions = generator.getFriends().get(friendUuid);
        permissions.setCanAddTowers(canAddTowers);
        permissions.setCanRemoveTowers(canRemoveTowers);
        permissions.setCanSellCactus(canSellCactus);
        
        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);
        
        return true;
    }
    
    /**
     * Alterna uma permissão específica de um amigo
     */
    public boolean toggleFriendPermission(CactusGenerator generator, UUID friendUuid, String permission) {
        // Verificar se é amigo
        if (!generator.getFriends().containsKey(friendUuid)) {
            return false;
        }
        
        FriendPermissions permissions = generator.getFriends().get(friendUuid);
        
        switch (permission.toLowerCase()) {
            case "add_towers":
                permissions.setCanAddTowers(!permissions.canAddTowers());
                break;
            case "remove_towers":
                permissions.setCanRemoveTowers(!permissions.canRemoveTowers());
                break;
            case "sell_cactus":
                permissions.setCanSellCactus(!permissions.canSellCactus());
                break;
            default:
                return false;
        }
        
        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);
        
        return true;
    }
    
    /**
     * Verifica se um jogador tem uma permissão específica
     */
    public boolean hasPermission(CactusGenerator generator, UUID playerUuid, String permission) {
        return generator.hasPermission(playerUuid, permission);
    }
    
    /**
     * Obtém todos os amigos de um gerador
     */
    public Set<UUID> getFriends(CactusGenerator generator) {
        return generator.getFriends().keySet();
    }
    
    /**
     * Obtém as permissões de um amigo
     */
    public FriendPermissions getFriendPermissions(CactusGenerator generator, UUID friendUuid) {
        return generator.getFriends().get(friendUuid);
    }
    
    /**
     * Verifica se um jogador é amigo
     */
    public boolean isFriend(CactusGenerator generator, UUID playerUuid) {
        return generator.getFriends().containsKey(playerUuid);
    }
    
    /**
     * Conta quantos amigos um gerador tem
     */
    public int getFriendCount(CactusGenerator generator) {
        return generator.getFriends().size();
    }
    
    /**
     * Obtém o nome de um jogador por UUID
     */
    public String getPlayerName(UUID playerUuid) {
        Player player = Bukkit.getPlayer(playerUuid);
        if (player != null) {
            return player.getName();
        }
        
        // Se o jogador não está online, tentar obter do cache do Bukkit
        return Bukkit.getOfflinePlayer(playerUuid).getName();
    }
    
    /**
     * Obtém informações formatadas de um amigo
     */
    public String getFriendInfo(CactusGenerator generator, UUID friendUuid) {
        if (!isFriend(generator, friendUuid)) {
            return "§cNão é amigo";
        }
        
        String name = getPlayerName(friendUuid);
        FriendPermissions perms = getFriendPermissions(generator, friendUuid);
        
        StringBuilder info = new StringBuilder();
        info.append("§e").append(name).append("\n");
        info.append("§fAdicionar Torres: ").append(perms.canAddTowers() ? "§aPermitido" : "§cNegado").append("\n");
        info.append("§fRemover Torres: ").append(perms.canRemoveTowers() ? "§aPermitido" : "§cNegado").append("\n");
        info.append("§fVender Cactos: ").append(perms.canSellCactus() ? "§aPermitido" : "§cNegado");
        
        return info.toString();
    }
    
    /**
     * Obtém uma lista formatada de todos os amigos
     */
    public String getAllFriendsInfo(CactusGenerator generator) {
        Set<UUID> friends = getFriends(generator);
        
        if (friends.isEmpty()) {
            return "§7Nenhum amigo adicionado";
        }
        
        StringBuilder info = new StringBuilder();
        info.append("§eAmigos (").append(friends.size()).append("):\n");
        
        for (UUID friendUuid : friends) {
            String name = getPlayerName(friendUuid);
            info.append("§f- ").append(name).append("\n");
        }
        
        return info.toString().trim();
    }
    
    /**
     * Remove todos os amigos de um gerador
     */
    public void removeAllFriends(CactusGenerator generator) {
        generator.getFriends().clear();
        
        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);
    }
    
    /**
     * Verifica se um jogador pode acessar um gerador
     */
    public boolean canAccess(CactusGenerator generator, Player player) {
        UUID playerUuid = player.getUniqueId();
        
        // Verificar se é o dono
        if (generator.getOwnerUuid().equals(playerUuid)) {
            return true;
        }
        
        // Verificar se é amigo
        return isFriend(generator, playerUuid);
    }
}
