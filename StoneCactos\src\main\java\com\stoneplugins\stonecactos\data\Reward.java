package com.stoneplugins.stonecactos.data;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.List;

public class Reward {
    
    private int id;
    private String name;
    private List<String> lore;
    private String item;
    private int necessaryCactus;
    private List<String> rewardCommands;
    
    public Reward(int id, String name, List<String> lore, String item, 
                  int necessaryCactus, List<String> rewardCommands) {
        this.id = id;
        this.name = name;
        this.lore = lore;
        this.item = item;
        this.necessaryCactus = necessaryCactus;
        this.rewardCommands = rewardCommands;
    }
    
    // Getters e Setters
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public List<String> getLore() {
        return lore;
    }
    
    public void setLore(List<String> lore) {
        this.lore = lore;
    }
    
    public String getItem() {
        return item;
    }
    
    public void setItem(String item) {
        this.item = item;
    }
    
    public int getNecessaryCactus() {
        return necessaryCactus;
    }
    
    public void setNecessaryCactus(int necessaryCactus) {
        this.necessaryCactus = necessaryCactus;
    }
    
    public List<String> getRewardCommands() {
        return rewardCommands;
    }
    
    public void setRewardCommands(List<String> rewardCommands) {
        this.rewardCommands = rewardCommands;
    }
    
    // Métodos utilitários
    public ItemStack createItemStack(boolean collected, int playerCactus) {
        ItemStack itemStack;
        
        // Verificar se é uma textura de head ou material
        if (item.length() == 64 && !item.contains(":")) {
            // É uma textura de head
            itemStack = createSkullWithTexture(item);
        } else {
            // É um material
            String[] parts = item.split(":");
            Material material = Material.valueOf(parts[0]);
            short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;
            
            itemStack = new ItemStack(material, 1, data);
        }
        
        // Aplicar nome e lore
        ItemMeta meta = itemStack.getItemMeta();
        if (meta != null) {
            meta.setDisplayName(name.replace("&", "§"));
            
            if (lore != null && !lore.isEmpty()) {
                List<String> formattedLore = new java.util.ArrayList<>();
                for (String line : lore) {
                    String status;
                    if (collected) {
                        status = "§aRecompensa já coletada.";
                    } else if (playerCactus >= necessaryCactus) {
                        status = "§eClique para desbloquear";
                    } else {
                        status = "§cVocê precisa de " + (necessaryCactus - playerCactus) + " cactos a mais";
                    }
                    
                    formattedLore.add(line
                        .replace("&", "§")
                        .replace("%cactus%", String.valueOf(necessaryCactus))
                        .replace("%status%", status)
                    );
                }
                meta.setLore(formattedLore);
            }
            
            itemStack.setItemMeta(meta);
        }
        
        return itemStack;
    }
    
    private ItemStack createSkullWithTexture(String texture) {
        // Para Minecraft 1.8, usar SKULL_ITEM
        ItemStack skull = new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (short) 3);
        
        // Aqui você precisaria implementar a lógica para aplicar a textura
        // Isso requer reflection ou uma biblioteca como SkullCreator
        // Por simplicidade, retornamos um skull básico
        
        return skull;
    }
    
    public boolean canCollect(int playerCactus) {
        return playerCactus >= necessaryCactus;
    }
    
    public String getFormattedRequirement() {
        if (necessaryCactus >= 1000000) {
            return String.format("%.1fM cactos", necessaryCactus / 1000000.0);
        } else if (necessaryCactus >= 1000) {
            return String.format("%.1fK cactos", necessaryCactus / 1000.0);
        } else {
            return necessaryCactus + " cactos";
        }
    }
    
    @Override
    public String toString() {
        return "Reward{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", necessaryCactus=" + necessaryCactus +
                ", commands=" + rewardCommands.size() +
                '}';
    }
}
