package com.stoneplugins.spawnershop.listeners;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.gui.MainMenu;
import com.stoneplugins.spawnershop.gui.RankingMenu;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import com.stoneplugins.spawnershop.utils.ItemUtils;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.inventory.InventoryMoveItemEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.List;

public class InventoryListener implements Listener {

    private final SpawnerShop plugin;
    private final Map<UUID, Boolean> rankingOrderState = new HashMap<>();
    private final Map<UUID, MainMenu> activeMenus = new HashMap<>();

    public InventoryListener(SpawnerShop plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();

        // Verificar se é um menu do plugin
        String mainMenuTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuName("main"));
        String rankingMenuTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuName("ranking"));

        if (title.equals(mainMenuTitle)) {
            event.setCancelled(true);
            handleMainMenuClick(player, event.getSlot(), event.getCurrentItem(), event.getClick());
        } else if (title.equals(rankingMenuTitle)) {
            event.setCancelled(true);
            handleRankingMenuClick(player, event.getSlot());
        }

        // Verificar se clicou no item de limite
        if (event.getCurrentItem() != null && isLimitItem(event.getCurrentItem())) {
            event.setCancelled(true);
            handleLimitItemClick(player, event.getCurrentItem());
        }
    }

    private void handleMainMenuClick(Player player, int slot, ItemStack item, ClickType clickType) {
        if (item == null)
            return;

        // Criar uma nova instância do MainMenu para lidar com o clique
        MainMenu mainMenu = new MainMenu(plugin, player);
        mainMenu.handleClick(slot, item, clickType);
    }

    private void handleRankingMenuClick(Player player, int slot) {
        if (slot == plugin.getConfigManager().getRankingBackArrow()) {
            player.closeInventory();
            new MainMenu(plugin, player).open();
        } else if (slot == 4) { // Botão de ordenação
            player.closeInventory();
            // Alternar estado da ordenação usando cache
            boolean currentOrderByLimit = rankingOrderState.getOrDefault(player.getUniqueId(), false);
            boolean newOrderByLimit = !currentOrderByLimit;
            rankingOrderState.put(player.getUniqueId(), newOrderByLimit);
            new RankingMenu(plugin, player, newOrderByLimit).open();
        }
    }

    private boolean isLimitItem(ItemStack item) {
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }

        String itemName = plugin.getMessageUtils().stripColors(item.getItemMeta().getDisplayName());
        String configName = plugin.getMessageUtils().stripColors(
                plugin.getMessageUtils().colorize(plugin.getConfigManager().getLimitItemName()));

        return itemName.equals(configName);
    }

    private void handleLimitItemClick(Player player, ItemStack item) {
        if (!item.hasItemMeta() || !item.getItemMeta().hasLore()) {
            return;
        }

        List<String> lore = item.getItemMeta().getLore();

        // Extrair quantidade do lore
        int quantity = 0;
        for (String line : lore) {
            String cleanLine = plugin.getMessageUtils().stripColors(line).trim();
            if (cleanLine.startsWith("Quantidade:")) {
                try {
                    String quantityStr = cleanLine.replace("Quantidade:", "").trim();
                    quantity = Integer.parseInt(quantityStr);
                    break;
                } catch (NumberFormatException e) {
                    // Ignorar erro
                }
            }
        }

        if (quantity > 0) {
            // Aumentar limite do jogador
            plugin.getPlayerDataManager().increasePurchaseLimit(player, quantity);

            // Remover item do inventário
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                player.getInventory().removeItem(item);
            }

            // Enviar mensagem
            String message = plugin.getConfigManager().getMessage("ativoulimite")
                    .replace("{limite}", String.valueOf(quantity));
            player.sendMessage(plugin.getMessageUtils().colorize(message));

            player.updateInventory();
        }
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getPlayer();

        // Limpar menu ativo e timer
        MainMenu menu = activeMenus.remove(player.getUniqueId());
        if (menu != null) {
            menu.cleanup();
        }

        // Limpar dados temporários se necessário
        PlayerChatListener chatListener = plugin.getPlayerChatListener();
        if (chatListener != null && chatListener.isWaitingForQuantity(player)) {
            chatListener.removeWaitingForQuantity(player);
            player.sendMessage("§cAção cancelada!");
        }
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Limpar dados do jogador
        plugin.getPlayerDataManager().unloadPlayerData(player.getUniqueId());
        plugin.getPlayerDataManager().removePendingPurchase(player);

        // Limpar dados temporários
        PlayerChatListener chatListener = plugin.getPlayerChatListener();
        if (chatListener != null) {
            chatListener.removeWaitingForQuantity(player);
        }
    }

    public void registerMenu(Player player, MainMenu menu) {
        // Limpar menu anterior se existir
        MainMenu oldMenu = activeMenus.get(player.getUniqueId());
        if (oldMenu != null) {
            oldMenu.cleanup();
        }

        activeMenus.put(player.getUniqueId(), menu);
    }

}
