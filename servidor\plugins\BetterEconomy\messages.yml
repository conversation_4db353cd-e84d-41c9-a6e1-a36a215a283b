prefix: '&f[&6BetterEconomy&f] &r'
give-success: '&aSuccessfully give {balance} to {name}'
give-fail: '&cFailed to give {balance} to {name}'
cannot-do: '&c<PERSON><PERSON>ot do this action'
set-success: '&aSuccessfully set {balance} to {name}'
receive: '&a<PERSON><PERSON> received {balance} from {name}'
take-fail: '&cFailed to take {balance} from {name}'
balance-output: '&eBalance: &f{balance}'
success: '&aSuccess'
set-fail: '&cFailed to set {balance} to {name}'
invalid-amount: '&cInvalid Amount'
player-only: '&cThis command is for player only'
take-success: '&aSuccessfully take {balance} from {name}'
balance-top-output: '&f#{place} &e{name}: &f{balance}'
empty-balance-top: '&eThe balance top is empty'
player-not-found: '&cThe player is not found'
empty-player-selector: '&cNo player is selected'
