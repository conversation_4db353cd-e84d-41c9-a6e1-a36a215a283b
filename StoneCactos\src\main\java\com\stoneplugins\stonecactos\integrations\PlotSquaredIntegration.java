package com.stoneplugins.stonecactos.integrations;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;

/**
 * Integração simplificada com PlotSquared
 */
public class PlotSquaredIntegration {
    
    private final StoneCactos plugin;
    private boolean enabled = false;
    
    public PlotSquaredIntegration(StoneCactos plugin) {
        this.plugin = plugin;
        initialize();
    }
    
    /**
     * Inicializa a integração com PlotSquared
     */
    private void initialize() {
        if (Bukkit.getPluginManager().getPlugin("PlotSquared") == null) {
            plugin.getLogger().info("PlotSquared não encontrado. Integração desabilitada.");
            enabled = false;
            return;
        }
        
        try {
            // Verificar se PlotSquared está disponível
            enabled = true;
            plugin.getLogger().info("PlotSquared encontrado! Integração habilitada.");
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao inicializar PlotSquared: " + e.getMessage());
            enabled = false;
        }
    }
    
    /**
     * Verifica se a integração está habilitada
     */
    public boolean isEnabled() {
        return enabled;
    }
    
    /**
     * Verifica se uma localização está em um mundo de plots
     */
    public boolean isPlotWorld(Location location) {
        if (!enabled) {
            return true; // Se PlotSquared não estiver disponível, assumir que é permitido
        }
        
        try {
            // Verificação simplificada - assumir que mundos com "plot" no nome são mundos de plots
            String worldName = location.getWorld().getName().toLowerCase();
            return worldName.contains("plot");
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao verificar mundo de plots: " + e.getMessage());
            return true; // Permitir por padrão
        }
    }
    
    /**
     * Verifica se um jogador pode construir em uma localização
     */
    public boolean canBuild(Player player, Location location) {
        if (!enabled) {
            return true; // Se PlotSquared não estiver disponível, permitir por padrão
        }
        
        try {
            // Verificação simplificada - sempre permitir por enquanto
            // Em uma implementação completa, aqui seria feita a verificação real do PlotSquared
            return true;
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao verificar permissão de construção: " + e.getMessage());
            return true; // Permitir por padrão em caso de erro
        }
    }
    
    /**
     * Verifica se um jogador é dono de um plot
     */
    public boolean isPlotOwner(Player player, Location location) {
        if (!enabled) {
            return true; // Se não há integração, permite por padrão
        }
        
        // Verificação simplificada - sempre permitir por enquanto
        return true;
    }
    
    /**
     * Verifica se um jogador tem permissão em um plot (dono ou membro)
     */
    public boolean hasPlotPermission(Player player, Location location) {
        if (!enabled) {
            return true; // Se não há integração, permite por padrão
        }
        
        // Verificação simplificada - sempre permitir por enquanto
        return true;
    }
    
    /**
     * Verifica se há um jogador no plot do gerador
     */
    public boolean isPlayerInPlot(Location generatorLocation, java.util.UUID ownerUuid) {
        // Verificar se o dono está online e no mesmo mundo
        Player owner = plugin.getServer().getPlayer(ownerUuid);
        if (owner != null && owner.isOnline()) {
            Location playerLocation = owner.getLocation();
            
            // Verificar se estão no mesmo mundo
            if (playerLocation.getWorld().equals(generatorLocation.getWorld())) {
                // Verificar distância (considerando um plot padrão de 100x100)
                double distance = playerLocation.distance(generatorLocation);
                if (distance <= 100) {
                    return true; // Dono está no plot
                }
            }
        }

        // Verificar se algum amigo está no plot
        com.stoneplugins.stonecactos.data.CactusGenerator generator = plugin.getGeneratorManager().getGenerator(generatorLocation);
        if (generator != null) {
            java.util.Set<java.util.UUID> friends = plugin.getFriendsManager().getFriends(generator);
            
            for (java.util.UUID friendUuid : friends) {
                Player friend = plugin.getServer().getPlayer(friendUuid);
                if (friend != null && friend.isOnline()) {
                    Location friendLocation = friend.getLocation();
                    
                    // Verificar se estão no mesmo mundo
                    if (friendLocation.getWorld().equals(generatorLocation.getWorld())) {
                        // Verificar distância
                        double distance = friendLocation.distance(generatorLocation);
                        if (distance <= 100) {
                            return true; // Amigo está no plot
                        }
                    }
                }
            }
        }
        
        return false;
    }

    /**
     * Obtém informações sobre a integração
     */
    public String getIntegrationInfo() {
        if (enabled) {
            return "§aPlotSquared: §fIntegração ativa";
        } else {
            return "§cPlotSquared: §fIntegração inativa";
        }
    }
}
