package com.atlasplugins.pescaria.integrations;

import com.atlasplugins.pescaria.Pescaria;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class SimpleScoreIntegration {

    private final Pescaria plugin;
    private boolean hasSimpleScore = false;

    public SimpleScoreIntegration(Pescaria plugin) {
        this.plugin = plugin;
        setupSimpleScore();
    }

    private void setupSimpleScore() {
        if (Bukkit.getPluginManager().getPlugin("SimpleScore") != null) {
            hasSimpleScore = true;
            plugin.getLogger().info("Integração com SimpleScore ativada!");

            // Verificar se PlaceholderAPI está disponível
            if (Bukkit.getPluginManager().getPlugin("PlaceholderAPI") != null) {
                plugin.getLogger().info("PlaceholderAPI detectado - placeholders da Pescaria disponíveis!");
                plugin.getLogger().info("Use %pescaria_peixes%, %pescaria_vara_nivel%, etc. nos scoreboards!");
            }
        }
    }

    public boolean hasSimpleScore() {
        return hasSimpleScore;
    }

    public void forceScoreboard(Player player, String scoreboardName) {
        if (!hasSimpleScore)
            return;

        try {
            // Executar comando para forçar scoreboard
            String command = "sb force " + player.getName() + " " + scoreboardName;
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao forçar scoreboard: " + e.getMessage());
        }
    }

    public void toggleScoreboard(Player player, boolean enabled) {
        if (!hasSimpleScore)
            return;

        try {
            // Executar comando para toggle scoreboard
            String command = "sb toggle " + player.getName() + " " + (enabled ? "on" : "off");
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao toggle scoreboard: " + e.getMessage());
        }
    }

}
