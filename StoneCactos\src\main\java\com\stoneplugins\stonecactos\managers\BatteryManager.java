package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.BatteryItem;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class BatteryManager {
    
    private final StoneCactos plugin;
    private final Map<String, BatteryItem> batteryItems;
    
    public BatteryManager(StoneCactos plugin) {
        this.plugin = plugin;
        this.batteryItems = new HashMap<>();
        loadBatteryItems();
    }
    
    private void loadBatteryItems() {
        // Carregar itens de bateria da configuração
        if (plugin.getConfigManager().getConfig().contains("BatteryItems")) {
            for (String key : plugin.getConfigManager().getConfig().getConfigurationSection("BatteryItems").getKeys(false)) {
                String path = "BatteryItems." + key;
                
                String materialStr = plugin.getConfigManager().getConfig().getString(path + ".material", "COAL:0");
                String name = plugin.getConfigManager().getConfig().getString(path + ".name", "&eBateria");
                double batteryValue = plugin.getConfigManager().getConfig().getDouble(path + ".batteryValue", 10.0);
                List<String> loreList = plugin.getConfigManager().getConfig().getStringList(path + ".lore");
                
                // Parse material
                Material material;
                short data = 0;
                if (materialStr.contains(":")) {
                    String[] parts = materialStr.split(":");
                    material = Material.valueOf(parts[0]);
                    data = Short.parseShort(parts[1]);
                } else {
                    material = Material.valueOf(materialStr);
                }
                
                String[] lore = loreList.toArray(new String[0]);
                
                BatteryItem batteryItem = new BatteryItem(key, material, data, name, batteryValue, lore);
                batteryItems.put(key, batteryItem);
            }
        } else {
            // Criar itens padrão se não existirem na config
            createDefaultBatteryItems();
        }
        
        plugin.getLogger().info("Carregados " + batteryItems.size() + " tipos de bateria/combustível!");
    }
    
    private void createDefaultBatteryItems() {
        // Carvão - 10% de bateria
        BatteryItem coal = new BatteryItem("COAL", Material.COAL, (short) 0, "§8Carvão", 10.0, 
            new String[]{"§7Combustível básico", "§f+10% de bateria", "§eClique para usar"});
        batteryItems.put("COAL", coal);
        
        // Carvão Vegetal - 15% de bateria
        BatteryItem charcoal = new BatteryItem("CHARCOAL", Material.COAL, (short) 1, "§8Carvão Vegetal", 15.0,
            new String[]{"§7Combustível melhorado", "§f+15% de bateria", "§eClique para usar"});
        batteryItems.put("CHARCOAL", charcoal);
        
        // Blaze Rod - 25% de bateria
        BatteryItem blazeRod = new BatteryItem("BLAZE_ROD", Material.BLAZE_ROD, (short) 0, "§6Vara de Blaze", 25.0,
            new String[]{"§7Combustível avançado", "§f+25% de bateria", "§eClique para usar"});
        batteryItems.put("BLAZE_ROD", blazeRod);
        
        // Lava Bucket - 50% de bateria
        BatteryItem lavaBucket = new BatteryItem("LAVA_BUCKET", Material.LAVA_BUCKET, (short) 0, "§cBalde de Lava", 50.0,
            new String[]{"§7Combustível premium", "§f+50% de bateria", "§eClique para usar"});
        batteryItems.put("LAVA_BUCKET", lavaBucket);
    }
    
    public boolean isBatteryItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) {
            return false;
        }
        
        for (BatteryItem batteryItem : batteryItems.values()) {
            if (item.getType() == batteryItem.getMaterial() && 
                item.getDurability() == batteryItem.getData()) {
                
                // Verificar se tem o nome correto
                String itemName = item.getItemMeta().getDisplayName();
                String batteryName = plugin.getMessageUtils().colorize(batteryItem.getName());
                
                if (itemName != null && itemName.equals(batteryName)) {
                    return true;
                }
                
                // Se não tem nome customizado, verificar apenas material e data
                if (itemName == null || itemName.isEmpty()) {
                    return true;
                }
            }
        }
        
        return false;
    }
    
    public BatteryItem getBatteryItem(ItemStack item) {
        if (!isBatteryItem(item)) {
            return null;
        }
        
        for (BatteryItem batteryItem : batteryItems.values()) {
            if (item.getType() == batteryItem.getMaterial() && 
                item.getDurability() == batteryItem.getData()) {
                return batteryItem;
            }
        }
        
        return null;
    }
    
    public boolean useBattery(Player player, CactusGenerator generator, ItemStack batteryItem) {
        BatteryItem battery = getBatteryItem(batteryItem);
        if (battery == null) {
            return false;
        }
        
        // Verificar se a bateria não está cheia
        if (generator.getBattery() >= 100.0) {
            player.sendMessage("§c[StoneCactos] §fA bateria já está cheia!");
            return false;
        }
        
        // Adicionar bateria
        double newBattery = Math.min(100.0, generator.getBattery() + battery.getBatteryValue());
        generator.setBattery(newBattery);
        
        // Salvar gerador
        plugin.getGeneratorManager().saveGenerator(generator);
        
        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);
        
        // Consumir item
        if (batteryItem.getAmount() > 1) {
            batteryItem.setAmount(batteryItem.getAmount() - 1);
        } else {
            player.getInventory().removeItem(batteryItem);
        }
        
        // Mensagem de sucesso
        player.sendMessage("§a[StoneCactos] §f+" + battery.getBatteryValue() + "% de bateria adicionada!");
        player.sendMessage("§eBateria atual: " + String.format("%.1f%%", generator.getBattery()));
        
        return true;
    }
    
    public ItemStack createBatteryItem(String batteryId) {
        BatteryItem battery = batteryItems.get(batteryId);
        if (battery == null) {
            return null;
        }
        
        ItemStack item = new ItemStack(battery.getMaterial(), 1, battery.getData());
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            meta.setDisplayName(plugin.getMessageUtils().colorize(battery.getName()));
            
            List<String> lore = new ArrayList<>();
            for (String line : battery.getLore()) {
                lore.add(plugin.getMessageUtils().colorize(line));
            }
            meta.setLore(lore);
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    public List<BatteryItem> getAllBatteryItems() {
        return new ArrayList<>(batteryItems.values());
    }
    
    public BatteryItem getBatteryItemById(String id) {
        return batteryItems.get(id);
    }
}