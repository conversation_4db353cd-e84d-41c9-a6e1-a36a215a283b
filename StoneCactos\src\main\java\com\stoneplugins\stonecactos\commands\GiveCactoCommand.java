package com.stoneplugins.stonecactos.commands;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class GiveCactoCommand implements CommandExecutor {

    private final StoneCactos plugin;

    public GiveCactoCommand(StoneCactos plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("stonecactos.admin")) {
            sender.sendMessage(ChatColor.RED + "Você não tem permissão para usar este comando!");
            return true;
        }

        if (args.length < 2) {
            sender.sendMessage(ChatColor.RED + "Uso: /givecacto <player> <amount>");
            return true;
        }

        String playerName = args[0];
        Player target = Bukkit.getPlayer(playerName);

        if (target == null) {
            sender.sendMessage(ChatColor.RED + "Jogador não encontrado!");
            return true;
        }

        int amount;
        try {
            amount = Integer.parseInt(args[1]);
            if (amount <= 0) {
                sender.sendMessage(ChatColor.RED + "A quantidade deve ser maior que 0!");
                return true;
            }
        } catch (NumberFormatException e) {
            sender.sendMessage(ChatColor.RED + "Quantidade inválida!");
            return true;
        }

        ItemStack generator = plugin.getItemManager().createCactusGenerator(target.getName(), 0, 100);
        generator.setAmount(amount);

        if (target.getInventory().firstEmpty() == -1) {
            target.getWorld().dropItem(target.getLocation(), generator);
        } else {
            target.getInventory().addItem(generator);
        }

        target.sendMessage(ChatColor.GREEN + "Você recebeu " + amount + " gerador(es) de cacto!");
        sender.sendMessage(
                ChatColor.GREEN + "Você deu " + amount + " gerador(es) de cacto para " + target.getName() + "!");

        plugin.getLogger().info(sender.getName() + " deu " + amount + " gerador(es) de cacto para " + target.getName());

        return true;
    }
}
