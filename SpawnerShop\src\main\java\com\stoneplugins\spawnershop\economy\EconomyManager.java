package com.stoneplugins.spawnershop.economy;

import com.stoneplugins.spawnershop.SpawnerShop;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.RegisteredServiceProvider;

public class EconomyManager {

    private final SpawnerShop plugin;
    private Economy vaultEconomy;
    private String economyType;

    public EconomyManager(SpawnerShop plugin) {
        this.plugin = plugin;
        this.economyType = plugin.getConfigManager().getEconomyPlugin();
        setupEconomy();
    }

    private void setupEconomy() {
        if (economyType.equalsIgnoreCase("VAULT")) {
            setupVaultEconomy();
        } else {
            plugin.getLogger().info("Usando sistema de economia: " + economyType);
        }
    }

    private void setupVaultEconomy() {
        if (plugin.getServer().getPluginManager().getPlugin("Vault") == null) {
            plugin.getLogger().severe("Vault não encontrado!");
            return;
        }

        RegisteredServiceProvider<Economy> rsp = plugin.getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            plugin.getLogger().severe("Nenhum plugin de economia encontrado!");
            return;
        }

        vaultEconomy = rsp.getProvider();
        plugin.getLogger().info("Vault Economy configurado com sucesso!");
    }

    public double getBalance(Player player) {
        switch (economyType.toUpperCase()) {
            case "VAULT":
                return vaultEconomy != null ? vaultEconomy.getBalance(player) : 0.0;
            case "YPOINTS":
                return getYPointsBalance(player);
            case "PLAYERPOINTS":
                return getPlayerPointsBalance(player);
            case "ATLASECONOMIASECUNDARIA":
                return getAtlasEconomiaBalance(player);
            case "STORMECONOMIASECUNDARIA":
                return getStormEconomiaBalance(player);
            default:
                return 0.0;
        }
    }

    public boolean hasBalance(Player player, double amount) {
        return getBalance(player) >= amount;
    }

    public boolean withdrawBalance(Player player, double amount) {
        if (!hasBalance(player, amount)) {
            return false;
        }

        switch (economyType.toUpperCase()) {
            case "VAULT":
                if (vaultEconomy != null) {
                    return vaultEconomy.withdrawPlayer(player, amount).transactionSuccess();
                }
                return false;
            case "YPOINTS":
                return withdrawYPoints(player, amount);
            case "PLAYERPOINTS":
                return withdrawPlayerPoints(player, amount);
            case "ATLASECONOMIASECUNDARIA":
                return withdrawAtlasEconomia(player, amount);
            case "STORMECONOMIASECUNDARIA":
                return withdrawStormEconomia(player, amount);
            default:
                return false;
        }
    }

    public String formatBalance(double balance) {
        return formatNumber(balance);
    }

    private String formatNumber(double number) {
        // Formatar com letras (K, M, B, T)
        if (number >= 1000000000000.0) {
            return String.format("%.1fT", number / 1000000000000.0);
        } else if (number >= 1000000000.0) {
            return String.format("%.1fB", number / 1000000000.0);
        } else if (number >= 1000000.0) {
            return String.format("%.1fM", number / 1000000.0);
        } else if (number >= 1000.0) {
            return String.format("%.1fK", number / 1000.0);
        } else {
            // Números menores que 1000, mostrar sem decimais se for inteiro
            if (number == (long) number) {
                return String.format("%.0f", number);
            } else {
                return String.format("%.2f", number);
            }
        }
    }

    // Métodos para yPoints
    private double getYPointsBalance(Player player) {
        if (Bukkit.getPluginManager().getPlugin("yPoints") != null) {
            try {
                // Usar reflexão para acessar a API do yPoints
                Class<?> yPointsAPI = Class.forName("me.yic.ypoints.api.yPointsAPI");
                Object instance = yPointsAPI.getMethod("getInstance").invoke(null);
                return (double) yPointsAPI.getMethod("getPoints", Player.class).invoke(instance, player);
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao acessar yPoints: " + e.getMessage());
            }
        }
        return 0.0;
    }

    private boolean withdrawYPoints(Player player, double amount) {
        if (Bukkit.getPluginManager().getPlugin("yPoints") != null) {
            try {
                Class<?> yPointsAPI = Class.forName("me.yic.ypoints.api.yPointsAPI");
                Object instance = yPointsAPI.getMethod("getInstance").invoke(null);
                yPointsAPI.getMethod("removePoints", Player.class, int.class).invoke(instance, player, (int) amount);
                return true;
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao remover yPoints: " + e.getMessage());
            }
        }
        return false;
    }

    // Métodos para PlayerPoints
    private double getPlayerPointsBalance(Player player) {
        if (Bukkit.getPluginManager().getPlugin("PlayerPoints") != null) {
            try {
                Class<?> playerPointsAPI = Class.forName("org.black_ixx.playerpoints.PlayerPointsAPI");
                Object instance = playerPointsAPI.getMethod("getInstance").invoke(null);
                return (double) (int) playerPointsAPI.getMethod("look", java.util.UUID.class).invoke(instance,
                        player.getUniqueId());
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao acessar PlayerPoints: " + e.getMessage());
            }
        }
        return 0.0;
    }

    private boolean withdrawPlayerPoints(Player player, double amount) {
        if (Bukkit.getPluginManager().getPlugin("PlayerPoints") != null) {
            try {
                Class<?> playerPointsAPI = Class.forName("org.black_ixx.playerpoints.PlayerPointsAPI");
                Object instance = playerPointsAPI.getMethod("getInstance").invoke(null);
                return (boolean) playerPointsAPI.getMethod("take", java.util.UUID.class, int.class).invoke(instance,
                        player.getUniqueId(), (int) amount);
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao remover PlayerPoints: " + e.getMessage());
            }
        }
        return false;
    }

    // Métodos para AtlasEconomiaSecundaria
    private double getAtlasEconomiaBalance(Player player) {
        if (Bukkit.getPluginManager().getPlugin("AtlasEconomiaSecundaria") != null) {
            try {
                Class<?> atlasAPI = Class
                        .forName("com.atlasplugins.atlaseconomiasecundaria.api.AtlasEconomiaSecundariaAPI");
                return (double) atlasAPI.getMethod("getCoins", Player.class).invoke(null, player);
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao acessar AtlasEconomiaSecundaria: " + e.getMessage());
            }
        }
        return 0.0;
    }

    private boolean withdrawAtlasEconomia(Player player, double amount) {
        if (Bukkit.getPluginManager().getPlugin("AtlasEconomiaSecundaria") != null) {
            try {
                Class<?> atlasAPI = Class
                        .forName("com.atlasplugins.atlaseconomiasecundaria.api.AtlasEconomiaSecundariaAPI");
                atlasAPI.getMethod("removeCoins", Player.class, double.class).invoke(null, player, amount);
                return true;
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao remover AtlasEconomiaSecundaria: " + e.getMessage());
            }
        }
        return false;
    }

    // Métodos para StormEconomiaSecundaria
    private double getStormEconomiaBalance(Player player) {
        if (Bukkit.getPluginManager().getPlugin("StormEconomiaSecundaria") != null) {
            try {
                Class<?> stormAPI = Class
                        .forName("com.stormplugins.stormeconomiasecundaria.api.StormEconomiaSecundariaAPI");
                return (double) stormAPI.getMethod("getCoins", Player.class).invoke(null, player);
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao acessar StormEconomiaSecundaria: " + e.getMessage());
            }
        }
        return 0.0;
    }

    private boolean withdrawStormEconomia(Player player, double amount) {
        if (Bukkit.getPluginManager().getPlugin("StormEconomiaSecundaria") != null) {
            try {
                Class<?> stormAPI = Class
                        .forName("com.stormplugins.stormeconomiasecundaria.api.StormEconomiaSecundariaAPI");
                stormAPI.getMethod("removeCoins", Player.class, double.class).invoke(null, player, amount);
                return true;
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao remover StormEconomiaSecundaria: " + e.getMessage());
            }
        }
        return false;
    }

    public String getEconomyType() {
        return economyType;
    }
}
