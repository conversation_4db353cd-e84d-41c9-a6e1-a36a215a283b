package com.atlasplugins.pescaria.managers;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.models.Mission;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class PlayerManager {

    private final Pescaria plugin;
    private final Map<UUID, Integer> playerFishes;
    private final Map<UUID, Integer> playerMissions;

    public PlayerManager(Pescaria plugin) {
        this.plugin = plugin;
        this.playerFishes = new HashMap<>();
        this.playerMissions = new HashMap<>();
        loadAllPlayers();
    }

    private void loadAllPlayers() {
        FileConfiguration dataConfig = plugin.getConfigManager().getDataConfig();
        if (dataConfig.contains("Players")) {
            for (String uuidStr : dataConfig.getConfigurationSection("Players").getKeys(false)) {
                try {
                    UUID uuid = UUID.fromString(uuidStr);
                    int fishes = dataConfig.getInt("Players." + uuidStr + ".Fishes", 0);
                    int missionOrder = dataConfig.getInt("Players." + uuidStr + ".MissionOrder", 1);

                    playerFishes.put(uuid, fishes);
                    playerMissions.put(uuid, missionOrder);
                } catch (IllegalArgumentException e) {
                    plugin.getLogger().warning("UUID inválido encontrado no arquivo de dados: " + uuidStr);
                }
            }
        }
    }

    public void saveAllPlayers() {
        FileConfiguration dataConfig = plugin.getConfigManager().getDataConfig();

        // Limpar seção de jogadores
        dataConfig.set("Players", null);

        // Salvar dados de todos os jogadores
        for (Map.Entry<UUID, Integer> entry : playerFishes.entrySet()) {
            UUID uuid = entry.getKey();
            int fishes = entry.getValue();
            int missionOrder = playerMissions.getOrDefault(uuid, 1);

            dataConfig.set("Players." + uuid.toString() + ".Fishes", fishes);
            dataConfig.set("Players." + uuid.toString() + ".MissionOrder", missionOrder);
        }

        plugin.getConfigManager().saveDataConfig();
    }

    public void savePlayer(UUID uuid) {
        if (!playerFishes.containsKey(uuid)) {
            return;
        }

        FileConfiguration dataConfig = plugin.getConfigManager().getDataConfig();
        dataConfig.set("Players." + uuid.toString() + ".Fishes", playerFishes.get(uuid));
        dataConfig.set("Players." + uuid.toString() + ".MissionOrder", playerMissions.getOrDefault(uuid, 1));
        plugin.getConfigManager().saveDataConfig();
    }

    public int getPlayerFishes(UUID uuid) {
        return playerFishes.getOrDefault(uuid, 0);
    }

    public int getPlayerMissionOrder(UUID uuid) {
        return playerMissions.getOrDefault(uuid, 1);
    }

    public void setPlayerFishes(UUID uuid, int fishes) {
        playerFishes.put(uuid, fishes);
    }

    public void setPlayerMission(UUID uuid, int missionOrder) {
        playerMissions.put(uuid, missionOrder);
    }

    public void addPlayerFish(UUID uuid) {
        int currentFishes = getPlayerFishes(uuid);
        setPlayerFishes(uuid, currentFishes + 1);

        // Verificar se completou a missão atual
        Player player = Bukkit.getPlayer(uuid);
        if (player != null) {
            checkMissionCompletion(player);
        }
    }

    public void checkMissionCompletion(Player player) {
        UUID uuid = player.getUniqueId();
        int currentMissionOrder = getPlayerMissionOrder(uuid);
        int currentFishes = getPlayerFishes(uuid);

        Mission currentMission = plugin.getMissionManager().getMissionByOrder(currentMissionOrder);
        if (currentMission == null) {
            return; // Não há missão atual
        }

        if (currentFishes >= currentMission.getPeixesToComplete()) {
            // Missão completada
            plugin.getMissionManager().completeMission(player, currentMission);
        }
    }

    public Mission getCurrentMission(UUID uuid) {
        int missionOrder = getPlayerMissionOrder(uuid);
        return plugin.getMissionManager().getMissionByOrder(missionOrder);
    }

    public int getMissionProgress(UUID uuid) {
        Mission currentMission = getCurrentMission(uuid);
        if (currentMission == null) {
            return 0;
        }

        int currentFishes = getPlayerFishes(uuid);
        int requiredFishes = currentMission.getPeixesToComplete();

        return Math.min(currentFishes, requiredFishes);
    }

    public String getMissionProgressBar(UUID uuid) {
        Mission currentMission = getCurrentMission(uuid);
        if (currentMission == null) {
            return "§cNenhuma missão disponível";
        }

        int currentFishes = getPlayerFishes(uuid);
        int requiredFishes = currentMission.getPeixesToComplete();

        // Calcular progresso em porcentagem
        double progress = (double) currentFishes / requiredFishes * 100;
        progress = Math.min(progress, 100); // Limitar a 100%

        // Criar barra de progresso
        StringBuilder progressBar = new StringBuilder("§7[§a");
        int barLength = 10;
        int filledBars = (int) (progress / 100 * barLength);

        for (int i = 0; i < barLength; i++) {
            if (i < filledBars) {
                progressBar.append("|");
            } else {
                progressBar.append("§7|");
            }
        }

        progressBar.append("§7] §f").append(String.format("%.1f", progress)).append("%");

        return progressBar.toString();
    }

    public int getCompletedMissions(UUID uuid) {
        int currentMissionOrder = getPlayerMissionOrder(uuid);
        return currentMissionOrder - 1; // Missões completadas são as anteriores à atual
    }
}