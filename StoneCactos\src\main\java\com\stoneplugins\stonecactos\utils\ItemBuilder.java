package com.stoneplugins.stonecactos.utils;

import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class ItemBuilder {
  private ItemStack item;
  private ItemMeta meta;

  public ItemBuilder(Material material) {
    this.item = new ItemStack(material);
    this.meta = item.getItemMeta();
  }

  public ItemBuilder(ItemStack item) {
    this.item = item;
    this.meta = item.getItemMeta();
  }

  public static ItemBuilder skull(String texture) {
    ItemStack head = new ItemStack(Material.valueOf("SKULL_ITEM"), 1, (short) 3);
    ItemBuilder builder = new ItemBuilder(head);

    try {
      // Aplicar textura usando reflection
      org.bukkit.inventory.meta.SkullMeta skullMeta = (org.bukkit.inventory.meta.SkullMeta) builder.meta;

      // Criar GameProfile com textura
      Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
      Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");

      Object gameProfile = gameProfileClass.getConstructor(java.util.UUID.class, String.class)
          .newInstance(java.util.UUID.randomUUID(), null);

      Object property = propertyClass.getConstructor(String.class, String.class)
          .newInstance("textures", texture);

      Object properties = gameProfile.getClass().getMethod("getProperties").invoke(gameProfile);
      properties.getClass().getMethod("put", Object.class, Object.class)
          .invoke(properties, "textures", property);

      // Aplicar GameProfile ao SkullMeta
      java.lang.reflect.Field profileField = skullMeta.getClass().getDeclaredField("profile");
      profileField.setAccessible(true);
      profileField.set(skullMeta, gameProfile);

      builder.item.setItemMeta(skullMeta);
      builder.meta = builder.item.getItemMeta();
    } catch (Exception e) {
      e.printStackTrace();
    }

    return builder;
  }

  public ItemBuilder setDisplayName(String name) {
    meta.setDisplayName(name);
    return this;
  }

  public ItemBuilder setLore(String... lore) {
    meta.setLore(Arrays.asList(lore));
    return this;
  }

  public ItemBuilder setLore(List<String> lore) {
    meta.setLore(lore);
    return this;
  }

  public ItemBuilder addLoreLine(String line) {
    List<String> lore = meta.getLore();
    if (lore == null) {
      lore = new ArrayList<>();
    }
    lore.add(line);
    meta.setLore(lore);
    return this;
  }

  public ItemBuilder setAmount(int amount) {
    item.setAmount(amount);
    return this;
  }

  public ItemBuilder addEnchant(Enchantment enchantment, int level) {
    meta.addEnchant(enchantment, level, true);
    return this;
  }

  public ItemBuilder addItemFlag(ItemFlag flag) {
    meta.addItemFlags(flag);
    return this;
  }

  public ItemStack build() {
    item.setItemMeta(meta);
    return item;
  }
}
