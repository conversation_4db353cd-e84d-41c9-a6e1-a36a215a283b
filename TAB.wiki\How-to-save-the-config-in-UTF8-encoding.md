# Programs
* [Notepad](#notepad)
* [Notepad++](#notepad-1)
* [Sublime Text 3](#sublime-text-3)
* [WinSCP](#winscp)
* [VSCode](#vscode)
***

# Notepad
Open the file > Click file > Save as... > Set encoding to UTF-8 > Click save
![](https://i.ibb.co/zQHbL3p/notepad.png)

***

# Notepad++
Open the file > Click encoding > Click UTF-8 (if that didn't work, use Convert to UTF-8)  
![](https://i.ibb.co/qNdj80Q/notepad.png)

***

# Sublime Text 3
![](https://cdn.discordapp.com/attachments/659545536533364776/847537718236938260/unknown.png)

***

# WinSCP
Open the file in WinSCP and check if it is in UTF-8 encoding already (1). If it is not, click on ENCODING at the top (2), select UTF-8 (3) and then save the file. You can now edit the file as you like and paste in characters.
![](https://i.ibb.co/nRxDmHk/Win-SCP-Xlxw-Qfft-Cb.png)
![](https://i.ibb.co/dP77yzp/Win-SCP-UXWc4-Xdp2-N.png)

# VSCode
At the bottom right of VSCode, you can change the current file's encoding. It'll open a pop-up where you can select UTF-8
![](https://github.com/user-attachments/assets/3ca885ee-24fb-47a5-863b-46b69df42e3a)
![](https://github.com/user-attachments/assets/ceab40b0-d875-4292-9d75-414557c7d4f8)
