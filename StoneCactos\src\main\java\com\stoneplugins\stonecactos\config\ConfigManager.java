package com.stoneplugins.stonecactos.config;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.List;

public class ConfigManager {
    
    private final StoneCactos plugin;
    private FileConfiguration config;
    private FileConfiguration messages;
    private FileConfiguration menus;
    
    private File configFile;
    private File messagesFile;
    private File menusFile;
    
    public ConfigManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    public void loadConfigs() {
        // Criar pasta de configurações se não existir
        if (!plugin.getDataFolder().exists()) {
            plugin.getDataFolder().mkdirs();
        }
        
        // Carregar config.yml
        loadConfig();
        
        // Carregar messages.yml
        loadMessages();
        
        // Carregar menus.yml
        loadMenus();
    }
    
    private void loadConfig() {
        configFile = new File(plugin.getDataFolder(), "config.yml");
        if (!configFile.exists()) {
            plugin.saveResource("config.yml", false);
        }
        config = YamlConfiguration.loadConfiguration(configFile);
    }
    
    private void loadMessages() {
        messagesFile = new File(plugin.getDataFolder(), "messages.yml");
        if (!messagesFile.exists()) {
            plugin.saveResource("messages.yml", false);
        }
        messages = YamlConfiguration.loadConfiguration(messagesFile);
    }
    
    private void loadMenus() {
        menusFile = new File(plugin.getDataFolder(), "menus.yml");
        if (!menusFile.exists()) {
            plugin.saveResource("menus.yml", false);
        }
        menus = YamlConfiguration.loadConfiguration(menusFile);
    }
    
    public void reloadConfigs() {
        config = YamlConfiguration.loadConfiguration(configFile);
        messages = YamlConfiguration.loadConfiguration(messagesFile);
        menus = YamlConfiguration.loadConfiguration(menusFile);
    }
    
    public void saveConfig() {
        try {
            config.save(configFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erro ao salvar config.yml: " + e.getMessage());
        }
    }
    
    public void saveMessages() {
        try {
            messages.save(messagesFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erro ao salvar messages.yml: " + e.getMessage());
        }
    }
    
    public void saveMenus() {
        try {
            menus.save(menusFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Erro ao salvar menus.yml: " + e.getMessage());
        }
    }
    
    // Getters para configurações
    public FileConfiguration getConfig() {
        return config;
    }
    
    public FileConfiguration getMessages() {
        return messages;
    }
    
    public FileConfiguration getMenus() {
        return menus;
    }
    
    // Métodos utilitários para acessar configurações específicas
    public String getDatabaseType() {
        return config.getString("Geral.sqlType", "SQLITE");
    }
    
    public String getPlotWorldName() {
        return config.getString("Geral.plotWorldName", "plots");
    }
    
    public double getUnitCactusValue() {
        return config.getDouble("Geral.unitCactusValue", 100.0);
    }
    
    public String getCashPlugin() {
        return config.getString("Geral.cashPlugin", "VAULT");
    }
    
    public String getCactusEconomy() {
        return config.getString("Geral.cactusEconomy", "VAULT");
    }
    
    public int getBatteryConsumptionCactus() {
        return config.getInt("Geral.standardBatteryConsumption.cactus", 10);
    }
    
    public double getBatteryConsumptionAmount() {
        return config.getDouble("Geral.standardBatteryConsumption.consume", 1.0);
    }
    
    public double getCactusHeight() {
        return config.getDouble("Geral.cactusHeight", 2.3);
    }
    
    public List<String> getCactusHologram() {
        return config.getStringList("Geral.cactusHologram");
    }
    
    // Métodos para mensagens
    public String getMessage(String path) {
        return messages.getString(path, "§cMensagem não encontrada: " + path);
    }
    
    public String getMessage(String path, String defaultValue) {
        return messages.getString(path, defaultValue);
    }
    
    public List<String> getMessageList(String path) {
        return messages.getStringList(path);
    }
    
    // Métodos para menus
    public String getMenuTitle(String menuName) {
        return menus.getString("Menus." + menuName + ".title", "Menu");
    }
    
    public int getMenuSize(String menuName) {
        return menus.getInt("Menus." + menuName + ".size", 27);
    }
    
    public String getMenuItemName(String menuName, String itemName) {
        return menus.getString("Menus." + menuName + "." + itemName + ".name", "§cItem");
    }
    
    public List<String> getMenuItemLore(String menuName, String itemName) {
        return menus.getStringList("Menus." + menuName + "." + itemName + ".lore");
    }
    
    public String getMenuItemMaterial(String menuName, String itemName) {
        return menus.getString("Menus." + menuName + "." + itemName + ".item", "STONE");
    }
    
    public int getMenuItemSlot(String menuName, String itemName) {
        return menus.getInt("Menus." + menuName + "." + itemName + ".slot", 0);
    }
    
    // Métodos para boosters
    public boolean hasBooster(String boosterId) {
        return config.contains("Boosters." + boosterId);
    }
    
    public String getBoosterName(String boosterId) {
        return config.getString("Boosters." + boosterId + ".name", "§aBooster");
    }
    
    public List<String> getBoosterLore(String boosterId) {
        return config.getStringList("Boosters." + boosterId + ".lore");
    }
    
    public String getBoosterItem(String boosterId) {
        return config.getString("Boosters." + boosterId + ".item", "EXP_BOTTLE:0");
    }
    
    public int getBoosterTime(String boosterId) {
        return config.getInt("Boosters." + boosterId + ".time", 60);
    }
    
    public double getBoosterMultiplier(String boosterId) {
        return config.getDouble("Boosters." + boosterId + ".multiplier", 2.0);
    }
    
    public double getBoosterPrice(String boosterId) {
        return config.getDouble("Boosters." + boosterId + ".price", 50000);
    }
    
    public String getBoosterEconomy(String boosterId) {
        return config.getString("Boosters." + boosterId + ".economy", "VAULT");
    }
    
    // Métodos para recompensas
    public boolean hasReward(String rewardId) {
        return config.contains("Rewards." + rewardId);
    }
    
    public String getRewardName(String rewardId) {
        return config.getString("Rewards." + rewardId + ".name", "§aRecompensa");
    }
    
    public List<String> getRewardLore(String rewardId) {
        return config.getStringList("Rewards." + rewardId + ".lore");
    }
    
    public String getRewardItem(String rewardId) {
        return config.getString("Rewards." + rewardId + ".item", "DIAMOND");
    }
    
    public int getRewardNecessaryCactus(String rewardId) {
        return config.getInt("Rewards." + rewardId + ".necessaryCactus", 100);
    }
    
    public List<String> getRewardCommands(String rewardId) {
        return config.getStringList("Rewards." + rewardId + ".rewardCommands");
    }
    
    public int getRewardId(String rewardId) {
        return config.getInt("Rewards." + rewardId + ".id", 0);
    }
    
    // Métodos para upgrades
    public double getUpgradePrice(String upgradeType, int level) {
        return config.getDouble("EnvolveDevelopment." + upgradeType + "." + level + ".price", 10000);
    }
    
    public String getUpgradeEconomy(String upgradeType, int level) {
        return config.getString("EnvolveDevelopment." + upgradeType + "." + level + ".economy", "VAULT");
    }
    
    public int getUpgradeCapacity(int level) {
        return config.getInt("EnvolveDevelopment.capacityTowers." + level + ".capacity", 100);
    }
    
    public int getUpgradeQuantity(int level) {
        return config.getInt("EnvolveDevelopment.quantityPerConstruction." + level + ".quantity", 2);
    }
    
    public long getUpgradeTime(int level) {
        return config.getLong("EnvolveDevelopment.constructionTime." + level + ".time", 40000);
    }
}
