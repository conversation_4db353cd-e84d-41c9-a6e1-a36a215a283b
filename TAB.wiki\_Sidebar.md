## Getting started
* [Why TAB?](https://github.com/NEZNAMY/TAB/wiki/Why-TAB%3F)
* [Installation](https://github.com/NEZNAMY/TAB/wiki/Installation)  
* [Commands & Permissions](https://github.com/NEZNAMY/TAB/wiki/Commands-&-Permissions)  
* [Frequently Asked Questions](https://github.com/NEZNAMY/TAB/wiki/Frequently-Asked-Questions)  
* [Compatibility](https://github.com/NEZNAMY/TAB/wiki/Compatibility)
* [How to assign players into groups](https://github.com/NEZNAMY/TAB/wiki/How-to-assign-players-into-groups)  
* [Known issues](https://github.com/NEZNAMY/TAB/wiki/Known-issues)
* [TAB-Bridge plugin](https://github.com/NEZNAMY/TAB/wiki/TAB-Bridge)

## Core features
* [Belowname](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Belowname)
* [Bossbar](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Bossbar)
* [Global playerlist](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Global-playerlist)
* [Header/Footer](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Header-&-Footer)
* [Layout](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Layout)
* [Multi server support](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Multi-server-support)
* [Nametags](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Nametags)
* [Per world playerlist](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Per-world-playerlist)
* [Ping spoof](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Ping-Spoof)
* [Playerlist objective](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Playerlist-Objective)
* [Scoreboard](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Scoreboard)
* [Sorting in tablist](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Sorting-players-in-tablist)  
* [Spectator fix](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Spectator-fix)
* [Tablist name formatting](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Tablist-name-formatting)

## Functions
* [Animations](https://github.com/NEZNAMY/TAB/wiki/Animations)
* [Conditional placeholders](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Conditional-placeholders)
* [Error logging](https://github.com/NEZNAMY/TAB/wiki/Error-logging)  
* [MySQL](https://github.com/NEZNAMY/TAB/wiki/MySQL)
* [Placeholder output replacements](https://github.com/NEZNAMY/TAB/wiki/Feature-guide:-Placeholder-output-replacements)
* [Placeholders](https://github.com/NEZNAMY/TAB/wiki/Placeholders)  
* [RGB/font usage](https://github.com/NEZNAMY/TAB/wiki/How-to-use-RGB-color-codes)

## Other
* [Additional information](https://github.com/NEZNAMY/TAB/wiki/Additional-information)
* [Bedrock compatibility](https://github.com/NEZNAMY/TAB/wiki/Bedrock-compatibility)
* [Client-sided mechanics](https://github.com/NEZNAMY/TAB/wiki/Client%E2%80%90sided-mechanics)
* [Developer API](https://github.com/NEZNAMY/TAB/wiki/Developer-API)
* [How to display name from nickname plugins](https://github.com/NEZNAMY/TAB/wiki/How-to-display-name-from-nickname-plugins)
* [How to save config in UTF-8 encoding](https://github.com/NEZNAMY/TAB/wiki/How-to-save-the-config-in-UTF8-encoding)
* [How to set up compatibility with glow plugins](https://github.com/NEZNAMY/TAB/wiki/How-to-make-TAB-compatible-with-glow-plugins)
* [Mini guides collection](https://github.com/NEZNAMY/TAB/wiki/Mini-guides-collection)
* [Optimizing plugin's CPU usage](https://github.com/NEZNAMY/TAB/wiki/Optimizing-the-plugin)
* [Quick PlaceholderAPI startup guide](https://github.com/NEZNAMY/TAB/wiki/Quick-PlaceholderAPI-startup-guide)  