package com.stoneplugins.spawnershop.listeners;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.data.Spawner;
import com.stoneplugins.spawnershop.gui.MainMenu;
import com.stoneplugins.spawnershop.managers.PlayerDataManager;
import com.stoneplugins.spawnershop.utils.MessageUtils;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.AsyncPlayerChatEvent;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

public class PlayerChatListener implements Listener {

    private final SpawnerShop plugin;
    private final Map<UUID, String> waitingForQuantity;
    private final Map<UUID, Boolean> waitingForMultiplicador;

    public PlayerChatListener(SpawnerShop plugin) {
        this.plugin = plugin;
        this.waitingForQuantity = new HashMap<>();
        this.waitingForMultiplicador = new HashMap<>();
    }

    @EventHandler(priority = EventPriority.LOWEST)
    public void onPlayerChat(AsyncPlayerChatEvent event) {
        Player player = event.getPlayer();
        String message = event.getMessage().toLowerCase().trim();

        // Verificar se o jogador tem uma compra pendente
        if (plugin.getPlayerDataManager().hasPendingPurchase(player)) {
            event.setCancelled(true);

            if (message.equals("confirmar") || message.equals("confirm")) {
                // Confirmar compra
                PlayerDataManager.PendingPurchase pending = plugin.getPlayerDataManager().getPendingPurchase(player);
                if (pending != null) {
                    plugin.getServer().getScheduler().runTask(plugin, () -> {
                        boolean success = plugin.getSpawnerManager().purchaseSpawner(player, pending.getSpawner(),
                                pending.getQuantity());
                        if (success) {
                            player.sendMessage(plugin.getMessageUtils()
                                    .colorize(plugin.getConfigManager().getMessage("spawnercomprado")));
                        }
                        plugin.getPlayerDataManager().removePendingPurchase(player);
                    });
                }
                return;
            }

            if (message.equals("cancelar") || message.equals("cancel")) {
                // Cancelar compra
                plugin.getPlayerDataManager().removePendingPurchase(player);
                player.sendMessage("§cCompra cancelada!");
                return;
            }

            // Mensagem inválida
            player.sendMessage("§cDigite §f'confirmar' §cpara confirmar a compra ou §f'cancelar' §cpara cancelar.");
            return;
        }

        // Verificar se o jogador está esperando para digitar multiplicador
        if (isWaitingForMultiplicador(player)) {
            event.setCancelled(true);

            if (message.equals("cancelar") || message.equals("cancel")) {
                removeWaitingForMultiplicador(player);
                player.sendMessage("§cAção cancelada!");
                return;
            }

            try {
                int novoMultiplicador = Integer.parseInt(message);
                if (novoMultiplicador >= 1 && novoMultiplicador <= 10) {
                    PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
                    playerData.setMultiplicador(novoMultiplicador);

                    plugin.getServer().getScheduler().runTaskAsynchronously(plugin, () -> {
                        plugin.getDatabaseManager().savePlayerData(playerData);
                    });

                    String successMessage = plugin.getConfigManager().getMessage("multiplicadoralterado")
                            .replace("{multiplicador}", String.valueOf(novoMultiplicador));
                    player.sendMessage(plugin.getMessageUtils().colorize(successMessage));
                } else {
                    String errorMessage = plugin.getConfigManager().getMessage("multiplicadorinvalido")
                            .replace("{maximo}", "10");
                    player.sendMessage(plugin.getMessageUtils().colorize(errorMessage));
                }
            } catch (NumberFormatException e) {
                String errorMessage = plugin.getConfigManager().getMessage("multiplicadorinvalido")
                        .replace("{maximo}", "10");
                player.sendMessage(plugin.getMessageUtils().colorize(errorMessage));
            }

            removeWaitingForMultiplicador(player);
            return;
        }

        // Verificar se o jogador está esperando para digitar quantidade
        if (waitingForQuantity.containsKey(player.getUniqueId())) {
            event.setCancelled(true);

            String spawnerId = waitingForQuantity.get(player.getUniqueId());
            waitingForQuantity.remove(player.getUniqueId());

            if (message.equals("cancelar") || message.equals("cancel")) {
                player.sendMessage("§cAção cancelada!");
                return;
            }

            // Verificar se é um número válido
            try {
                int quantity = Integer.parseInt(message);
                if (quantity <= 0) {
                    player.sendMessage("§cA quantidade deve ser maior que 0!");
                    return;
                }

                // Executar compra
                plugin.getServer().getScheduler().runTask(plugin, () -> {
                    Spawner spawner = plugin.getSpawnerManager().getSpawner(spawnerId);
                    if (spawner == null) {
                        player.sendMessage("§cSpawner não encontrado!");
                        return;
                    }

                    // Verificar se pode comprar
                    if (!plugin.getSpawnerManager().canPlayerBuySpawner(player, spawner)) {
                        String status = plugin.getSpawnerManager().getSpawnerStatus(player, spawner);
                        player.sendMessage("§cVocê não pode comprar este spawner!");
                        player.sendMessage(plugin.getMessageUtils().colorize(status));
                        return;
                    }

                    // Verificar limite por compra (não acumulado)
                    PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
                    if (quantity > playerData.getPurchaseLimit()) {
                        player.sendMessage(
                                plugin.getMessageUtils().colorize("&c&lLIMITE EXCEDIDO! &fVoce so pode comprar ate &a"
                                        + playerData.getPurchaseLimit() + "&f geradores por vez."));
                        return;
                    }

                    // Calcular preço
                    double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
                    double finalPrice = spawner.getPriceWithDiscount(discount) * quantity;

                    // Verificar saldo
                    if (!plugin.getEconomyManager().hasBalance(player, finalPrice)) {
                        player.sendMessage(
                                plugin.getMessageUtils().colorize(plugin.getConfigManager().getMessage("semsaldo")));
                        return;
                    }

                    // Executar compra (SpawnerManager já envia mensagem de sucesso)
                    boolean success = plugin.getSpawnerManager().purchaseSpawner(player, spawner, quantity);

                    // Se a compra foi bem-sucedida, reabrir a loja atualizada
                    if (success) {
                        plugin.getServer().getScheduler().runTask(plugin, () -> {
                            new MainMenu(plugin, player).open();
                        });
                    }
                });

            } catch (NumberFormatException e) {
                player.sendMessage("§cQuantidade inválida! Digite um número válido.");
            }
        }
    }

    public void addWaitingForQuantity(Player player, String spawnerId) {
        waitingForQuantity.put(player.getUniqueId(), spawnerId);

        // Enviar mensagem de instrução
        plugin.getMessageUtils().sendConfigMessageList(player, "quantidadeSpawners");
    }

    public void removeWaitingForQuantity(Player player) {
        waitingForQuantity.remove(player.getUniqueId());
    }

    public void addWaitingForMultiplicador(Player player) {
        waitingForMultiplicador.put(player.getUniqueId(), true);

        // Enviar mensagem de instrução
        MessageUtils messageUtils = plugin.getMessageUtils();
        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);

        for (String line : plugin.getConfigManager().getConfig().getStringList("Mensagens.multiplicadorEscolha")) {
            String message = line
                    .replace("{maximo}", "10")
                    .replace("{atual}", String.valueOf(playerData.getMultiplicador()));
            player.sendMessage(messageUtils.colorize(message));
        }
    }

    public boolean isWaitingForMultiplicador(Player player) {
        return waitingForMultiplicador.containsKey(player.getUniqueId());
    }

    public void removeWaitingForMultiplicador(Player player) {
        waitingForMultiplicador.remove(player.getUniqueId());
    }

    public boolean isWaitingForQuantity(Player player) {
        return waitingForQuantity.containsKey(player.getUniqueId());
    }
}
