# ========================================
#     STONE TAGS + TAB INTEGRATION
# ========================================
# Este é um exemplo de como configurar o TAB para usar os placeholders do StoneTags
# Copie este conteúdo para o config.yml do TAB

# Configuração do TAB para StoneTags
tablist-name-formatting:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%%stonetags_current_tag_suffix%"

# Configuração de grupos baseada nas tags
groups:
  staff:
    tabprefix: "&c[STAFF] "
    tabsuffix: ""
    sortpriority: 1000
  mvp:
    tabprefix: "&b[MVP] "
    tabsuffix: ""
    sortpriority: 800
  vip:
    tabprefix: "&6[VIP] "
    tabsuffix: ""
    sortpriority: 600
  default:
    tabprefix: ""
    tabsuffix: ""
    sortpriority: 100

# Placeholders do StoneTags disponíveis para usar no TAB:
# %stonetags_current_tag_id% - ID da tag atual
# %stonetags_current_tag_name% - Nome da tag atual
# %stonetags_current_tag_prefix% - Prefix da tag atual
# %stonetags_current_tag_suffix% - Suffix da tag atual
# %stonetags_current_tag_color% - Cor da tag atual
# %stonetags_current_tag_position% - Posição da tag atual
# %stonetags_highest_tag_id% - ID da tag de maior prioridade
# %stonetags_highest_tag_name% - Nome da tag de maior prioridade
# %stonetags_highest_tag_position% - Posição da tag de maior prioridade
# %stonetags_has_tag% - Se o jogador tem uma tag selecionada
# %stonetags_tag_count% - Quantidade de tags disponíveis para o jogador
# %stonetags_formatted_name% - Nome formatado com a tag

# Exemplo de configuração avançada do TAB:
tablist:
  enabled: true
  header-footer:
    enabled: true
    header:
      - "&6&lSERVIDOR MINECRAFT"
      - "&7Tags by StoneTags"
    footer:
      - "&7Sua tag: %stonetags_current_tag_name%"
      - "&7Posição: %stonetags_current_tag_position%"

# Configuração de sorting automático baseado na posição da tag
sorting:
  enabled: true
  sorting-types:
    - "PLACEHOLDER_LOW_TO_HIGH:%stonetags_current_tag_position%"
    - "PLACEHOLDER_A_TO_Z:%player%"

# Configuração de nametags (tags acima dos jogadores)
nametags:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%"

# Configuração de belowname (texto abaixo do nome)
belowname:
  enabled: true
  text: "%stonetags_current_tag_name%"

# Configuração de playerlist
playerlist:
  enabled: true
  format: "%stonetags_current_tag_prefix%%player%%stonetags_current_tag_suffix%"
