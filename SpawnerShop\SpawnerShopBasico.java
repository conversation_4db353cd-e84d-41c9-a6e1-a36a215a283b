import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.ClickType;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.AsyncPlayerChatEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;
import org.bukkit.plugin.java.JavaPlugin;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

public class SpawnerShopBasico extends JavaPlugin implements Listener {
    
    private Map<String, SpawnerInfo> spawners = new HashMap<>();
    private Map<UUID, PlayerData> playerData = new HashMap<>();
    private Map<UUID, String> playerChat = new HashMap<>();
    private Connection connection;
    private String dbPath;
    private int maxMultiplicador = 100;
    
    // Top compradores
    private Map<String, Integer> topCompradores = new LinkedHashMap<>();
    private Map<String, Integer> topLimites = new LinkedHashMap<>();
    
    @Override
    public void onEnable() {
        // Salvar config padrão se não existir
        saveDefaultConfig();
        
        // Inicializar banco de dados
        setupDatabase();
        
        // Registrar eventos
        getServer().getPluginManager().registerEvents(this, this);
        
        // Carregar spawners da config
        loadSpawners();
        
        // Carregar top compradores
        loadTopCompradores();
        
        getLogger().info("SpawnerShop carregado com sucesso!");
        getLogger().info("Use /spawners para abrir a loja!");
    }
    
    @Override
    public void onDisable() {
        // Fechar conexão com banco de dados
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
            }
        } catch (SQLException e) {
            getLogger().severe("Erro ao fechar conexão com banco de dados: " + e.getMessage());
        }
        
        getLogger().info("SpawnerShop descarregado!");
    }
    
    /**
     * Configura o banco de dados SQLite
     */
    private void setupDatabase() {
        dbPath = getDataFolder().getAbsolutePath() + "/database.db";
        
        try {
            Class.forName("org.sqlite.JDBC");
            connection = DriverManager.getConnection("jdbc:sqlite:" + dbPath);
            
            Statement statement = connection.createStatement();
            
            // Criar tabela de jogadores se não existir
            statement.execute(
                "CREATE TABLE IF NOT EXISTS player_data (" +
                "uuid TEXT PRIMARY KEY, " +
                "nome TEXT, " +
                "limite INTEGER DEFAULT 64, " +
                "compras INTEGER DEFAULT 0, " +
                "multiplicador INTEGER DEFAULT 1)"
            );
            
            // Verificar se a coluna multiplicador existe, se não, adicionar
            try {
                ResultSet rs = statement.executeQuery("SELECT multiplicador FROM player_data LIMIT 1");
                rs.close();
            } catch (SQLException e) {
                if (e.getMessage().contains("no such column")) {
                    statement.execute("ALTER TABLE player_data ADD COLUMN multiplicador INTEGER DEFAULT 1");
                    getLogger().info("Coluna 'multiplicador' adicionada à tabela player_data");
                }
            }
            
            statement.close();
        } catch (ClassNotFoundException | SQLException e) {
            getLogger().severe("Erro ao configurar banco de dados: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void loadSpawners() {
        spawners.put("cow", new SpawnerInfo("&eVaca", 30000));
        spawners.put("pig", new SpawnerInfo("&ePorco", 25000));
        spawners.put("chicken", new SpawnerInfo("&eGalinha", 20000));
        spawners.put("sheep", new SpawnerInfo("&eOvelha", 28000));
        spawners.put("zombie", new SpawnerInfo("&eZombie", 60000));
        spawners.put("skeleton", new SpawnerInfo("&eEsqueleto", 65000));
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage(ChatColor.RED + "Apenas jogadores podem usar este comando!");
            return true;
        }
        
        Player player = (Player) sender;
        
        if (command.getName().equalsIgnoreCase("spawners")) {
            if (args.length == 0) {
                openSpawnerShop(player);
                return true;
            }
            
            if (args[0].equalsIgnoreCase("reload") && player.hasPermission("spawnershop.admin")) {
                loadSpawners();
                player.sendMessage(ChatColor.GREEN + "Plugin recarregado!");
                return true;
            }
            
            if (args[0].equalsIgnoreCase("help")) {
                player.sendMessage(ChatColor.GOLD + "=== SpawnerShop ===");
                player.sendMessage(ChatColor.WHITE + "/spawners - Abrir loja");
                player.sendMessage(ChatColor.WHITE + "/spawners help - Esta ajuda");
                if (player.hasPermission("spawnershop.admin")) {
                    player.sendMessage(ChatColor.RED + "/spawners reload - Recarregar");
                }
                return true;
            }
        }
        
        return false;
    }
    
    private void openSpawnerShop(Player player) {
        Inventory inv = Bukkit.createInventory(null, 54, 
            ChatColor.translateAlternateColorCodes('&', "&8Loja de Spawners"));
        
        int slot = 10;
        for (Map.Entry<String, SpawnerInfo> entry : spawners.entrySet()) {
            String id = entry.getKey();
            SpawnerInfo info = entry.getValue();
            
            ItemStack item = new ItemStack(Material.MOB_SPAWNER);
            ItemMeta meta = item.getItemMeta();
            meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', info.name));
            meta.setLore(Arrays.asList(
                "",
                ChatColor.GRAY + "Preço: " + ChatColor.GREEN + formatMoney(info.price),
                ChatColor.GRAY + "Tipo: " + ChatColor.YELLOW + id,
                "",
                ChatColor.GREEN + "Clique para comprar!"
            ));
            item.setItemMeta(meta);
            
            inv.setItem(slot, item);
            slot++;
            if (slot == 17) slot = 19;
            if (slot == 26) slot = 28;
            if (slot >= 35) break;
        }
        
        // Item de informações
        ItemStack info = new ItemStack(Material.BOOK);
        ItemMeta infoMeta = info.getItemMeta();
        infoMeta.setDisplayName(ChatColor.GOLD + "Informações");
        infoMeta.setLore(Arrays.asList(
            "",
            ChatColor.GRAY + "Bem-vindo à loja de spawners!",
            ChatColor.GRAY + "Clique nos spawners para comprar.",
            "",
            ChatColor.YELLOW + "Plugin criado por StonePlugins"
        ));
        info.setItemMeta(infoMeta);
        inv.setItem(4, info);
        
        player.openInventory(inv);
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        
        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();
        
        if (title.contains("Loja de Spawners")) {
            event.setCancelled(true);
            
            ItemStack item = event.getCurrentItem();
            if (item == null || item.getType() != Material.MOB_SPAWNER) return;
            
            String itemName = item.getItemMeta().getDisplayName();
            
            for (Map.Entry<String, SpawnerInfo> entry : spawners.entrySet()) {
                String id = entry.getKey();
                SpawnerInfo info = entry.getValue();
                
                if (ChatColor.translateAlternateColorCodes('&', info.name).equals(itemName)) {
                    player.closeInventory();
                    player.sendMessage(ChatColor.GREEN + "Você comprou um spawner de " + 
                        ChatColor.stripColor(itemName) + "!");
                    player.sendMessage(ChatColor.GRAY + "Preço: " + ChatColor.GREEN + formatMoney(info.price));
                    player.sendMessage(ChatColor.YELLOW + "Comando executado: give " + 
                        player.getName() + " mob_spawner 1");
                    
                    // Aqui você integraria com economia e executaria o comando real
                    // Bukkit.dispatchCommand(Bukkit.getConsoleSender(), 
                    //     "give " + player.getName() + " mob_spawner 1");
                    
                    break;
                }
            }
        }
    }
    
    private String formatMoney(double amount) {
        if (amount >= 1000000) {
            return String.format("%.1fM", amount / 1000000);
        } else if (amount >= 1000) {
            return String.format("%.1fK", amount / 1000);
        } else {
            return String.format("%.0f", amount);
        }
    }
    
    private static class SpawnerInfo {
        String name;
        double price;
        
        SpawnerInfo(String name, double price) {
            this.name = name;
            this.price = price;
        }
    }
}
