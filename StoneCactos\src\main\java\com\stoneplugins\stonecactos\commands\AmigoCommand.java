package com.stoneplugins.stonecactos.commands;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import com.stoneplugins.stonecactos.managers.FriendsManager;
import com.stoneplugins.stonecactos.menus.FriendPermissionsMenu;
import com.stoneplugins.stonecactos.utils.MessageUtils;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class AmigoCommand implements CommandExecutor {

    private final StoneCactos plugin;
    private final FriendsManager friendsManager;
    private final MessageUtils messageUtils;
    private final FriendPermissionsMenu friendPermissionsMenu;

    public AmigoCommand(StoneCactos plugin) {
        this.plugin = plugin;
        this.friendsManager = plugin.getFriendsManager();
        this.messageUtils = plugin.getMessageUtils();
        this.friendPermissionsMenu = new FriendPermissionsMenu(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("Este comando só pode ser executado por jogadores.");
            return true;
        }

        Player player = (Player) sender;
        if (args.length < 1) {
            sendHelp(player);
            return true;
        }

        String subCommand = args[0].toLowerCase();
        switch (subCommand) {
            case "add":
                addFriend(player, args);
                break;
            case "remove":
                removeFriend(player, args);
                break;
            case "permissions":
                openPermissionsMenu(player, args);
                break;
            default:
                sendHelp(player);
                break;
        }

        return true;
    }

    private void addFriend(Player player, String[] args) {
        if (args.length < 2) {
            messageUtils.sendMessage(player, "&cUse: /amigo add <player>");
            return;
        }

        Player friend = Bukkit.getPlayer(args[1]);
        if (friend == null) {
            messageUtils.sendMessage(player, "&cJogador não encontrado.");
            return;
        }

        CactusGenerator generator = plugin.getGeneratorManager().getGenerator(player.getUniqueId());
        if (generator == null) {
            messageUtils.sendMessage(player, "&cVocê não possui um gerador.");
            return;
        }

        if (friendsManager.addFriend(generator, friend.getUniqueId(), false, false, false)) {
            messageUtils.sendMessage(player, "&aAmigo adicionado com sucesso!");
        } else {
            messageUtils.sendMessage(player, "&cEste jogador já é seu amigo.");
        }
    }

    private void removeFriend(Player player, String[] args) {
        if (args.length < 2) {
            messageUtils.sendMessage(player, "&cUse: /amigo remove <player>");
            return;
        }

        Player friend = Bukkit.getPlayer(args[1]);
        if (friend == null) {
            messageUtils.sendMessage(player, "&cJogador não encontrado.");
            return;
        }

        CactusGenerator generator = plugin.getGeneratorManager().getGenerator(player.getUniqueId());
        if (generator == null) {
            messageUtils.sendMessage(player, "&cVocê não possui um gerador.");
            return;
        }

        if (friendsManager.removeFriend(generator, friend.getUniqueId())) {
            messageUtils.sendMessage(player, "&aAmigo removido com sucesso!");
        } else {
            messageUtils.sendMessage(player, "&cEste jogador não é seu amigo.");
        }
    }

    private void openPermissionsMenu(Player player, String[] args) {
        if (args.length < 2) {
            messageUtils.sendMessage(player, "&cUse: /amigo permissions <player>");
            return;
        }

        Player friend = Bukkit.getPlayer(args[1]);
        if (friend == null) {
            messageUtils.sendMessage(player, "&cJogador não encontrado.");
            return;
        }

        player.setMetadata("editing_friend_permissions", new org.bukkit.metadata.FixedMetadataValue(plugin, friend.getUniqueId()));
        friendPermissionsMenu.openMenu(player, friend.getUniqueId());
    }

    private void sendHelp(Player player) {
        messageUtils.sendMessage(player, "&eComandos de Amigo:");
        messageUtils.sendMessage(player, "&f/amigo add <player> &7- Adiciona um amigo.");
        messageUtils.sendMessage(player, "&f/amigo remove <player> &7- Remove um amigo.");
        messageUtils.sendMessage(player, "&f/amigo permissions <player> &7- Gerencia as permissões de um amigo.");
    }
}
