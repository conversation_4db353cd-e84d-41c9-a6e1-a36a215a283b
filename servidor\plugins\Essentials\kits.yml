# EssentialsX kit configuration.
# If you don't have any kits defined in this file, the plugin will try to copy them from the config.yml

# Note: All items MUST be followed by a quantity!
# All kit names should be lower case, and will be treated as lower in permissions/costs.
# Syntax: - name[:durability] amount [enchantment:level]... [itemmeta:value]...
# For Item Meta information visit http://wiki.ess3.net/wiki/Item_Meta
# To make the kit execute a command, add /<command> to the items list. Use {USERNAME} to specify the player receiving the kit.
# {PLAYER} will show the player's displayname instead of username.
# 'delay' refers to the cooldown between how often you can use each kit, measured in seconds.
# Set delay to -1 for a one time kit.
# For more information, visit http://wiki.ess3.net/wiki/Kits
kits:
  tools:
    delay: 10
    items:
      - stonesword 1
      - stoneshovel 1
      - stonepickaxe 1
      - stoneaxe 1
  dtools:
    delay: 600
    items:
      - dpickaxe 1 efficiency:1 durability:1 fortune:1 name:&4Gigadrill lore:The_drill_that_&npierces|the_heavens
      - dshovel 1 digspeed:3 name:Dwarf lore:Diggy|Diggy|Hole
      - lhelm 1 color:255,255,255 name:Top_Hat lore:Good_day,_Good_day
      - daxe:780 1
      - /broadcast {USERNAME} just got some fancy tools!
  notch:
    delay: 6000
    items:
      - playerhead 1 player:Notch
  color:
    delay: 6000
    items:
      - writtenbook 1 title:&4Book_&9o_&6Colors author:KHobbits lore:Ingame_color_codes book:Colors
  firework:
    delay: 6000
    items:
      - fireworkrocket 1 name:Angry_Creeper color:red fade:green type:creeper power:1
      - fireworkrocket 1 name:Starry_Night color:yellow,orange fade:blue type:star effect:trail,twinkle power:1
      - fireworkrocket 2 name:Solar_Wind color:yellow,orange fade:red shape:large effect:twinkle color:yellow,orange fade:red shape:ball effect:trail color:red,purple fade:pink shape:star effect:trail power:1
