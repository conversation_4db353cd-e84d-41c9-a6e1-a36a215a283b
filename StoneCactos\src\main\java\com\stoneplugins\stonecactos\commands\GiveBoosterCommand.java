package com.stoneplugins.stonecactos.commands;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class GiveBoosterCommand implements CommandExecutor {
    
    private final StoneCactos plugin;
    
    public GiveBoosterCommand(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Verificar permissão
        if (!sender.hasPermission("stonecactos.admin")) {
            sender.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return true;
        }
        
        // Verificar argumentos
        if (args.length != 2) {
            sender.sendMessage(plugin.getConfigManager().getMessage("commands.givebooster.usage"));
            return true;
        }
        
        // Obter jogador alvo
        Player target = Bukkit.getPlayer(args[0]);
        if (target == null) {
            sender.sendMessage(plugin.getConfigManager().getMessage("commands.givebooster.playerNotFound"));
            return true;
        }
        
        // Obter ID do booster
        String boosterId = args[1].toUpperCase();
        
        // Verificar se o booster existe
        if (!plugin.getConfigManager().hasBooster(boosterId)) {
            sender.sendMessage(plugin.getConfigManager().getMessage("commands.givebooster.boosterNotFound"));
            return true;
        }
        
        // Criar item do booster
        ItemStack boosterItem = plugin.getItemManager().createBoosterItem(boosterId);
        
        // Adicionar ao inventário do jogador
        if (target.getInventory().firstEmpty() != -1) {
            target.getInventory().addItem(boosterItem);
        } else {
            // Inventário cheio, dropar no chão
            target.getWorld().dropItemNaturally(target.getLocation(), boosterItem);
        }
        
        // Enviar mensagens
        String boosterName = plugin.getConfigManager().getBoosterName(boosterId);
        
        String successMessage = plugin.getConfigManager().getMessage("commands.givebooster.success")
            .replace("%booster%", boosterName)
            .replace("%player%", target.getName());
        sender.sendMessage(successMessage);
        
        String receivedMessage = plugin.getConfigManager().getMessage("commands.givebooster.received")
            .replace("%booster%", boosterName);
        target.sendMessage(receivedMessage);
        
        return true;
    }
}
