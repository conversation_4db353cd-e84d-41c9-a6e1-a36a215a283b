worlds:
  world:
    ==: MVWorld
    hidden: 'false'
    alias: world
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
      currency: '-1'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: -202.5
      y: 64.0
      z: 251.5
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-6540004420419152680'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  world_the_end:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '16.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
      currency: '-1'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 66.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: THE_END
    seed: '-6540004420419152680'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  PlotWorld:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
      currency: '-1'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 65.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '-6814283378569707232'
    generator: PlotSquared
    playerLimit: '-1'
    allowFlight: 'true'
  world_nether:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '8.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
      currency: '-1'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 114.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NETHER
    seed: '-6540004420419152680'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
  pescaria:
    ==: MVWorld
    hidden: 'false'
    alias: ''
    color: WHITE
    style: NORMAL
    pvp: 'true'
    scale: '1.0'
    respawnWorld: ''
    allowWeather: 'true'
    difficulty: EASY
    spawning:
      ==: MVSpawnSettings
      animals:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
      monsters:
        ==: MVSpawnSubSettings
        spawn: 'true'
        spawnrate: '-1'
        exceptions: []
    entryfee:
      ==: MVEntryFee
      amount: '0.0'
      currency: '-1'
    hunger: 'true'
    autoHeal: 'true'
    adjustSpawn: 'true'
    portalForm: ALL
    gameMode: SURVIVAL
    keepSpawnInMemory: 'true'
    spawnLocation:
      ==: MVSpawnLocation
      x: 0.0
      y: 65.0
      z: 0.0
      pitch: 0.0
      yaw: 0.0
    autoLoad: 'true'
    bedRespawn: 'true'
    worldBlacklist: []
    environment: NORMAL
    seed: '3948779887129900390'
    generator: 'null'
    playerLimit: '-1'
    allowFlight: 'true'
