MySQL:
  host: "localhost"
  user: "root"
  password: ""
  database: "db"

SQLite:
  file: "database.db"

General:
  sqlType: SQLITE #SQLITE OR MYSQL
  economyPlugin: VAULT # VAULT, YPOINTS, PLAYERPOINTS, ATLASECONOMIASECUNDARIA, STORMECONOMIASECUNDARIA
  permissionsPlugin: PERMISSIONSEX # PERMISSIONSEX OU LUCKPERMS
  useLimite: true # Habilitar sistema de limites
  limitePadrao: "64"
  itemLimite:
    # MATERIAL : DATA OU URL DE UMA SKULL
    # OU %jogador% para a cabeça do jogador
    item: "NETHER_STAR:0"
    glow: true
    name: "&aLimite de compra"
    lore:
      - ''
      - '&f Quantidade: &a{quantidade}'
      - ''
      - '&aClique para ativar'

Descontos:
  - 'desconto1.usar:10.0:&6Ouro'

Spawners:
  cow:
    title: "&eVaca"
    price: 30000
    skull: "5d6c6eda942f7f5f71c3161c7306f4aed307d82895f9d2b07ab4525718edc5"
    command: "spawneradmin give {player} cow {quantidade}"
    permission: "" # Deixa nulo se não quiser usar
    liberaAs: "" #Coloque a data em que o spawner sera liberado ou deixe nulo caso não for usar
    rankNecessario: "" #Compativel apenas com o StormRankUP | Coloque o id do rank ou deixe nulo caso não for usar

  skeleton:
    title: "&eEsqueleto"
    price: 65000
    skull: "4a35e76492d42d72cf56dfc4b7d5347d48cc88940dfd6a9d6235bc718f4c6bab"
    command: "spawneradmin give {player} skeleton {quantidade}"
    permission: "" # Deixa nulo se não quiser usar
    liberaAs: "23/07/2025 22:50" #Coloque a data em que o spawner sera liberado ou deixe nulo caso não for usar

  zombie:
    title: "&eZombie"
    price: 60000
    skull: "56fc854bb84cf4b7697297973e02b79bc10698460b51a639c60e5e417734e11"
    command: "spawneradmin give {player} zombie {quantidade}"
    permission: "" # Deixa nulo se não quiser usar
    liberaAs: "24/07/2025 20:20" #Coloque a data em que o spawner sera liberado ou deixe nulo caso não for usar
    rankNecessario: "" #Compativel apenas com o StormRankUP | Coloque o id do rank ou deixe nulo caso não for usar

Mensagens:
  spawnercomprado: "&a&l✓ COMPRA REALIZADA! &fSeu gerador foi adicionado ao inventario."
  semsaldo: "&c&l✖ SALDO INSUFICIENTE! &fVoce nao possui coins suficientes."
  limiteatingido: "&c&l✖ LIMITE ATINGIDO! &fVoce atingiu seu limite de compras."
  ativoulimite: "&a&l✓ LIMITE ATIVADO! &fSeu limite foi aumentado em &6{limite}&f."
  seulimite: "&e&l⚡ SEU LIMITE: &f{limite} geradores"
  spawnersinsuficiente: "&c&l✖ COINS INSUFICIENTES! &fVoce precisa de mais coins."
  multiplicadoralterado: "&a&l✓ MULTIPLICADOR ALTERADO! &fNovo valor: &d{multiplicador}x"
  multiplicadorinvalido: "&c&l✖ VALOR INVALIDO! &fUse um numero entre 1 e {maximo}."
  quantidadeSpawners:
    - ""
    - "&e&l⚡ ESCOLHA A QUANTIDADE:"
    - "&7Digite no chat quantos geradores deseja comprar."
    - "&7Seu limite atual: &a{limite} &7| Restante: &b{restante}"
    - ""
    - "&a&l▶ &fPara cancelar digite: &c'cancelar'"
    - ""
  multiplicadorEscolha:
    - ""
    - "&e&l⚙ ALTERAR MULTIPLICADOR:"
    - "&7Digite o novo valor do multiplicador (1x a {maximo}x)."
    - "&7Multiplicador atual: &d{atual}x"
    - ""
    - "&a&l▶ &fPara cancelar digite: &c'cancelar'"
    - ""

Menus:
  main:
    name: "&8&l[&6&lLOJA&8&l] &f&lGeradores"
    size: 54
    arrowBackPage: 45
    arrowNextPage: 53
    slots: "19,20,21,22,23,24,25,28,29,30,31,32,33,34,37,38,39,40,41,42,43"
    autoComplete: true
    decoracao:
      vidro:
        item: "STAINED_GLASS_PANE:7"
        name: " "
        slots: "0,1,2,6,7,8,9,17,18,26,27,35,36,44,45,46,47,48,49,50,51,52,53"
      borda:
        item: "STAINED_GLASS_PANE:15"
        name: " "
        slots: "3,4,5,10,11,12,13,14,15,16"
    spawners:
      lore:
        - ''
        - '&f&l▶ &7Preco: &a{preco} coins'
        - '&f&l▶ &7Grupo: &b{grupos}'
        - '&f&l▶ &7Desconto: &e{desconto}%'
        - '&f&l▶ &7Pode comprar: &d{geradores}x'
        - ''
        - '&a&l▶ COMO COMPRAR:'
        - '&f  &7• &fBotao Esquerdo: &aEscolher quantidade'
        - '&f  &7• &fBotao Direito: &aComprar 1x'
        - '&f  &7• &fTecla Q: &aComprar maximo'
        - ''
        - '{status}'
    spawnerUnavailable:
      item: "BARRIER"
      name: "&c&l✖ INDISPONIVEL"
      lore:
        - ''
        - '&f&l▶ &7Preco: &c-/-'
        - '&f&l▶ &7Grupo: &c-/-'
        - '&f&l▶ &7Desconto: &c0%'
        - ''
        - '&c&l⚠ Este gerador sera liberado em breve!'
        - '&7Aguarde a liberacao para poder comprar.'
        - ''
    profile:
      slot: 4
      item: "WOOL:0"
      name: "&e&l⚙ MEU PERFIL"
      lore:
        - ''
        - '&f&l▶ &7Limite de compra: &a{limite}'
        - '&f&l▶ &7Compras realizadas: &b{compras}'
        - '&f&l▶ &7Multiplicador: &d{multiplicador}x'
        - '&f&l▶ &7Descontos ativos: &e{descontos}'
        - ''
        - '&a&l▶ INFORMACOES:'
        - '&7Apos adquirir seus geradores, coloque-os'
        - '&7em sua ilha para comecar a farmar!'
        - '&7Ganhe dinheiro e se torne o mais rico!'
        - ''
        - '&e&l⚡ Clique para alterar multiplicador!'
    ranking:
      slot: 5
      item: "GOLD_INGOT"
      name: "&6&l★ TOP COMPRADORES"
      lore:
        - ''
        - '&7Veja o ranking dos maiores'
        - '&7compradores do servidor!'
        - ''
        - '&e&l⚡ Clique para abrir!'
  ranking:
    name: "&8&l[&6&lTOP&8&l] &f&lCompradores"
    size: 45
    backArrow: 40
    slots: "10,11,12,13,14,15,16,19,20,21,22,23,24,25"
    autoComplete: true
    decoracao:
      vidro:
        item: "STAINED_GLASS_PANE:7"
        name: " "
        slots: "0,1,2,3,4,5,6,7,8,9,17,18,26,27,35,36,37,38,39,41,42,43,44"
      ordenacao:
        slot: 4
        item: "COMPARATOR"
        name: "&e&l⚙ ORDENACAO"
        lore:
          - ''
          - '&7Clique para alterar a ordenacao:'
          - '&f• &aLimite de compra'
          - '&f• &bGeradores comprados'
          - ''
          - '&e&l⚡ Clique para alternar!'
    rank:
      name: "&f&l{pos}º &7{grupo} &f{jogador}"
      lore:
        - ''
        - '&f&l▶ &7Jogador: &b{jogador}'
        - '&f&l▶ &7Compras: &a{compras}'
        - '&f&l▶ &7Limite: &d{limite}'
        - '&f&l▶ &7Posicao: &e#{pos}'
        - ''
    voltar:
      slot: 40
      item: "ARROW"
      name: "&c&l← VOLTAR"
      lore:
        - ''
        - '&7Clique para voltar ao'
        - '&7menu principal da loja.'
        - ''
