package com.atlasplugins.pescaria.commands;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.gui.LojaGUI;
import com.atlasplugins.pescaria.gui.PescaGUI;
import com.atlasplugins.pescaria.models.Mission;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.World;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.UUID;

public class PescaCommand implements CommandExecutor {

    private final Pescaria plugin;
    private final PescaGUI pescaGUI;

    public PescaCommand(Pescaria plugin) {
        this.plugin = plugin;
        this.pescaGUI = new PescaGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cEste comando só pode ser executado por jogadores.");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Comando /pesca - Menu principal
            showMainMenu(player);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "definir":
                // Comando /pesca definir - Define o spawn do mundo de pesca
                if (!player.hasPermission("pescaria.definir")) {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão para executar este comando.");
                    return true;
                }
                setFishingSpawn(player);
                break;

            case "spawn":
                // Comando /pesca spawn - Define o spawn do servidor
                if (!player.hasPermission("pescaria.spawn")) {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão para executar este comando.");
                    return true;
                }
                setServerSpawn(player);
                break;

            case "tp":
            case "teleportar":
                // Comando /pesca tp - Teleporta para o mundo de pesca
                teleportToFishing(player);
                break;

            case "voltar":
                // Comando /pesca voltar - Volta para o spawn do servidor
                teleportToSpawn(player);
                break;

            case "info":
                // Comando /pesca info - Mostra informações sobre o plugin
                showInfo(player);
                break;

            case "loja":
                // Comando /pesca loja - Abre a loja de pesca
                openShopMenu(player);
                break;

            case "help":
            case "ajuda":
                // Comando /pesca help - Mostra ajuda
                showHelp(player);
                break;

            case "scoreboard":
            case "sb":
                // Comando /pesca scoreboard - Força atualização do scoreboard
                plugin.getScoreboardManager().updateScoreboard(player);
                player.sendMessage("§a§lPESCARIA §fScoreboard atualizado!");
                break;

            case "reset":
                // Comando /pesca reset - Resetar encantamentos (apenas para teste)
                if (player.hasPermission("pescaria.admin")) {
                    resetPlayerEnchantments(player);
                } else {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão para usar este comando!");
                }
                break;

            case "setpeixes":
                // Comando /pesca setpeixes <jogador> <quantidade>
                if (!player.hasPermission("pesca.setpeixes")) {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão!");
                    return true;
                }
                handleSetPeixes(player, args);
                break;

            case "addpeixes":
                // Comando /pesca addpeixes <jogador> <quantidade>
                if (!player.hasPermission("pesca.addpeixes")) {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão!");
                    return true;
                }
                handleAddPeixes(player, args);
                break;

            case "givebooster":
                // Comando /pesca givebooster <jogador> <booster> <quantidade>
                if (!player.hasPermission("pesca.givebooster")) {
                    player.sendMessage("§c§lPESCARIA §fVocê não tem permissão!");
                    return true;
                }
                handleGiveBooster(player, args);
                break;

            default:
                player.sendMessage("§c§lPESCARIA §fComando desconhecido. Use /pesca para ver os comandos disponíveis.");
                break;
        }

        return true;
    }

    private void showMainMenu(Player player) {
        pescaGUI.openMainMenu(player);
    }

    private void setFishingSpawn(Player player) {
        World world = player.getWorld();
        String pescariaWorldName = plugin.getConfigManager().getPescariaWorldName();

        if (!world.getName().equalsIgnoreCase(pescariaWorldName)) {
            player.sendMessage("§c§lPESCARIA §fVocê precisa estar no mundo de pesca para definir o spawn.");
            player.sendMessage("§c§lPESCARIA §fMundo de pesca: §e" + pescariaWorldName);
            return;
        }

        Location location = player.getLocation();
        plugin.getConfigManager().setFishingSpawnLocation(location);
        player.sendMessage("§a§lPESCARIA §fSpawn da área de pesca definido com sucesso!");
    }

    private void setServerSpawn(Player player) {
        Location location = player.getLocation();
        plugin.getConfigManager().setServerSpawnLocation(location);
        player.sendMessage("§a§lPESCARIA §fSpawn do servidor definido com sucesso!");
    }

    private void teleportToFishing(Player player) {
        Location fishingSpawn = plugin.getConfigManager().getFishingSpawnLocation();

        if (fishingSpawn == null) {
            player.sendMessage("§c§lPESCARIA §fO spawn da área de pesca ainda não foi definido.");
            player.sendMessage("§c§lPESCARIA §fPeça para um administrador definir usando /pesca definir");
            return;
        }

        player.teleport(fishingSpawn);
        player.sendMessage("§a§lPESCARIA §fVocê foi teleportado para a área de pesca!");
    }

    private void teleportToSpawn(Player player) {
        Location serverSpawn = plugin.getConfigManager().getServerSpawnLocation();

        if (serverSpawn == null) {
            player.sendMessage("§c§lPESCARIA §fO spawn do servidor ainda não foi definido.");
            player.sendMessage("§c§lPESCARIA §fPeça para um administrador definir usando /pesca spawn");
            return;
        }

        player.teleport(serverSpawn);
        player.sendMessage("§a§lPESCARIA §fVocê foi teleportado para o spawn do servidor!");
    }

    private void showInfo(Player player) {
        player.sendMessage("§a§l=== PESCARIA ===§r");
        player.sendMessage("§fPlugin desenvolvido por §eAtlasPlugins");
        player.sendMessage("§fVersão: §e" + plugin.getDescription().getVersion());
        player.sendMessage("§fMundo de pesca: §e" + plugin.getConfigManager().getPescariaWorldName());
        player.sendMessage("§fTotal de missões: §e" + plugin.getMissionManager().getTotalMissions());
        player.sendMessage("§fSite: §eloja.atlasplugins.com");
    }

    private void showHelp(Player player) {
        player.sendMessage("§a§l=== COMANDOS PESCARIA ===§r");
        player.sendMessage("§f/pesca §7- Abrir menu principal");
        player.sendMessage("§f/pesca tp §7- Teleportar para pescaria");
        player.sendMessage("§f/pesca voltar §7- Voltar ao spawn");
        player.sendMessage("§f/pesca loja §7- Abrir loja de pesca");
        player.sendMessage("§f/pesca info §7- Informações do plugin");
        player.sendMessage("§f/pesca help §7- Mostrar esta ajuda");

        if (player.hasPermission("pesca.admin")) {
            player.sendMessage("§c§l=== COMANDOS ADMIN ===§r");
            player.sendMessage("§f/pesca setpeixes <jogador> <qtd> §7- Definir peixes");
            player.sendMessage("§f/pesca addpeixes <jogador> <qtd> §7- Adicionar peixes");
            player.sendMessage("§f/pesca givebooster <jogador> <tipo> <qtd> §7- Dar booster");
            player.sendMessage("§f/pesca definir §7- Definir spawn de pesca");
            player.sendMessage("§f/pesca spawn §7- Definir spawn do servidor");
        }
    }

    private void openShopMenu(Player player) {
        // Usar a nova LojaGUI
        LojaGUI lojaGUI = new LojaGUI(plugin);
        lojaGUI.openLojaMenu(player);
    }

    private void handleSetPeixes(Player player, String[] args) {
        if (args.length != 3) {
            player.sendMessage("§c§lPESCARIA §fUso correto: /pesca setpeixes <jogador> <quantidade>");
            return;
        }

        String targetName = args[1];
        Player target = plugin.getServer().getPlayer(targetName);

        if (target == null) {
            player.sendMessage("§c§lPESCARIA §fJogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(args[2]);
            plugin.getPlayerManager().setPlayerFishes(target.getUniqueId(), amount);
            plugin.getPlayerManager().savePlayer(target.getUniqueId());

            player.sendMessage(
                    "§a§lPESCARIA §fVocê definiu §e" + amount + " §fpeixes para §e" + target.getName() + "§f!");
            target.sendMessage("§a§lPESCARIA §fSeus peixes foram definidos para §e" + amount + "§f!");
        } catch (NumberFormatException e) {
            player.sendMessage("§c§lPESCARIA §fQuantidade inválida!");
        }
    }

    private void handleAddPeixes(Player player, String[] args) {
        if (args.length != 3) {
            player.sendMessage("§c§lPESCARIA §fUso correto: /pesca addpeixes <jogador> <quantidade>");
            return;
        }

        String targetName = args[1];
        Player target = plugin.getServer().getPlayer(targetName);

        if (target == null) {
            player.sendMessage("§c§lPESCARIA §fJogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(args[2]);
            int currentFishes = plugin.getPlayerManager().getPlayerFishes(target.getUniqueId());
            plugin.getPlayerManager().setPlayerFishes(target.getUniqueId(), currentFishes + amount);
            plugin.getPlayerManager().savePlayer(target.getUniqueId());

            player.sendMessage(
                    "§a§lPESCARIA §fVocê adicionou §e" + amount + " §fpeixes para §e" + target.getName() + "§f!");
            target.sendMessage("§a§lPESCARIA §fVocê recebeu §e" + amount + " §fpeixes!");
        } catch (NumberFormatException e) {
            player.sendMessage("§c§lPESCARIA §fQuantidade inválida!");
        }
    }

    private void handleGiveBooster(Player player, String[] args) {
        if (args.length != 4) {
            player.sendMessage("§c§lPESCARIA §fUso correto: /pesca givebooster <jogador> <booster> <quantidade>");
            return;
        }

        String targetName = args[1];
        String boosterType = args[2];

        Player target = plugin.getServer().getPlayer(targetName);

        if (target == null) {
            player.sendMessage("§c§lPESCARIA §fJogador não encontrado!");
            return;
        }

        try {
            int amount = Integer.parseInt(args[3]);

            // TODO: Implementar sistema de boosters
            player.sendMessage("§a§lPESCARIA §fVocê deu §e" + amount + " §fbooster(s) §e" + boosterType + " §fpara §e"
                    + target.getName() + "§f!");
            target.sendMessage("§a§lPESCARIA §fVocê recebeu §e" + amount + " §fbooster(s) §e" + boosterType + "§f!");
        } catch (NumberFormatException e) {
            player.sendMessage("§c§lPESCARIA §fQuantidade inválida!");
        }
    }

    private void resetPlayerEnchantments(Player player) {
        UUID uuid = player.getUniqueId();

        // Resetar dados no config
        plugin.getConfigManager().getDataConfig().set("Players." + uuid.toString(), null);
        plugin.getConfigManager().saveDataConfig();

        // Remover vara atual
        for (int i = 0; i < player.getInventory().getSize(); i++) {
            ItemStack item = player.getInventory().getItem(i);
            if (item != null && item.getType() == Material.FISHING_ROD) {
                player.getInventory().setItem(i, null);
            }
        }

        player.sendMessage("§a§lPESCARIA §fTodos os seus dados foram resetados!");
        player.sendMessage("§e§lPESCARIA §fSaia e entre no mundo de pesca para receber uma nova vara!");
    }
}