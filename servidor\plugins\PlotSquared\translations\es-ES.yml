confirm:
  failed_confirm: $2No tienes acciones pendientes por confirmar.
  requires_confirm: '$2¿Estas seguro de realizar este comando?: $1%s$2?&-$2¡No hay vuelta atras! Si estas seguro escribe: $1/plot confirm'
  expired_confirm: $2La confirmación ha expirado, por favor ejecute el comando otra vez!
move:
  move_success: $4La parcela fue movida correctamente.
  copy_success: $4La parcela fue copiada correctamente.
  requires_unowned: $2La localizacion especificada ya esta ocupada.
web:
  generating_link: $1Procesando parcela...
  generating_link_failed: $2¡Fallo al generar un link de descarga!
  save_failed: $2Fallo al guardar.
  load_null: $2Por favor usa $4/plot load $2para conseguir una schematic.
  load_failed: $2Fallo al cargar schematic.
  load_list: '$2Para cargar una schematic usa $1/plot load #'
  save_success: $1¡Guardado completado!
compass:
  compass_target: $4Parcela seleccionada con la brujula completada.
cluster:
  cluster_available_args: '$1Los siguientes subcomandos estan disponibles: $4list$2,
    $4create$2, $4delete$2, $4resize$2, $4invite$2, $4kick$2, $4leave$2, $4members$2,
    $4info$2, $4tp$2, $4sethome'
  cluster_list_heading: $2Hay $1%s$2 agrupaciones en este mundo.
  cluster_list_element: $2 - $1%s&-
  cluster_intersection: $2El area propuesta se solapa con $1%s$2 de la parcela existente.
  cluster_added: $4Creacion de agrupaciones completado.
  cluster_deleted: $4Borrado de agrupaciones completado.
  cluster_resized: $4Reescalado de agrupaciones completado.
  cluster_added_user: $4Añadido usuario a la agrupacion con exito.
  cannot_kick_player: $2Tu no puedes expulsar a este jugador.
  cluster_invited: '$1Has sido invitado a la siguiente agrupacion: $2%s'
  cluster_removed: '$1Has sido eliminado de la agrupacion: $2%s'
  cluster_kicked_user: $4Usuario expulsado con exito.
  invalid_cluster: '$1Nombre de agrupacion invalido: $2%s'
  cluster_not_added: $2Este jugador no fue añadido a la agrupacion de parcelas.
  cluster_cannot_leave: $1Debes eliminar o transferir el dueño antes de salir.
  cluster_added_helper: $4Ayudante añadido a la agrupacion con exito.
  cluster_removed_helper: $4Ayudante eliminado de la agrupacion con exito.
  cluster_regenerated: $4Regeneracion de la agrupacion comenzada con exito.
  cluster_teleporting: $4Teletransportando...
  cluster_info: '$1Agrupacion actual: $2%id%&-$1Nombre: $2%name%&-$1Propietario: $2%owner%&-$1Tamaño:
    $2%size%&-$1Derechos: $2%rights%'
  cluster_outside: '$2El área propuesta está fuera del área de la parcela: %s0'
border:
  border: $2Estas en las afueras del borde del mapa.
unclaim:
  unclaim_success: $4Desclaimeaste la parcela correctamente.
  unclaim_failed: $2No se pudo desclaimear la parcela.
worldedit masks:
  worldedit_delayed: $2Por favor espere mientras se procesa tu accion de WorldEdit...
  worldedit_run: '$2Sentimos la tardanza. Ejecutando: %s'
  require_selection_in_mask: $2%s la seleccion no esta en tu parcela. Solo puedes editar la zona de tu parcela.
  worldedit_volume: $2No puedes seleccionar el volumen de %current%. El volumen maximo de modificacion es %max%.
  worldedit_iterations: $2No puedes repetir %current% veces. El límite es %max%.
  worldedit_unsafe: $2El acceso a ese comando ha sido bloqueado.
  worldedit_bypass: $2&oPara evitar las restrinciones usa $4/plot wea
  worldedit_bypassed: $2 Sobrepasando las restrincciones de WorlEdit.
  worldedit_unmasked: $1El WorldEdit desactivo tus restricciones. Tus permisos de WorldEdit son ilimitados.
  worldedit_restricted: $1Tus permisos de WorldEdit estan restringidos.
gamemode:
  gamemode_was_bypassed: $1Has omitido el modo de juego ($2{gamemode}$1) $1set for $2{plot}
height limit:
  height_limit: $1Esta parcela tiene un limite de altura de $2{limit}
records:
  record_play: $2%player $2comenzando a registrar la parcela $1%name
  notify_enter: $2%player $2introduce tu parcela ($1%plot$2)
  notify_leave: $2%player $2abandona tu parcela ($1%plot$2)
swap:
  swap_overlap: $2Las áreas propuestas no pueden superponerse.
  swap_dimensions: $2El area propuesta debe tener dimensiones comparables.
  swap_syntax: $2/plot swap <plot id>
  swap_success: $4Intercambio de parcelas completado
  started_swap: $2 Intercambiando las tareas de las parcelas. Serás notificado cuando el proceso haya terminado.
comment:
  inbox_notification: '%s Mensajes no leidos. Usa /plot inbox'
  not_valid_inbox_index: $2No hay comentarios %s
  inbox_item: $2 - $4%s
  comment_syntax: $2Usa /plot comment [X;Z] <%s> <comment>
  invalid_inbox: '$2Esta no es una bandeja de entrada valida.&-$1Los valores aceptados: %s'
  no_perm_inbox: $2No tienes permisos para ingresar a esta bandeja de entrada.
  no_perm_inbox_modify: $2No tienes permisos para modificar esta bandeja de entrada.
  no_plot_inbox: $2Debes permanecer en la parcela para poder escribir el comentario.
  comment_removed: $4Comentario eliminado satisfactoriamente/s:n$2 - '$3%s$2'
  comment_added: $4Te han dejado un comentario.
  comment_header: $2&m---------&r $1Comentarios $2&m---------&r
  inbox_empty: $2No hay comentarios.
console:
  not_console: $2Por razones de seguridad, este comando solo se puede ejecutar desde la consola.
  is_console: $2Este comando solo puede ser ejecutado por los jugadores.
inventory:
  inventory_usage: '&cUso: &6{usage}'
  inventory_desc: '&cDescripcion: &6{desc}'
  inventory_category: '&cCategoria: &6{category}'
clipboard:
  clipboard_set: $2La parcela actual fue copiada a tu portapapeles, usa $1/plot paste$2 para copiarla.
  pasted: $4La seleccion de la parcela ha sido copiada correctamente.
  paste_failed: '$2Fallo al copiar la seleccion. Razon: $2%s'
  no_clipboard: $2No tienes nada seleccionado en el portapapeles.
  clipboard_info: '$2Seleccion actual - Plot ID: $1%id$2, Ancho: $1%width$2, Bloques totales: $1%total$2'
toggle:
  toggle_enabled: '$2Ajustes activados : %s'
  toggle_disabled: '$2Ajustes desactivados : %s'
blocked command:
  command_blocked: $2El comando no esta permitido en esta parcela.
done:
  done_already_done: $2Esta parcela esta finalizada.
  done_not_done: $2Esta parcela no esta finalizada.
  done_insufficient_complexity: $2Esta parcela es muy simple. Porfavor añade mas detalles antes de usar este comando.
  done_success: $1Parcela finalizada con exito.
  done_removed: $1Ya puedes continuar construyendo en esta parcela.
ratings:
  rating_not_valid: $2Tienes que especificar un número entre 1 y 10.
  rating_already_exists: $2Ya has valorado la parcela. $2%s
  rating_applied: $4Has valorado correctamente la parcela. $2%s
  rating_not_your_own: $2No puedes valorar tu propia parcela.
  rating_not_done: $2Solo puedes valorar parcelas terminadas.
  rating_not_owned: $2No puedes valorar una parcela que no pertenece a nadie.
  ratings_purged: $2Calificaciones purgadas para esta parcela
tutorial:
  rate_this: $2¡Valora esta parcela!
  comment_this: '$2Deja tu opinion de esta parcela: %s'
economy:
  econ_disabled: $2La economia no esta activada.
  cannot_afford_plot: $2No te puedes permitir comprar esta parcela. Cuesta $1%s
  not_for_sale: $2Esta parcela no esta a la venta.
  cannot_buy_own: $2No puedes comprar tu propia parcela.
  plot_sold: $4Tu parcela; $1%s0$4, se ha vendido por $1%s1$4 para $1$%s2
  cannot_afford_merge: $2Tu no puedes permitirte combinar las parcelas. Cuesta $1%s
  added_balance: $1%s $2 fue añadido a tu fondo de dinero.
  removed_balance: $1%s $2 fue removido de tu fondo de dinero.
  removed_granted_plot: $2Usaste %s concesiones de trama, te quedan 5 $1%s
setup:
  setup_init: '$1Uso: $2/plot setup <cantidad>'
  setup_step: '$3[$1Step %s0$3] $1%s1 $2- $1Espectando: $2%s2 $1Default: $2%s3'
  setup_invalid_arg: '$2%s0 no es un argumento valido para este paso %s1. Para cancelar la configuracion usa: $1/plot setup cancel'
  setup_valid_arg: $2Valor $1%s0 $2ajustado a %s1
  setup_finished: $4Deberias haber sido teletransportado al mundo creado, sino tendras que generarlo manualmente usando el bukkit.yml
  setup_world_taken: $2%s este mundo ya esta registrado.
  setup_missing_world: $2 Necesitas especificar el nombre del mundo ($1/plot setup
    &l<world>$1 <generator>$2)&-$1Comandos adicionales:&-$2 - $1/plot setup <value>&-$2
    - $1/plot setup back&-$2 - $1/plot setup cancel
  setup_missing_generator: $2Tienes que especificar un generador ($1/plot setup <world>
    &l<generator>&r$2)&-$1Comandos adicionales:&-$2 - $1/plot setup <value>&-$2 - $1/plot
    setup back&-$2 - $1/plot setup cancel
  setup_invalid_generator: '$2Generador inválido. Opciones posibles: %s'
schematics:
  schematic_missing_arg: '$2Necesitas especificar un argumento. Valores posibles:
    $1test <name>$2 , $1save$2 , $1paste $2, $1exportall'
  schematic_invalid: '$2Este no es un schematic valido. Razon: $2%s'
  schematic_valid: $2Este es un schematic valido.
  schematic_paste_failed: $2Fallo al pegar el schematic.
  schematic_paste_success: $4El schematic ha sido copiado correctamente.
  schematic_too_large: $2¡La trama es demasiado grande para esta acción!
titles:
  title_entered_plot: '$1Parcela: %world%;%x%;%z%'
  title_entered_plot_sub: $4Propiedad de %s
  prefix_greeting: '$1%id%$2> '
  prefix_farewell: '$1%id%$2> '
core:
  task_start: Comenzando la tarea...
  prefix: $3[$1P2$3] $2
  enabled: $1PlotSquared esta activado.
reload:
  reloaded_configs: $1Las traducciones y opciones del mundo han sido cargados.
  reload_failed: $2Fallo al cargar las configuraciones de los archivos.
desc:
  desc_set: $2Descripcion de la parcela.
  desc_unset: $2Descripcion de la parcela no especificada.
  missing_desc: $2Necesitas especificar una descripcion.
alias:
  alias_set_to: $2Alias de la parcela establecido en $1%alias%
  missing_alias: $2Necesitas especificar un alias.
  alias_too_long: $2El alias debe ser < de 50 caracteres
  alias_is_taken: $2Ese alias ya ha sido utilizado.
  alias_removed: $2Parcela alias eliminado
position:
  missing_position: '$2Necesitas especificar una posicion. Valores posibles: $1none'
  position_set: $1Hogar establecido en tu posicion actual.
  position_unset: $1Hogar reseteado a la posicion por defecto.
  home_argument: $2Usa /plot set home [none]
  invalid_position: $2Valor de posicion no valida.
time:
  time_format: $1%hours%, %min%, %sec%
permission:
  no_schematic_permission: $2No tienes permisos para usar schematic $1%s
  no_permission: '$2Careces de los permisos de este nodo: $1%s'
  no_permission_event: '$2Careces de los permisos de este nodo: $1%s'
  no_plot_perms: $2Tienes que ser el dueño de la parcela para realizar esta accion.
  cant_claim_more_plots: $2No puedes claimear mas parcelas.
  cant_transfer_more_plots: $2No puedes enviar mas parcelas a ese usuario.
  cant_claim_more_plots_num: $2No puedes claimear mas de $1%s $2parcelas a la vez.
  you_be_denied: $2No tienes permitido entrar en esta parcela.
  merge_request_confirm: Peticion de agrupamiento de %s
  cant_claim_more_clusters: $2No puedes reclamar más grupos.
merge:
  merge_not_valid: $2La peticion de agrupacion ya no es valida.
  merge_accepted: $2La peticion de agrupacion ha sido aceptada.
  success_merge: $2¡Las parcelas se han agrupado!
  merge_requested: $2Se ha enviado con exito la peticion de agrupar.
  no_perm_merge: '$2No eres el dueño de la parcela: $1%plot%'
  no_available_automerge: $2 No eres propietario de ninguna parcela adyacente en la direccion especificada o no tienes permitido agrupar el tamaño requerido.
  unlink_required: $2Se requiere una desconexion para hacer esto.
  unlink_impossible: $2Solo puedes desconectar una mega-parcela.
  unlink_success: $2Se han desconectado las parcelas.
errors:
  invalid_player_wait: '$2Jugador no encontrado: $1%s$2, buscando. Prueba mas tarde.'
  invalid_player: '$2Jugador no encontrado: $1%s$2.'
  invalid_player_offline: '$2El jugador debe estar conectado: $1%s.'
  command_went_wrong: $2Algo ocurrio ejecutando este comando...
  no_free_plots: $2No hay parcelas libres disponibles.
  not_in_plot: $2No estas en ninguna parcela.
  not_in_cluster: $2Debes tener una agrupacion de parcelas para realizar esta accion.
  not_in_plot_world: $2No estas en el mundo de parcelas.
  plotworld_incompatible: $2Los dos mundos deben ser compatibles.
  not_valid_world: $2Esto no es un mundo valido. (caso sensitivo)
  not_valid_plot_world: $2Esta no es una parcela valida. (caso sensitivo)
  no_plots: $2No tienes ninguna parcela.
  wait_for_timer: $2El temporizador del selector de bloque esta ligado a ti o a la parcela actual. Por favor espere a que termine.
  invalid_command_flag: '$2Indicador de comando no válido: %s0'
  error: '$2Ocurrió un error: %s'
  not_loaded: $2No se pudo cargar la trama
paste:
  debug_report_created: '$1Se ha enviado una depuracion a: $1%url%'
purge:
  purge_success: $4Se ha limpiado %s parcelas.
trim:
  trim_in_progress: La tarea de recorte esta en proceso!
  not_valid_hybrid_plot_world: Es necesario el dueño de las parcelas para realizar esta accion.
block list:
  block_list_separater: '$1,$2 '
biome:
  need_biome: $2Necesitas especificar un bioma valido.
  biome_set_to: $2El bioma de la parcela ha sido cambiado a $2
teleport:
  teleported_to_plot: $1Has sido teletransportado.
  teleported_to_road: $2Has sido teletransportado a la carretera.
  teleport_in_seconds: $1Teletransporte en %s segundos. No te muevas...
  teleport_failed: $2Teletransporte cancelado debido a movimiento o daño.
set block:
  set_block_action_finished: $1La ultima accion de seleccion de bloque se ha finalizado.
unsafe:
  debugallowunsafe_on: $2Acciones inseguras habilitadas.
  debugallowunsafe_off: $2Acciones inseguras deshabilitadas.
debug:
  debug_header: $1Informacion de Debug&-
  debug_section: $2>> $1&l%val%
  debug_line: $2>> $1%var%$2:$1 %val%&-
invalid:
  not_valid_data: $2No es una informacion de id valida.
  not_valid_block: '$2No es un bloque valido: %s'
  not_allowed_block: '$2Ese bloque no esta permitido: %s'
  not_valid_number: '$2No es un numero valido en el rango: %s'
  not_valid_plot_id: $2No es una id de parcela valida.
  plot_id_form: '$2La id de la parcela tiene que estar de manera: $1X;Y $2e.g. $1-5;7'
  not_your_plot: $2Esa no es tu parcela.
  no_such_plot: $2No existe dicha parcela.
  player_has_not_been_on: $2Este usuario no ha estado en el mundo de parcelas.
  found_no_plots: $2No se ha encontrado parcelas con esta informacion.
  found_no_plots_for_player: '$2No se encontraron parcelas para jugador: %s'
camera:
  camera_started: $2Has entrado en modo camara para la parcela $1%s
  camera_stopped: $2Ya no estas en modo camara.
need:
  need_plot_number: $2Necesitas especificar un alias o numero de parcela.
  need_block: $2Necesitas especificar un bloque.
  need_plot_id: $2Necesitas especificar la id de una parcela.
  need_plot_world: $2Necesitas especificar un mundo de parcelas.
  need_user: $2Necesitas especificar un usuario.
info:
  none: Ninguno
  unknown: Desconocido
  everyone: Todos
  plot_unowned: $2La parcela actual tiene que tener un propietario para ejecutar esa accion.
  plot_info_unclaimed: $2Parcela $1%s$2 aun no esta claimeada.
  plot_info_header: $3&m---------&r $1INFO $3&m---------
  plot_info: '$1ID: $2%id%$1&-$1Alias: $2%alias%$1&-$1Propietario: $2%owner%$1&-$1Bioma:
    $2%biome%$1&-$1Puede construir: $2%build%$1&-$1Clasificacion: $2%rating%&-$1Confianza:
    $2%trusted%$1&-$1Miembros: $2%members%$1&-$1Denegados: $2%denied%$1&-$1Flags:
    $2%flags%'
  plot_info_footer: $3&m---------&r $1INFO $3&m---------
  plot_info_trusted: $1Confianza:$2 %trusted%
  plot_info_members: $1Miembros:$2 %members%
  plot_info_denied: $1Denegado:$2 %denied%
  plot_info_flags: $1Flags:$2 %flags%
  plot_info_biome: $1Bioma:$2 %biome%
  plot_info_rating: $1Clasificacion:$2 %rating%
  plot_info_owner: $1Propietario:$2 %owner%
  plot_info_id: $1ID:$2 %id%
  plot_info_alias: $1Alias:$2 %alias%
  plot_info_size: $1Tamaño:$2 %size%
  plot_user_list: ' $1%user%$2,'
  info_syntax_console: $2/plot info X;Y
  now: Ahora
  never: Nunca
  plot_info_seen: $1Seen:$2 %seen%
  plot_flag_list: $1%s0:%s1$2
working:
  generating_component: $1Empezando a generar componentes desde tus opciones.
  clearing_plot: $2Limpiando parcela async.
  clearing_done: $4¡Limpieza completada! Ha tardado %sms.
  plot_not_claimed: $2Parcela no reclamada.
  plot_is_claimed: $2Esta parcela ya esta reclamada.
  claimed: $4Has reclamado con exito esta parcela.
  deleting_done: $4Eliminar completado! Tomó %sms.
list:
  comment_list_header_paged: $2(Page $1%cur$2/$1%max$2) $1Lista de %amount% comentarios.
  clickable: ' (interactive)'
  plot_list_header_paged: $2(Page $1%cur$2/$1%max$2) $1Lista de %amount% parcelas.
  plot_list_header: $1Lista de %word% parcelas.
  plot_list_item: $2>> $1%id$2:$1%world $2- $1%owner.
  plot_list_item_ordered: $2[$1%in$2] >> $1%id$2:$1%world $2- $1%owner.
  plot_list_footer: $2>> $1%word% un total $2%num% $1claimeado %plot%.
  area_list_header_paged: $2(Página $1%cur$2/$1%max$2) $1lista de %amount% areas
left:
  left_plot: $2Has abandonado una parcela.
chat:
  plot_chat_format: '$2[$1Plot Chat$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
  plot_chat_forced: $2Este mundo obliga a usar el chat de parcela.
  plot_chat_on: $4Chat de parcela activado.
  plot_chat_off: $4Chat de parcela desactivado.
  plot_chat_spy_format: '$2[$1Plot Spy$2][$1%plot_id%$2] $1%sender%$2: $1%msg%'
deny:
  denied_removed: $4Este usuario ya no esta denegado en esta parcela.
  denied_added: $4Has denegado este usuario de tu parcela.
  denied_need_argument: $2Faltan argumentos. $1/plot denied add <name> $2o $1/plot denied remove <name>
  was_not_denied: $2Ese usuario no ha sido denegado de este plot.
  you_got_denied: $4Has sido denegado de la parcela, se te ha teletransportado al spawn.
rain:
  need_on_off: '$2Necesitas un valor especifico. Posible valores: $1on$2, $1off'
  setting_updated: $4Has actualizado las opciones.
flag:
  flag_key: '$2Llave: %s'
  flag_type: '$2Tipo: %s'
  flag_desc: '$2Desc: %s'
  not_valid_flag: $2No es una flag valida.
  not_valid_value: $2El valor de la flag tiene que ser alfanumerico.
  flag_not_in_plot: $2La parcela no tiene flag.
  flag_not_removed: $2No se ha podido remover la flag.
  flag_not_added: $2La flag no se ha podido añadir.
  flag_removed: $4Flag removida con exito.
  flag_added: $4Flag añadida con exito.
  not_valid_flag_suggested: '$2Eso no es una bandera válida. Querías decir: $1%s'
trusted:
  trusted_added: $4Has añadido un usuario de confianza en esta parcela.
  trusted_removed: $4Has removido un usuario de confianza en esta parcela.
  was_not_added: $2Ese usuario no es de confianza en esta parcela.
  plot_removed_user: $1La parcela %s en la que estabas añadido fue removida por inactividad.
member:
  removed_players: $2Removido %s jugadores de esta parcela.
  already_owner: $2Ese usuario es el dueño del parcela.
  already_added: $2Ese usuario ya esta en esa categoria.
  member_added: $4Este usuario puede construir mientras el dueño de la parcela este conectado.
  member_removed: $1Has removido a un usuario de tu parcela.
  member_was_not_added: $2Ese usuario no fue añadido a tu parcela.
  plot_max_members: $2No puedes añadir mas personas.
owner:
  set_owner: $4Has añadido con exito el dueño de la parcela.
  now_owner: $4Ahora eres dueño de esta parcela %s
  set_owner_cancelled: $2La setowner la acción fue cancelada
signs:
  owner_sign_line_1: '$1ID: $1%id%'
  owner_sign_line_2: '$1Propietario:'
  owner_sign_line_3: $2%plr%
  owner_sign_line_4: $3Claimeado
help:
  help_header: $3&m---------&r $1Plot² Ayuda $3&m---------
  help_page_header: '$1Categoria: $2%category%$2,$1 Página: $2%current%$3/$2%max%$2'
  help_footer: $3&m---------&r $1Plot² Ayuda $3&m---------
  help_info_item: $1/plot help %category% $3- $2%category_desc%
  help_item: $1%usage% [%alias%]&- $3- $2%desc%&-
  direction: '$1Direccion actual: %dir%'
'-':
  custom_string: '-'
commandconfig:
  not_valid_subcommand: $2No es un subcomando valido.
  did_you_mean: '$2Quisiste decir: $1%s'
  name_little: $2%s0 el nombre es muy corto, $1%s1$2<$1%s3
  no_commands: $2No tienes permitido utilizar cualquier subcomando.
  subcommand_set_options_header: '$2Valores posibles: '
  command_syntax: '$1Usos: $2%s'
  flag_tutorial_usage: '$1Haz que un administrador coloque la bandera: $2%s'
bar api: {}
set:
  set_attribute: $4Establecer con éxito %s0 ajustado a %s1
cap:
  entity_cap: $2No tienes permitido engendrar más mobs
near:
  plot_near: '$1Jugadores: %s0'
kick:
  you_got_kicked: $4Te patearon!
grants:
  granted_plots: '$1Resultado: $2%s $1grants left'
  granted_plot: $1You granted %s0 plot to $2%s1
  granted_plot_failed: '$1Subvención fallida: $2%s'
