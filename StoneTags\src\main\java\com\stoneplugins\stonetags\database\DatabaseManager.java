package com.stoneplugins.stonetags.database;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.PlayerData;

import java.io.File;
import java.sql.*;
import java.util.UUID;

public class DatabaseManager {

    private final StoneTags plugin;
    private Connection connection;

    public DatabaseManager(StoneTags plugin) {
        this.plugin = plugin;
        initializeDatabase();
    }

    private void initializeDatabase() {
        try {
            if (plugin.getConfigManager().getDatabaseType().equalsIgnoreCase("MYSQL")) {
                initializeMySQL();
            } else {
                initializeSQLite();
            }

            createTables();
            plugin.getLogger().info("Banco de dados inicializado com sucesso!");

        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao inicializar banco de dados: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void initializeMySQL() throws SQLException {
        String host = plugin.getConfigManager().getMySQLHost();
        int port = plugin.getConfigManager().getMySQLPort();
        String database = plugin.getConfigManager().getMySQLDatabase();
        String username = plugin.getConfigManager().getMySQLUsername();
        String password = plugin.getConfigManager().getMySQLPassword();

        String url = "jdbc:mysql://" + host + ":" + port + "/" + database + "?useSSL=false&autoReconnect=true";

        connection = DriverManager.getConnection(url, username, password);
        plugin.getLogger().info("Conectado ao MySQL com sucesso!");
    }

    private void initializeSQLite() throws SQLException {
        String fileName = plugin.getConfigManager().getSQLiteFile();
        File dataFolder = plugin.getDataFolder();

        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        File databaseFile = new File(dataFolder, fileName);
        String url = "jdbc:sqlite:" + databaseFile.getAbsolutePath();

        connection = DriverManager.getConnection(url);
        plugin.getLogger().info("Conectado ao SQLite com sucesso!");
    }

    private void createTables() throws SQLException {
        // Tabela de dados dos jogadores
        String createPlayerDataTable = "CREATE TABLE IF NOT EXISTS player_data (" +
                "player_id VARCHAR(36) PRIMARY KEY," +
                "selected_tag_id VARCHAR(50)," +
                "selected_suffix_id VARCHAR(50)," +
                "custom_suffix TEXT," +
                "custom_name TEXT," +
                "last_updated BIGINT" +
                ")";

        try (PreparedStatement stmt = connection.prepareStatement(createPlayerDataTable)) {
            stmt.executeUpdate();
        }

        // Adicionar coluna selected_suffix_id se não existir (para bancos existentes)
        try {
            String addSuffixColumn = "ALTER TABLE player_data ADD COLUMN selected_suffix_id VARCHAR(50)";
            try (PreparedStatement stmt = connection.prepareStatement(addSuffixColumn)) {
                stmt.executeUpdate();
                plugin.getLogger().info("Coluna selected_suffix_id adicionada à tabela player_data");
            }
        } catch (SQLException e) {
            // Coluna já existe, ignorar erro
        }

        plugin.getLogger().info("Tabelas do banco de dados criadas/verificadas com sucesso!");
    }

    /**
     * Carrega os dados de um jogador
     */
    public PlayerData loadPlayerData(UUID playerId) {
        String query = "SELECT * FROM player_data WHERE player_id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, playerId.toString());

            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    String selectedTagId = rs.getString("selected_tag_id");
                    String selectedSuffixId = rs.getString("selected_suffix_id");
                    String customSuffix = rs.getString("custom_suffix");
                    String customName = rs.getString("custom_name");
                    long lastUpdated = rs.getLong("last_updated");

                    return new PlayerData(playerId, selectedTagId, selectedSuffixId, customSuffix, customName,
                            lastUpdated);
                }
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao carregar dados do jogador " + playerId + ": " + e.getMessage());
        }

        // Retornar dados padrão se não encontrar
        return new PlayerData(playerId);
    }

    /**
     * Salva os dados de um jogador
     */
    public void savePlayerData(PlayerData playerData) {
        String query = "INSERT OR REPLACE INTO player_data (player_id, selected_tag_id, selected_suffix_id, custom_suffix, custom_name, last_updated) VALUES (?, ?, ?, ?, ?, ?)";

        // Para MySQL, usar ON DUPLICATE KEY UPDATE
        if (plugin.getConfigManager().getDatabaseType().equalsIgnoreCase("MYSQL")) {
            query = "INSERT INTO player_data (player_id, selected_tag_id, selected_suffix_id, custom_suffix, custom_name, last_updated) VALUES (?, ?, ?, ?, ?, ?) "
                    +
                    "ON DUPLICATE KEY UPDATE selected_tag_id = VALUES(selected_tag_id), selected_suffix_id = VALUES(selected_suffix_id), custom_suffix = VALUES(custom_suffix), "
                    +
                    "custom_name = VALUES(custom_name), last_updated = VALUES(last_updated)";
        }

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, playerData.getPlayerId().toString());
            stmt.setString(2, playerData.getSelectedTagId());
            stmt.setString(3, playerData.getSelectedSuffixId());
            stmt.setString(4, playerData.getCustomSuffix());
            stmt.setString(5, playerData.getCustomName());
            stmt.setLong(6, playerData.getLastUpdated());

            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger()
                    .severe("Erro ao salvar dados do jogador " + playerData.getPlayerId() + ": " + e.getMessage());
        }
    }

    /**
     * Remove os dados de um jogador
     */
    public void deletePlayerData(UUID playerId) {
        String query = "DELETE FROM player_data WHERE player_id = ?";

        try (PreparedStatement stmt = connection.prepareStatement(query)) {
            stmt.setString(1, playerId.toString());
            stmt.executeUpdate();
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao deletar dados do jogador " + playerId + ": " + e.getMessage());
        }
    }

    /**
     * Verifica se a conexão está ativa
     */
    public boolean isConnected() {
        try {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }

    /**
     * Reconecta ao banco de dados se necessário
     */
    public void reconnect() {
        try {
            if (!isConnected()) {
                closeConnection();
                initializeDatabase();
            }
        } catch (Exception e) {
            plugin.getLogger().severe("Erro ao reconectar ao banco de dados: " + e.getMessage());
        }
    }

    /**
     * Fecha a conexão com o banco de dados
     */
    public void closeConnection() {
        try {
            if (connection != null && !connection.isClosed()) {
                connection.close();
                plugin.getLogger().info("Conexão com banco de dados fechada!");
            }
        } catch (SQLException e) {
            plugin.getLogger().severe("Erro ao fechar conexão com banco de dados: " + e.getMessage());
        }
    }

    public Connection getConnection() {
        return connection;
    }
}
