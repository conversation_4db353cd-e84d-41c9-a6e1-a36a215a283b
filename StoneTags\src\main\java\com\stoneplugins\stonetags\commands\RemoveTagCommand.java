package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class RemoveTagCommand implements CommandExecutor {

    private final StoneTags plugin;

    public RemoveTagCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Verificar permissão de admin
        if (!sender.hasPermission("stonetags.admin.removetag")) {
            plugin.getMessageUtils().sendConfigMessage(sender, "noPermission");
            return true;
        }

        // Verificar argumentos
        if (args.length < 3) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /tagremove <jogador> <tag_atual> <tag_nova>"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Remove a tag atual e define uma nova"));
            return true;
        }

        String playerName = args[0];
        String oldTagId = args[1];
        String newTagId = args[2];

        // Buscar jogador
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cJogador '" + playerName + "' não encontrado!"));
            return true;
        }

        // Verificar se o jogador tem a tag especificada
        com.stoneplugins.stonetags.data.Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(target);
        if (currentTag == null || !currentTag.getId().equals(oldTagId)) {
            sender.sendMessage(plugin.getMessageUtils()
                    .colorize("&cO jogador " + target.getName() + " não está usando a tag '" + oldTagId + "'!"));
            return true;
        }

        // Verificar se a nova tag existe
        com.stoneplugins.stonetags.data.Tag newTag = plugin.getTagManager().getTag(newTagId);
        if (newTag == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cTag '" + newTagId + "' não encontrada!"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Tags disponíveis: " +
                    String.join(", ", plugin.getTagManager().getTagIds())));
            return true;
        }

        // Remover tag atual e definir nova
        plugin.getPlayerDataManager().setSelectedTag(target, newTagId);

        // Mensagens de sucesso
        String oldTagDisplay = plugin.getMessageUtils().colorize(currentTag.getCurrentPrefix());
        String newTagDisplay = plugin.getMessageUtils().colorize(newTag.getCurrentPrefix());

        sender.sendMessage(plugin.getMessageUtils().colorize(
                "&aTag de " + target.getName() + " alterada: " + oldTagDisplay + " &7→ " + newTagDisplay));

        target.sendMessage(plugin.getMessageUtils().colorize(
                "&cSua tag foi alterada por um administrador: " + oldTagDisplay + " &7→ " + newTagDisplay));

        // Log da ação
        plugin.getLogger().info("[ADMIN] " + sender.getName() + " alterou a tag de " + target.getName() +
                " de '" + oldTagId + "' para '" + newTagId + "'");

        return true;
    }
}
