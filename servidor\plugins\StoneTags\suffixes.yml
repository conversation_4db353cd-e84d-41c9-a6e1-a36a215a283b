# ================================
# StoneTags - Configuração de Sufixos
# ================================
#
# Este arquivo contém todos os sufixos disponíveis no servidor.
# 
# Estrutura de um suffix:
# suffixId:
#   name: "Nome do Suffix"                    # Nome exibido nos menus
#   displayName: "Nome Personalizado"         # Nome alternativo (opcional)
#   chatSuffix: " &8[&aSUFFIX&8]"            # Suffix no chat
#   tabSuffix: " &8[&aSUFFIX&8]"             # Suffix no TAB (opcional, usa chatSuffix se não definido)
#   aboveName: "&aSUFFIX"                     # Texto acima do jogador (opcional)
#   requiresPermission: true                  # Se requer permissão
#   permission: "stonetags.suffix.exemplo"   # Permissão necessária
#   priority: 100                             # Prioridade (maior = mais importante)
#   invisible: false                          # Se torna o jogador invisível
#   animated: false                           # Se é animado
#   animationFrames:                          # Frames da animação (se animated: true)
#     - " &8[&aFRAME1&8]"
#     - " &8[&bFRAME2&8]"
#   animationSpeed: 1000                      # Velocidade da animação em ms

suffixes:
  
  # Suffix Premium
  premium:
    name: "Premium"
    displayName: "&6&lPREMIUM"
    chatSuffix: " &8[&6PREMIUM&8]"
    tabSuffix: " &8[&6PREMIUM&8]"
    aboveName: "&6PREMIUM"
    requiresPermission: true
    permission: "stonetags.suffix.premium"
    priority: 500
    invisible: false
    animated: false
  
  # Suffix VIP+
  vipplus:
    name: "VIP+"
    displayName: "&a&lVIP+"
    chatSuffix: " &8[&aVIP+&8]"
    tabSuffix: " &8[&aVIP+&8]"
    aboveName: "&aVIP+"
    requiresPermission: true
    permission: "stonetags.suffix.vipplus"
    priority: 300
    invisible: false
    animated: false
  
  # Suffix Booster
  booster:
    name: "Booster"
    displayName: "&d&lBOOSTER"
    chatSuffix: " &8[&dBOOSTER&8]"
    tabSuffix: " &8[&dBOOSTER&8]"
    aboveName: "&dBOOSTER"
    requiresPermission: true
    permission: "stonetags.suffix.booster"
    priority: 200
    invisible: false
    animated: false
  
  # Suffix Animado - Exemplo
  rainbow:
    name: "Rainbow"
    displayName: "&c&lRAINBOW"
    chatSuffix: " &8[&cRAINBOW&8]"
    tabSuffix: " &8[&cRAINBOW&8]"
    aboveName: "&cRAINBOW"
    requiresPermission: true
    permission: "stonetags.suffix.rainbow"
    priority: 400
    invisible: false
    animated: true
    animationFrames:
      - " &8[&cR&6A&eI&aN&bB&9O&dW&8]"
      - " &8[&6R&eA&aI&bN&9B&dO&cW&8]"
      - " &8[&eR&aA&bI&9N&dB&cO&6W&8]"
      - " &8[&aR&bA&9I&dN&cB&6O&eW&8]"
      - " &8[&bR&9A&dI&cN&6B&eO&aW&8]"
      - " &8[&9R&dA&cI&6N&eB&aO&bW&8]"
    animationSpeed: 500
  
  # Suffix Invisível - Exemplo
  ninja:
    name: "Ninja"
    displayName: "&8&lNINJA"
    chatSuffix: " &8[&7NINJA&8]"
    tabSuffix: " &8[&7NINJA&8]"
    aboveName: "&7NINJA"
    requiresPermission: true
    permission: "stonetags.suffix.ninja"
    priority: 150
    invisible: true
    animated: false
  
  # Suffix Público (sem permissão)
  newbie:
    name: "Novato"
    displayName: "&7&lNOVATO"
    chatSuffix: " &8[&7NOVATO&8]"
    tabSuffix: " &8[&7NOVATO&8]"
    aboveName: "&7NOVATO"
    requiresPermission: false
    permission: ""
    priority: 10
    invisible: false
    animated: false
  
  # Suffix de Evento
  evento:
    name: "Evento"
    displayName: "&5&lEVENTO"
    chatSuffix: " &8[&5EVENTO&8]"
    tabSuffix: " &8[&5EVENTO&8]"
    aboveName: "&5EVENTO"
    requiresPermission: true
    permission: "stonetags.suffix.evento"
    priority: 600
    invisible: false
    animated: true
    animationFrames:
      - " &8[&5E&dV&5E&dN&5T&dO&8]"
      - " &8[&dE&5V&dE&5N&dT&5O&8]"
    animationSpeed: 1000
