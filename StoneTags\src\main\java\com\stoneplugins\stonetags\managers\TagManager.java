package com.stoneplugins.stonetags.managers;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.*;

public class TagManager {

    private final StoneTags plugin;
    private final Map<String, Tag> tags;

    public TagManager(StoneTags plugin) {
        this.plugin = plugin;
        this.tags = new LinkedHashMap<>();
        loadTags();
    }

    public void loadTags() {
        tags.clear();

        ConfigurationSection tagsSection = plugin.getConfig().getConfigurationSection("Tags");
        if (tagsSection == null) {
            plugin.getLogger().warning("Nenhuma tag encontrada na configuração!");
            return;
        }

        for (String tagId : tagsSection.getKeys(false)) {
            ConfigurationSection tagConfig = tagsSection.getConfigurationSection(tagId);
            if (tagConfig == null)
                continue;

            try {
                String name = tagConfig.getString("name", "&7" + tagId);
                String prefix = tagConfig.getString("prefix", "");
                String suffix = tagConfig.getString("suffix", "");
                String color = tagConfig.getString("color", "&7");
                String icon = tagConfig.getString("icon", "NAME_TAG");
                String permission = tagConfig.getString("permission", "");
                int position = tagConfig.getInt("position", 0);
                List<String> description = tagConfig.getStringList("description");
                String preview = tagConfig.getString("preview", prefix + "{player}");
                String tabPrefix = tagConfig.getString("tabPrefix", prefix);
                String tabSuffix = tagConfig.getString("tabSuffix", suffix);
                String aboveName = tagConfig.getString("aboveName", "");
                boolean invisible = tagConfig.getBoolean("invisible", false);
                boolean animated = tagConfig.getBoolean("animated", false);
                List<String> animationFrames = tagConfig.getStringList("animationFrames");

                Tag tag = new Tag(tagId, name, prefix, suffix, color, icon, permission, position,
                        description, preview, tabPrefix, tabSuffix, aboveName, invisible, animated, animationFrames);

                tags.put(tagId, tag);
                plugin.getLogger().info("Tag carregada: " + tagId + " (posição: " + position + ")");

            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao carregar tag " + tagId + ": " + e.getMessage());
            }
        }

        plugin.getLogger().info("Carregadas " + tags.size() + " tags!");
    }

    public void reloadTags() {
        loadTags();
    }

    /**
     * Obtém uma tag pelo ID
     */
    public Tag getTag(String tagId) {
        return tags.get(tagId);
    }

    /**
     * Obtém todas as tags
     */
    public Collection<Tag> getAllTags() {
        return tags.values();
    }

    /**
     * Obtém todas as tags ordenadas por posição
     */
    public List<Tag> getTagsSortedByPosition() {
        List<Tag> sortedTags = new ArrayList<>(tags.values());
        sortedTags.sort((t1, t2) -> Integer.compare(t2.getPosition(), t1.getPosition())); // Maior posição primeiro
        return sortedTags;
    }

    /**
     * Obtém tags que um jogador pode usar
     */
    public List<Tag> getAvailableTags(Player player) {
        List<Tag> availableTags = new ArrayList<>();

        for (Tag tag : getTagsSortedByPosition()) {
            if (canUseTag(player, tag)) {
                availableTags.add(tag);
            }
        }

        return availableTags;
    }

    /**
     * Verifica se um jogador pode usar uma tag
     */
    public boolean canUseTag(Player player, Tag tag) {
        // Verificar permissão da tag
        if (tag.hasPermissionRequirement()) {
            if (!player.hasPermission(tag.getPermission())) {
                return false;
            }
        }

        // Verificar permissão para tags invisíveis
        if (tag.isInvisible()) {
            if (!player.hasPermission("stonetags.invisible")) {
                return false;
            }
        }

        return true;
    }

    /**
     * Obtém a tag de maior prioridade que um jogador pode usar
     */
    public Tag getHighestPriorityTag(Player player) {
        plugin.getLogger().info("=== DEBUG: getHighestPriorityTag para " + player.getName() + " ===");

        List<Tag> availableTags = getAvailableTags(player);
        plugin.getLogger().info("Tags disponíveis: " + availableTags.size());

        for (Tag tag : availableTags) {
            plugin.getLogger().info("- " + tag.getId() + " (posição: " + tag.getPosition() + ")");
        }

        if (availableTags.isEmpty()) {
            plugin.getLogger().info("Nenhuma tag disponível");
            return null;
        }

        // A primeira tag da lista já é a de maior prioridade (ordenada por posição)
        Tag highest = availableTags.get(0);
        plugin.getLogger().info("Tag de maior prioridade: " + highest.getId());
        return highest;
    }

    /**
     * Verifica se uma tag existe
     */
    public boolean tagExists(String tagId) {
        return tags.containsKey(tagId);
    }

    /**
     * Obtém o número total de tags
     */
    public int getTagCount() {
        return tags.size();
    }

    /**
     * Obtém tags por página para o menu
     */
    public List<Tag> getTagsForPage(Player player, int page, int itemsPerPage) {
        List<Tag> availableTags = getAvailableTags(player);

        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, availableTags.size());

        if (startIndex >= availableTags.size()) {
            return new ArrayList<>();
        }

        return availableTags.subList(startIndex, endIndex);
    }

    /**
     * Calcula o número máximo de páginas para um jogador
     */
    public int getMaxPages(Player player, int itemsPerPage) {
        List<Tag> availableTags = getAvailableTags(player);
        return (int) Math.ceil((double) availableTags.size() / itemsPerPage);
    }

    /**
     * Procura tags por nome (busca parcial)
     */
    public List<Tag> searchTags(String query) {
        List<Tag> results = new ArrayList<>();
        String lowerQuery = query.toLowerCase();

        for (Tag tag : tags.values()) {
            if (tag.getId().toLowerCase().contains(lowerQuery) ||
                    plugin.getMessageUtils().stripColor(tag.getName()).toLowerCase().contains(lowerQuery)) {
                results.add(tag);
            }
        }

        return results;
    }

    /**
     * Obtém informações de uma tag para placeholders
     */
    public String getTagInfo(String tagId, String info) {
        Tag tag = getTag(tagId);
        if (tag == null)
            return "";

        switch (info.toLowerCase()) {
            case "id":
                return tag.getId();
            case "name":
                return tag.getName();
            case "prefix":
                return tag.getPrefix();
            case "prefix_animated":
                return tag.getCurrentPrefix(null);
            case "suffix":
                return tag.getSuffix();
            case "color":
                return tag.getColor();
            case "icon":
                return tag.getIcon();
            case "permission":
                return tag.getPermission();
            case "position":
                return String.valueOf(tag.getPosition());
            case "preview":
                return tag.getPreview();
            case "tabprefix":
                return tag.getTabPrefix();
            case "tabsuffix":
                return tag.getTabSuffix();
            case "abovename":
                return tag.getAboveName();
            case "invisible":
                return String.valueOf(tag.isInvisible());
            case "animated":
                return String.valueOf(tag.isAnimated());
            default:
                return "";
        }
    }

    public List<String> getTagIds() {
        return new ArrayList<>(tags.keySet());
    }
}
