package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class RewardManager {
    
    private final StoneCactos plugin;
    
    public RewardManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Coleta uma recompensa para um jogador
     */
    public boolean collectReward(Player player, String rewardId) {
        UUID playerUuid = player.getUniqueId();
        int rewardIdInt = plugin.getConfigManager().getRewardId(rewardId);
        
        // Verificar se a recompensa existe
        if (!plugin.getConfigManager().hasReward(rewardId)) {
            return false;
        }
        
        // Verificar se já foi coletada
        if (plugin.getDatabaseManager().hasCollectedReward(playerUuid, rewardIdInt)) {
            player.sendMessage(plugin.getConfigManager().getMessage("rewardAlreadyRedeemed"));
            return false;
        }
        
        // Verificar se o jogador tem cactos suficientes
        int playerCactus = plugin.getDatabaseManager().getTotalCactusGenerated(playerUuid);
        int necessaryCactus = plugin.getConfigManager().getRewardNecessaryCactus(rewardId);
        
        if (playerCactus < necessaryCactus) {
            String message = plugin.getConfigManager().getMessage("insufficientRequirementsForReward")
                .replace("%cactus%", String.valueOf(necessaryCactus));
            player.sendMessage(message);
            return false;
        }
        
        // Executar comandos da recompensa
        List<String> commands = plugin.getConfigManager().getRewardCommands(rewardId);
        for (String command : commands) {
            String processedCommand = command.replace("%player%", player.getName());
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
        }
        
        // Marcar como coletada
        plugin.getDatabaseManager().markRewardAsCollected(playerUuid, rewardIdInt);
        
        // Enviar mensagem de sucesso
        player.sendMessage(plugin.getConfigManager().getMessage("rewardCollected"));
        
        return true;
    }
    
    /**
     * Verifica se um jogador pode coletar uma recompensa
     */
    public boolean canCollectReward(UUID playerUuid, String rewardId) {
        int rewardIdInt = plugin.getConfigManager().getRewardId(rewardId);
        
        // Verificar se a recompensa existe
        if (!plugin.getConfigManager().hasReward(rewardId)) {
            return false;
        }
        
        // Verificar se já foi coletada
        if (plugin.getDatabaseManager().hasCollectedReward(playerUuid, rewardIdInt)) {
            return false;
        }
        
        // Verificar se o jogador tem cactos suficientes
        int playerCactus = plugin.getDatabaseManager().getTotalCactusGenerated(playerUuid);
        int necessaryCactus = plugin.getConfigManager().getRewardNecessaryCactus(rewardId);
        
        return playerCactus >= necessaryCactus;
    }
    
    /**
     * Verifica se um jogador já coletou uma recompensa
     */
    public boolean hasCollectedReward(UUID playerUuid, String rewardId) {
        int rewardIdInt = plugin.getConfigManager().getRewardId(rewardId);
        return plugin.getDatabaseManager().hasCollectedReward(playerUuid, rewardIdInt);
    }
    
    /**
     * Obtém todas as recompensas disponíveis
     */
    public List<String> getAvailableRewards() {
        List<String> rewards = new ArrayList<>();
        
        // Verificar recompensas na configuração
        if (plugin.getConfigManager().getConfig().getConfigurationSection("Rewards") != null) {
            for (String key : plugin.getConfigManager().getConfig().getConfigurationSection("Rewards").getKeys(false)) {
                rewards.add(key);
            }
        }
        
        return rewards;
    }
    
    /**
     * Obtém recompensas que um jogador pode coletar
     */
    public List<String> getCollectableRewards(UUID playerUuid) {
        List<String> collectableRewards = new ArrayList<>();
        
        for (String rewardId : getAvailableRewards()) {
            if (canCollectReward(playerUuid, rewardId)) {
                collectableRewards.add(rewardId);
            }
        }
        
        return collectableRewards;
    }
    
    /**
     * Obtém recompensas já coletadas por um jogador
     */
    public List<String> getCollectedRewards(UUID playerUuid) {
        List<String> collectedRewards = new ArrayList<>();
        
        for (String rewardId : getAvailableRewards()) {
            if (hasCollectedReward(playerUuid, rewardId)) {
                collectedRewards.add(rewardId);
            }
        }
        
        return collectedRewards;
    }
    
    /**
     * Obtém o progresso de um jogador em uma recompensa
     */
    public String getRewardProgress(UUID playerUuid, String rewardId) {
        if (!plugin.getConfigManager().hasReward(rewardId)) {
            return "§cRecompensa não encontrada";
        }
        
        int playerCactus = plugin.getDatabaseManager().getTotalCactusGenerated(playerUuid);
        int necessaryCactus = plugin.getConfigManager().getRewardNecessaryCactus(rewardId);
        
        if (hasCollectedReward(playerUuid, rewardId)) {
            return "§aColetada";
        } else if (playerCactus >= necessaryCactus) {
            return "§eDisponível para coleta";
        } else {
            double percentage = (double) playerCactus / necessaryCactus * 100;
            return String.format("§c%.1f%% (%d/%d)", percentage, playerCactus, necessaryCactus);
        }
    }
    
    /**
     * Obtém informações detalhadas de uma recompensa
     */
    public String getRewardInfo(String rewardId) {
        if (!plugin.getConfigManager().hasReward(rewardId)) {
            return "§cRecompensa não encontrada";
        }
        
        String name = plugin.getConfigManager().getRewardName(rewardId);
        int necessaryCactus = plugin.getConfigManager().getRewardNecessaryCactus(rewardId);
        List<String> commands = plugin.getConfigManager().getRewardCommands(rewardId);
        
        return String.format("§e%s\n§fCactos necessários: §a%d\n§fComandos: §7%d",
            name, necessaryCactus, commands.size());
    }
    
    /**
     * Obtém estatísticas das recompensas de um jogador
     */
    public String getPlayerRewardStats(UUID playerUuid) {
        List<String> allRewards = getAvailableRewards();
        List<String> collectedRewards = getCollectedRewards(playerUuid);
        List<String> collectableRewards = getCollectableRewards(playerUuid);
        
        return String.format("§eRecompensas: §f%d/%d coletadas §7| §e%d disponíveis",
            collectedRewards.size(), allRewards.size(), collectableRewards.size());
    }
    
    /**
     * Força a coleta de uma recompensa (comando admin)
     */
    public boolean forceCollectReward(Player player, String rewardId) {
        UUID playerUuid = player.getUniqueId();
        int rewardIdInt = plugin.getConfigManager().getRewardId(rewardId);
        
        // Verificar se a recompensa existe
        if (!plugin.getConfigManager().hasReward(rewardId)) {
            return false;
        }
        
        // Executar comandos da recompensa
        List<String> commands = plugin.getConfigManager().getRewardCommands(rewardId);
        for (String command : commands) {
            String processedCommand = command.replace("%player%", player.getName());
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), processedCommand);
        }
        
        // Marcar como coletada
        plugin.getDatabaseManager().markRewardAsCollected(playerUuid, rewardIdInt);
        
        return true;
    }
}
