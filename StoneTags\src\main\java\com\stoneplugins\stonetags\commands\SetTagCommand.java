package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class SetTagCommand implements CommandExecutor {

    private final StoneTags plugin;

    public SetTagCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Verificar permissão de admin
        if (!sender.hasPermission("stonetags.admin.settag")) {
            plugin.getMessageUtils().sendConfigMessage(sender, "noPermission");
            return true;
        }

        // Verificar argumentos
        if (args.length < 2) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /tagset <jogador> <tag>"));
            return true;
        }

        String playerName = args[0];
        String tagId = args[1];

        // Buscar jogador
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cJogador '" + playerName + "' não encontrado!"));
            return true;
        }

        // Verificar se a tag existe
        Tag tag = plugin.getTagManager().getTag(tagId);
        if (tag == null) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cTag '" + tagId + "' não encontrada!"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&7Tags disponíveis: " +
                    String.join(", ", plugin.getTagManager().getTagIds())));
            return true;
        }

        // Definir tag para o jogador
        plugin.getPlayerDataManager().setSelectedTag(target, tagId);

        // Mensagens de sucesso
        String tagDisplay = plugin.getMessageUtils().colorize(tag.getCurrentPrefix(target));

        sender.sendMessage(plugin.getMessageUtils().colorize(
                "&aTag definida para " + target.getName() + ": " + tagDisplay));

        target.sendMessage(plugin.getMessageUtils().colorize(
                "&aUm administrador definiu sua tag: " + tagDisplay));

        // Log da ação
        plugin.getLogger().info("[ADMIN] " + sender.getName() + " definiu a tag '" + tagId +
                "' para o jogador " + target.getName());

        return true;
    }

    private void removePlayerTag(CommandSender sender, Player target) {
        if (!plugin.getPlayerDataManager().hasSelectedTag(target)) {
            sender.sendMessage(plugin.getMessageUtils()
                    .colorize("&cO jogador " + target.getName() + " não está usando nenhuma tag!"));
            return;
        }

        // Remover tag
        plugin.getPlayerDataManager().removeSelectedTag(target);

        // Mensagens de sucesso
        sender.sendMessage(plugin.getMessageUtils().colorize("&aTag removida de " + target.getName() + "!"));
        target.sendMessage(plugin.getMessageUtils().colorize("&cUm administrador removeu sua tag."));

        // Log da ação
        plugin.getLogger().info("[ADMIN] " + sender.getName() + " removeu a tag do jogador " + target.getName());
    }
}
