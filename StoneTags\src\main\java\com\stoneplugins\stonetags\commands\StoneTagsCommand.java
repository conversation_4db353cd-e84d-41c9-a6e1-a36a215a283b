package com.stoneplugins.stonetags.commands;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Tag;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

import java.util.List;

public class StoneTagsCommand implements CommandExecutor {

    private final StoneTags plugin;

    public StoneTagsCommand(StoneTags plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("stonetags.admin")) {
            plugin.getMessageUtils().sendConfigMessage(sender, "noPermission");
            return true;
        }

        if (args.length == 0) {
            showHelp(sender);
            return true;
        }

        String subCommand = args[0].toLowerCase();

        switch (subCommand) {
            case "reload":
                reloadPlugin(sender);
                break;

            case "give":
                if (args.length < 3) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags give <jogador> <tag>"));
                    return true;
                }
                giveTag(sender, args[1], args[2]);
                break;

            case "remove":
                if (args.length < 2) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags remove <jogador>"));
                    return true;
                }
                removeTag(sender, args[1]);
                break;

            case "list":
                listAllTags(sender);
                break;

            case "info":
                if (args.length < 2) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags info <tag>"));
                    return true;
                }
                showTagInfo(sender, args[1]);
                break;

            case "player":
                if (args.length < 2) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags player <jogador>"));
                    return true;
                }
                showPlayerInfo(sender, args[1]);
                break;

            case "stats":
                showStats(sender);
                break;

            case "test":
                if (args.length < 2) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags test <jogador>"));
                    return true;
                }
                testPlayer(sender, args[1]);
                break;

            case "fixperms":
                if (args.length < 2) {
                    sender.sendMessage(plugin.getMessageUtils().colorize("&cUso: /stonetags fixperms <jogador>"));
                    return true;
                }
                fixPlayerPermissions(sender, args[1]);
                break;

            case "help":
                showHelp(sender);
                break;

            default:
                sender.sendMessage(plugin.getMessageUtils().colorize("&cComando desconhecido! Use /stonetags help"));
                break;
        }

        return true;
    }

    private void reloadPlugin(CommandSender sender) {
        try {
            plugin.reload();
            plugin.getMessageUtils().sendConfigMessage(sender, "configReloaded");
        } catch (Exception e) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cErro ao recarregar plugin: " + e.getMessage()));
            plugin.getLogger().severe("Erro ao recarregar plugin: " + e.getMessage());
        }
    }

    private void giveTag(CommandSender sender, String playerName, String tagId) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerNotFound");
            return;
        }

        Tag tag = plugin.getTagManager().getTag(tagId);
        if (tag == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "tagNotFound");
            return;
        }

        plugin.getPlayerDataManager().setSelectedTag(target, tagId);

        plugin.getMessageUtils().sendConfigMessage(sender, "tagGiven",
                "{tag}", plugin.getMessageUtils().colorize(tag.getName()),
                "{player}", target.getName());

        // Notificar o jogador
        plugin.getMessageUtils().sendConfigMessage(target, "tagSelected",
                "{tag}", plugin.getMessageUtils().colorize(tag.getName()));
    }

    private void removeTag(CommandSender sender, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerNotFound");
            return;
        }

        if (!plugin.getPlayerDataManager().hasSelectedTag(target)) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cEste jogador não está usando nenhuma tag!"));
            return;
        }

        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(target);
        plugin.getPlayerDataManager().removeSelectedTag(target);

        plugin.getMessageUtils().sendConfigMessage(sender, "tagTaken",
                "{tag}", currentTag != null ? plugin.getMessageUtils().colorize(currentTag.getName()) : "Unknown",
                "{player}", target.getName());

        // Notificar o jogador
        plugin.getMessageUtils().sendConfigMessage(target, "tagRemoved");
    }

    private void listAllTags(CommandSender sender) {
        List<Tag> allTags = plugin.getTagManager().getTagsSortedByPosition();

        if (allTags.isEmpty()) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cNenhuma tag configurada!"));
            return;
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lTodas as Tags (" + allTags.size() + "):"));
        sender.sendMessage("");

        for (Tag tag : allTags) {
            String status = tag.hasPermissionRequirement() ? "&c[Perm]" : "&a[Livre]";
            String type = tag.isInvisible() ? "&7[Invisível]" : "";
            String animated = tag.isAnimated() ? "&e[Animada]" : "";

            sender.sendMessage(plugin.getMessageUtils().colorize(
                    "&8• &f" + tag.getId() + " &8(" + tag.getPosition() + ") " +
                            status + type + animated + " &8- " + tag.getName()));
        }

        sender.sendMessage("");
    }

    private void showTagInfo(CommandSender sender, String tagId) {
        Tag tag = plugin.getTagManager().getTag(tagId);

        if (tag == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "tagNotFound");
            return;
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lInformações da Tag: &f" + tagId));
        sender.sendMessage("");
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome: " + tag.getName()));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fPrefix: &r" + tag.getPrefix()));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fSuffix: &r" + tag.getSuffix()));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fCor: " + tag.getColor()));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fÍcone: &7" + tag.getIcon()));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fPosição: &7" + tag.getPosition()));

        if (tag.hasPermissionRequirement()) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fPermissão: &7" + tag.getPermission()));
        }

        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fInvisível: " + (tag.isInvisible() ? "&cSim" : "&aNão")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fAnimada: " + (tag.isAnimated() ? "&aSim" : "&cNão")));

        if (tag.hasAboveName()) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fAbove Name: &r" + tag.getAboveName()));
        }

        sender.sendMessage("");
    }

    private void showPlayerInfo(CommandSender sender, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerNotFound");
            return;
        }

        Tag currentTag = plugin.getPlayerDataManager().getSelectedTag(target);
        String customSuffix = plugin.getPlayerDataManager().getCustomSuffix(target);
        String customName = plugin.getPlayerDataManager().getCustomName(target);

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lInformações do Jogador: &f" + target.getName()));
        sender.sendMessage("");
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag Atual: " +
                (currentTag != null ? currentTag.getName() + " &8(" + currentTag.getId() + ")" : "&cNenhuma")));

        if (customSuffix != null && !customSuffix.isEmpty()) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fSufixo Custom: &r" + customSuffix));
        }

        if (customName != null && !customName.isEmpty()) {
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fNome Custom: &r" + customName));
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fDisplay Name: &r" + target.getDisplayName()));

        List<Tag> availableTags = plugin.getTagManager().getAvailableTags(target);
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTags Disponíveis: &7" + availableTags.size()));

        sender.sendMessage("");
    }

    private void showStats(CommandSender sender) {
        int totalTags = plugin.getTagManager().getTagCount();
        int onlinePlayers = Bukkit.getOnlinePlayers().size();
        int playersWithTags = 0;

        for (Player player : Bukkit.getOnlinePlayers()) {
            if (plugin.getPlayerDataManager().hasSelectedTag(player)) {
                playersWithTags++;
            }
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lStoneTags - Estatísticas:"));
        sender.sendMessage("");
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTotal de Tags: &7" + totalTags));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fJogadores Online: &7" + onlinePlayers));
        sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fJogadores com Tags: &7" + playersWithTags));
        sender.sendMessage(plugin.getMessageUtils()
                .colorize("&8• &fBanco de Dados: &7" + plugin.getConfigManager().getDatabaseType()));
        sender.sendMessage(plugin.getMessageUtils()
                .colorize("&8• &fMenu Ativo: " + (plugin.getConfigManager().useMenu() ? "&aSim" : "&cNão")));
        sender.sendMessage(plugin.getMessageUtils()
                .colorize("&8• &fAnimações: " + (plugin.getConfigManager().enableAnimations() ? "&aSim" : "&cNão")));
        sender.sendMessage("");
    }

    private void testPlayer(CommandSender sender, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerNotFound");
            return;
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lTeste de Debug para: &f" + target.getName()));
        sender.sendMessage("");

        // Testar permissões
        sender.sendMessage(plugin.getMessageUtils().colorize("&ePermissões:"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fstonetags.use: " + target.hasPermission("stonetags.use")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fstonetags.vip: " + target.hasPermission("stonetags.vip")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fstonetags.mvp: " + target.hasPermission("stonetags.mvp")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fstonetags.staff: " + target.hasPermission("stonetags.staff")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &ftab.group.vip: " + target.hasPermission("tab.group.vip")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &ftab.group.mvp: " + target.hasPermission("tab.group.mvp")));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &ftab.group.staff: " + target.hasPermission("tab.group.staff")));
        sender.sendMessage("");

        // Testar placeholders
        sender.sendMessage(plugin.getMessageUtils().colorize("&ePlaceholders:"));
        if (plugin.getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            String tagId = me.clip.placeholderapi.PlaceholderAPI.setPlaceholders(target, "%stonetags_current_tag_id%");
            String tagName = me.clip.placeholderapi.PlaceholderAPI.setPlaceholders(target,
                    "%stonetags_current_tag_name%");
            String tagPrefix = me.clip.placeholderapi.PlaceholderAPI.setPlaceholders(target,
                    "%stonetags_current_tag_prefix%");

            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag ID: '" + tagId + "'"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag Name: '" + tagName + "'"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTag Prefix: '" + tagPrefix + "'"));
        } else {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cPlaceholderAPI não encontrado!"));
        }
        sender.sendMessage("");

        // Debug do scoreboard
        sender.sendMessage(plugin.getMessageUtils().colorize("&eScoreboard Debug:"));
        org.bukkit.scoreboard.Scoreboard scoreboard = Bukkit.getScoreboardManager().getMainScoreboard();
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&8• &fScoreboard teams: " + scoreboard.getTeams().size()));

        for (org.bukkit.scoreboard.Team team : scoreboard.getTeams()) {
            if (team.getName().startsWith("stonetags_")) {
                sender.sendMessage(plugin.getMessageUtils().colorize("&8• &fTeam: " + team.getName() +
                        " | Prefix: '" + team.getPrefix() + "' | Members: " + team.getEntries().size()));
                if (team.hasEntry(target.getName())) {
                    sender.sendMessage(
                            plugin.getMessageUtils().colorize("&8  &a✓ " + target.getName() + " está no team"));
                }
            }
        }
        sender.sendMessage("");

        // Forçar atualização
        sender.sendMessage(plugin.getMessageUtils().colorize("&eForçando atualização..."));
        plugin.getPlayerDataManager().updatePlayerDisplay(target);

        sender.sendMessage(plugin.getMessageUtils().colorize("&aTeste concluído! Verifique os logs do console."));
    }

    private void fixPlayerPermissions(CommandSender sender, String playerName) {
        Player target = Bukkit.getPlayer(playerName);
        if (target == null) {
            plugin.getMessageUtils().sendConfigMessage(sender, "playerNotFound");
            return;
        }

        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lCorrigindo permissões para: &f" + target.getName()));

        // Verificar qual tag o jogador tem
        Tag selectedTag = plugin.getPlayerDataManager().getSelectedTag(target);
        if (selectedTag != null) {
            // Dar permissão do TAB baseada na tag
            String tabPermission = "tab.group." + selectedTag.getId();

            // Usar comando do console para dar permissão (funciona com LuckPerms/PEX)
            String command = "lp user " + target.getName() + " permission set " + tabPermission + " true";
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);

            sender.sendMessage(plugin.getMessageUtils().colorize("&aPermissão '" + tabPermission + "' adicionada!"));
            sender.sendMessage(plugin.getMessageUtils().colorize("&eForçando atualização do TAB..."));

            // Forçar atualização
            plugin.getPlayerDataManager().updatePlayerDisplay(target);

        } else {
            sender.sendMessage(plugin.getMessageUtils().colorize("&cJogador não tem nenhuma tag selecionada!"));
        }
    }

    private void showHelp(CommandSender sender) {
        sender.sendMessage(plugin.getMessageUtils().colorize("&6&lStoneTags - Comandos Admin:"));
        sender.sendMessage("");
        sender.sendMessage(plugin.getMessageUtils().colorize("&f/stonetags reload &8- &7Recarregar plugin"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&f/stonetags give <jogador> <tag> &8- &7Dar tag para jogador"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&f/stonetags remove <jogador> &8- &7Remover tag do jogador"));
        sender.sendMessage(plugin.getMessageUtils().colorize("&f/stonetags list &8- &7Listar todas as tags"));
        sender.sendMessage(plugin.getMessageUtils().colorize("&f/stonetags info <tag> &8- &7Ver informações da tag"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&f/stonetags player <jogador> &8- &7Ver informações do jogador"));
        sender.sendMessage(plugin.getMessageUtils().colorize("&f/stonetags stats &8- &7Ver estatísticas do plugin"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&f/stonetags test <jogador> &8- &7Testar debug do jogador"));
        sender.sendMessage(
                plugin.getMessageUtils().colorize("&f/stonetags fixperms <jogador> &8- &7Corrigir permissões do TAB"));
        sender.sendMessage("");
    }
}
