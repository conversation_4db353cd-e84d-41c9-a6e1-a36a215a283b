package com.stoneplugins.spawnershop.managers;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.data.Spawner;
import org.bukkit.Bukkit;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.entity.Player;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class SpawnerManager {

    private final SpawnerShop plugin;
    private final Map<String, Spawner> spawners;

    public SpawnerManager(SpawnerShop plugin) {
        this.plugin = plugin;
        this.spawners = new LinkedHashMap<>(); // Manter ordem do arquivo
        loadSpawners();
    }

    public void loadSpawners() {
        spawners.clear();

        ConfigurationSection spawnersSection = plugin.getConfigManager().getConfig()
                .getConfigurationSection("Spawners");
        if (spawnersSection == null) {
            plugin.getLogger().warning("Nenhum spawner encontrado na configuração!");
            return;
        }

        // Carregar spawners na ordem do arquivo YAML (LinkedHashMap preserva ordem)
        plugin.getLogger().info("=== DEBUG: Ordem das keys do YAML ===");
        Set<String> keys = spawnersSection.getKeys(false);
        int keyIndex = 0;
        for (String key : keys) {
            plugin.getLogger().info("Key " + keyIndex + ": " + key);
            keyIndex++;
        }
        plugin.getLogger().info("=== FIM DEBUG ===");

        int order = 0;
        for (String spawnerId : spawnersSection.getKeys(false)) {
            ConfigurationSection spawnerConfig = spawnersSection.getConfigurationSection(spawnerId);
            if (spawnerConfig == null)
                continue;

            String title = spawnerConfig.getString("title", "&e" + spawnerId);
            double price = spawnerConfig.getDouble("price", 0.0);
            String skull = spawnerConfig.getString("skull", "");
            String command = spawnerConfig.getString("command", "");
            String permission = spawnerConfig.getString("permission", "");
            String releaseDate = spawnerConfig.getString("liberaAs", "");
            String requiredRank = spawnerConfig.getString("rankNecessario", "");

            Spawner spawner = new Spawner(spawnerId, title, price, skull, command, permission, releaseDate,
                    requiredRank, order);
            spawners.put(spawnerId, spawner);

            plugin.getLogger()
                    .info("Carregado spawner: " + spawnerId + " na posição " + order + " (title: " + title + ")");
            order++;
        }

        plugin.getLogger().info("Carregados " + spawners.size() + " spawners!");
    }

    public List<Spawner> getSpawners() {
        List<Spawner> spawnerList = new ArrayList<>(spawners.values());
        spawnerList.sort((s1, s2) -> Integer.compare(s1.getOrder(), s2.getOrder()));

        plugin.getLogger().info("=== DEBUG: Ordem final dos spawners ===");
        for (int i = 0; i < spawnerList.size(); i++) {
            Spawner spawner = spawnerList.get(i);
            plugin.getLogger().info("Posição " + i + ": " + spawner.getId() + " (order: " + spawner.getOrder()
                    + ", title: " + spawner.getTitle() + ")");
        }
        plugin.getLogger().info("=== FIM DEBUG ORDEM FINAL ===");

        return spawnerList;
    }

    public Spawner getSpawner(String id) {
        return spawners.get(id);
    }

    public Map<String, Spawner> getAllSpawners() {
        return new HashMap<>(spawners);
    }

    public boolean canPlayerBuySpawner(Player player, Spawner spawner) {
        // Verificar se o spawner foi liberado
        if (!spawner.isReleased() && !player.hasPermission("spawnershop.bypass.date")) {
            return false;
        }

        // Verificar permissão necessária
        if (spawner.hasPermissionRequirement() && !player.hasPermission(spawner.getPermission())
                && !player.hasPermission("spawnershop.bypass.permission")) {
            return false;
        }

        // Verificar rank necessário (se StormRankUP estiver presente)
        if (spawner.hasRequiredRank() && !player.hasPermission("spawnershop.bypass.rank")) {
            if (!hasRequiredRank(player, spawner.getRequiredRank())) {
                return false;
            }
        }

        return true;
    }

    private boolean hasRequiredRank(Player player, String requiredRank) {
        // Verificar se o StormRankUP está presente
        if (Bukkit.getPluginManager().getPlugin("StormRankUP") == null) {
            return true; // Se não tem o plugin, ignora a verificação
        }

        try {
            // Usar reflexão para acessar a API do StormRankUP
            Class<?> stormRankAPI = Class.forName("com.stormplugins.stormrankup.api.StormRankUPAPI");
            Object instance = stormRankAPI.getMethod("getInstance").invoke(null);
            String playerRank = (String) stormRankAPI.getMethod("getRank", Player.class).invoke(instance, player);

            // Aqui você pode implementar a lógica de comparação de ranks
            // Por simplicidade, vamos apenas verificar se o rank é igual
            return playerRank != null && playerRank.equalsIgnoreCase(requiredRank);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao verificar rank do jogador: " + e.getMessage());
            return true; // Em caso de erro, permite a compra
        }
    }

    public String getSpawnerStatus(Player player, Spawner spawner) {
        if (!spawner.isReleased() && !player.hasPermission("spawnershop.bypass.date")) {
            return "&cLibera em " + spawner.getTimeUntilRelease();
        }

        if (spawner.hasPermissionRequirement() && !player.hasPermission(spawner.getPermission())
                && !player.hasPermission("spawnershop.bypass.permission")) {
            return "&cVocê precisa da permissão: " + spawner.getPermission();
        }

        if (spawner.hasRequiredRank() && !player.hasPermission("spawnershop.bypass.rank")) {
            if (!hasRequiredRank(player, spawner.getRequiredRank())) {
                return "&cVocê precisa do rank: " + spawner.getRequiredRank();
            }
        }

        return "&aClique aqui para finalizar a compra.";
    }

    public int getMaxPurchasableQuantity(Player player, Spawner spawner) {
        double playerBalance = plugin.getEconomyManager().getBalance(player);
        double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
        double finalPrice = spawner.getPriceWithDiscount(discount);

        // Calcular máximo por dinheiro (para mostrar na descrição)
        if (finalPrice <= 0) {
            return Integer.MAX_VALUE;
        }

        // Nunca retornar valor negativo
        return Math.max(0, (int) Math.floor(playerBalance / finalPrice));
    }

    public int getMaxPurchasableQuantityWithLimit(Player player, Spawner spawner) {
        double playerBalance = plugin.getEconomyManager().getBalance(player);
        double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
        double finalPrice = spawner.getPriceWithDiscount(discount);

        // Calcular máximo por dinheiro
        int maxByMoney = 0;
        if (finalPrice > 0) {
            maxByMoney = (int) Math.floor(playerBalance / finalPrice);
        }

        // Calcular máximo por limite (limite é por compra, não acumulado)
        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
        int purchaseLimit = playerData.getPurchaseLimit();

        // Retornar o menor entre dinheiro e limite de compra, mas nunca negativo
        return Math.max(0, Math.min(maxByMoney, purchaseLimit));
    }

    public boolean purchaseSpawner(Player player, Spawner spawner, int quantity) {
        // Verificar se pode comprar
        if (!canPlayerBuySpawner(player, spawner)) {
            return false;
        }

        // Verificar limite por compra (não acumulado)
        PlayerData playerData = plugin.getPlayerDataManager().getPlayerData(player);
        if (quantity > playerData.getPurchaseLimit()) {
            player.sendMessage(plugin.getMessageUtils().colorize("&c&lLIMITE EXCEDIDO! &fVoce so pode comprar ate &a"
                    + playerData.getPurchaseLimit() + "&f geradores por vez."));
            return false;
        }

        // Calcular preço final
        double discount = plugin.getPlayerDataManager().getPlayerDiscount(player);
        double finalPrice = spawner.getPriceWithDiscount(discount) * quantity;

        // Verificar saldo
        if (!plugin.getEconomyManager().hasBalance(player, finalPrice)) {
            player.sendMessage(plugin.getMessageUtils().colorize(plugin.getConfigManager().getMessage("semsaldo")));
            return false;
        }

        // Retirar dinheiro
        if (!plugin.getEconomyManager().withdrawBalance(player, finalPrice)) {
            player.sendMessage(plugin.getMessageUtils().colorize(plugin.getConfigManager().getMessage("semsaldo")));
            return false;
        }

        // Executar comando
        String command = spawner.getFormattedCommand(player.getName(), quantity);
        Bukkit.dispatchCommand(Bukkit.getConsoleSender(), command);

        // Registrar compra
        plugin.getPlayerDataManager().addPurchase(player, spawner.getId(), quantity, finalPrice);

        // Enviar mensagem de sucesso com detalhes
        String formattedPrice = formatPrice(finalPrice);
        String message = plugin.getConfigManager().getMessage("spawnercomprado")
                .replace("{quantidade}", String.valueOf(quantity))
                .replace("{spawner}", spawner.getTitle())
                .replace("{tipo}", spawner.getId())
                .replace("{preco}", formattedPrice);
        player.sendMessage(plugin.getMessageUtils().colorize(message));

        return true;
    }

    public void reloadSpawners() {
        loadSpawners();
    }

    private String formatPrice(double price) {
        // Formatar preço com letras (K, M, B, T)
        if (price >= 1000000000000.0) {
            return String.format("%.1fT", price / 1000000000000.0);
        } else if (price >= 1000000000.0) {
            return String.format("%.1fB", price / 1000000000.0);
        } else if (price >= 1000000.0) {
            return String.format("%.1fM", price / 1000000.0);
        } else if (price >= 1000.0) {
            return String.format("%.1fK", price / 1000.0);
        } else {
            // Números menores que 1000, mostrar sem decimais se for inteiro
            if (price == (long) price) {
                return String.format("%.0f", price);
            } else {
                return String.format("%.2f", price);
            }
        }
    }
}
