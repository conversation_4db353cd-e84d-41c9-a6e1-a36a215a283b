package com.stoneplugins.spawnershop.utils;

import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemFlag;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.util.UUID;

public class ItemUtils {

    private static String colorize(String message) {
        if (message == null)
            return "";
        return ChatColor.translateAlternateColorCodes('&', message);
    }

    public static ItemStack createItem(Material material, int amount, short data, String name, List<String> lore) {
        ItemStack item = new ItemStack(material, amount, data);
        ItemMeta meta = item.getItemMeta();

        if (meta != null) {
            if (name != null && !name.isEmpty()) {
                meta.setDisplayName(colorize(name));
            }

            if (lore != null && !lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(colorize(line));
                }
                meta.setLore(coloredLore);
            }

            item.setItemMeta(meta);
        }

        return item;
    }

    public static ItemStack createItem(String materialData, String name, List<String> lore) {
        String[] parts = materialData.split(":");
        Material material = Material.valueOf(parts[0]);
        short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;

        return createItem(material, 1, data, name, lore);
    }

    public static ItemStack createSkull(String texture, String name, List<String> lore) {
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta meta = (SkullMeta) skull.getItemMeta();

        if (meta != null) {
            if (name != null && !name.isEmpty()) {
                meta.setDisplayName(colorize(name));
            }

            if (lore != null && !lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(colorize(line));
                }
                meta.setLore(coloredLore);
            }

            // Aplicar textura
            if (texture != null && !texture.isEmpty()) {
                if (texture.equals("%jogador%") || texture.equals("%player%")) {
                    // Será definido depois quando soubermos o jogador
                } else if (texture.length() > 16) {
                    // É uma textura base64
                    setSkullTexture(meta, texture);
                } else {
                    // É um nome de jogador
                    meta.setOwner(texture);
                }
            }

            skull.setItemMeta(meta);
        }

        return skull;
    }

    public static ItemStack createPlayerSkull(Player player, String name, List<String> lore) {
        ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta meta = (SkullMeta) skull.getItemMeta();

        if (meta != null) {
            if (name != null && !name.isEmpty()) {
                meta.setDisplayName(colorize(name));
            }

            if (lore != null && !lore.isEmpty()) {
                List<String> coloredLore = new ArrayList<>();
                for (String line : lore) {
                    coloredLore.add(colorize(line));
                }
                meta.setLore(coloredLore);
            }

            meta.setOwner(player.getName());
            skull.setItemMeta(meta);
        }

        return skull;
    }

    private static void setSkullTexture(SkullMeta meta, String texture) {
        try {
            // Usar reflexão para acessar GameProfile e Property
            Class<?> gameProfileClass = Class.forName("com.mojang.authlib.GameProfile");
            Class<?> propertyClass = Class.forName("com.mojang.authlib.properties.Property");

            // Criar GameProfile
            Object profile = gameProfileClass.getConstructor(UUID.class, String.class)
                    .newInstance(UUID.randomUUID(), null);

            // Criar textura base64 completa se necessário
            String textureValue = texture;
            if (!texture.startsWith("{")) {
                textureValue = "{\"textures\":{\"SKIN\":{\"url\":\"http://textures.minecraft.net/texture/" + texture
                        + "\"}}}";
            }

            // Codificar em base64
            String encodedTexture = Base64.getEncoder().encodeToString(textureValue.getBytes());

            // Criar Property
            Object property = propertyClass.getConstructor(String.class, String.class)
                    .newInstance("textures", encodedTexture);

            // Adicionar propriedade ao profile
            Method getPropertiesMethod = gameProfileClass.getMethod("getProperties");
            Object properties = getPropertiesMethod.invoke(profile);
            Method putMethod = properties.getClass().getMethod("put", Object.class, Object.class);
            putMethod.invoke(properties, "textures", property);

            // Usar reflexão para definir o profile no SkullMeta
            Field profileField = meta.getClass().getDeclaredField("profile");
            profileField.setAccessible(true);
            profileField.set(meta, profile);

        } catch (Exception e) {
            // Se falhar, usar textura padrão
            System.out.println("Erro ao definir textura da skull: " + e.getMessage());
        }
    }

    public static ItemStack addGlow(ItemStack item) {
        if (item == null)
            return item;

        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            meta.addEnchant(Enchantment.DURABILITY, 1, true);
            meta.addItemFlags(ItemFlag.HIDE_ENCHANTS);
            item.setItemMeta(meta);
        }

        return item;
    }

    public static ItemStack createGlassPane(int color, String name) {
        return createItem(Material.STAINED_GLASS_PANE, 1, (short) color, name, null);
    }

    public static ItemStack createArrow(String name, List<String> lore, boolean isNext) {
        Material material = isNext ? Material.ARROW : Material.ARROW;
        return createItem(material, 1, (short) 0, name, lore);
    }

    public static boolean isValidMaterial(String materialName) {
        try {
            Material.valueOf(materialName);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static ItemStack parseItemFromConfig(String itemConfig, String name, List<String> lore) {
        if (itemConfig == null || itemConfig.isEmpty()) {
            return createItem(Material.STONE, 1, (short) 0, name, lore);
        }

        // Verificar se é uma textura de skull
        if (itemConfig.length() > 16 && !itemConfig.contains(":")) {
            return createSkull(itemConfig, name, lore);
        }

        // Verificar se é um material:data
        if (itemConfig.contains(":")) {
            String[] parts = itemConfig.split(":");
            try {
                Material material = Material.valueOf(parts[0]);
                short data = parts.length > 1 ? Short.parseShort(parts[1]) : 0;
                return createItem(material, 1, data, name, lore);
            } catch (Exception e) {
                // Se falhar, tentar como textura de skull
                return createSkull(itemConfig, name, lore);
            }
        }

        // Tentar como material simples
        try {
            Material material = Material.valueOf(itemConfig);
            return createItem(material, 1, (short) 0, name, lore);
        } catch (Exception e) {
            // Último recurso - item padrão
            return createItem(Material.STONE, 1, (short) 0, name, lore);
        }
    }

    public static List<String> wrapLore(String text, int maxLength) {
        List<String> lore = new ArrayList<>();
        String[] words = text.split(" ");
        StringBuilder line = new StringBuilder();

        for (String word : words) {
            if (line.length() + word.length() + 1 > maxLength) {
                if (line.length() > 0) {
                    lore.add(line.toString());
                    line = new StringBuilder();
                }
            }

            if (line.length() > 0) {
                line.append(" ");
            }
            line.append(word);
        }

        if (line.length() > 0) {
            lore.add(line.toString());
        }

        return lore;
    }
}
