package com.atlasplugins.pescaria.gui;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.models.Mission;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class PescaGUI {

    private final Pescaria plugin;

    public PescaGUI(Pescaria plugin) {
        this.plugin = plugin;
    }

    public void openMainMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§b§lPESCARIA §8- §fMenu Principal");

        UUID uuid = player.getUniqueId();
        int fishes = plugin.getPlayerManager().getPlayerFishes(uuid);
        double coins = plugin.getEconomy().getBalance(player);

        // Encantamentos da Vara (slot 10)
        ItemStack enchantItem = createSkullItem("b2f79016cad84d1ae21609c4813782598e387961be13c15682752f126dce7a");
        ItemMeta enchantMeta = enchantItem.getItemMeta();
        enchantMeta.setDisplayName("§d§lEncantamentos da Vara");
        enchantMeta.setLore(Arrays.asList(
                "§7Gerencie os encantamentos",
                "§7da sua vara de pesca.",
                "",
                "§f▸ §7Sorte, Velocidade, Fortuna",
                "§f▸ §7Experiência, Magnetismo, Resistência",
                "",
                "§aClique para abrir!"));
        enchantItem.setItemMeta(enchantMeta);
        gui.setItem(10, enchantItem);

        // TOP Jogadores (slot 11)
        ItemStack topItem = createSkullItem("3ed1aba73f639f4bc42bd48196c715197be2712c3b962c97ebf9e9ed8efa025");
        ItemMeta topMeta = topItem.getItemMeta();
        topMeta.setDisplayName("§e§lTOP Pescadores");
        topMeta.setLore(Arrays.asList(
                "§7Veja o ranking dos jogadores",
                "§7que mais pescaram no servidor.",
                "",
                "§f▸ §7Sua posição no ranking",
                "§f▸ §7Melhores pescadores",
                "",
                "§aClique para ver!"));
        topItem.setItemMeta(topMeta);
        gui.setItem(11, topItem);

        // Boosters (slot 12)
        ItemStack boosterItem = createSkullItem("d5c6dc2bbf51c36cfc7714585a6a5683ef2b14d47d8ff714654a893f5da622");
        ItemMeta boosterMeta = boosterItem.getItemMeta();
        boosterMeta.setDisplayName("§a§lBoosters de Pesca");
        boosterMeta.setLore(Arrays.asList(
                "§7Ative boosters para ganhar",
                "§7mais peixes e experiência.",
                "",
                "§f▸ §7Boosters 2x, 3x, 5x",
                "§f▸ §7Duração configurável",
                "",
                "§aClique para ver!"));
        boosterItem.setItemMeta(boosterMeta);
        gui.setItem(12, boosterItem);

        // Loja de Peixes (slot 13)
        ItemStack lojaItem = createSkullItem("6cc2c3702491935fec1b7757c48a0c6615b7c3833b5c8cc5b9e5b7c2b5c8cc5");
        ItemMeta lojaMeta = lojaItem.getItemMeta();
        lojaMeta.setDisplayName("§a§lLoja de Peixes");
        lojaMeta.setLore(Arrays.asList(
                "§7Venda seus peixes por",
                "§7coins ou cash.",
                "",
                "§f▸ §7Vender por Coins",
                "§f▸ §7Vender por Cash",
                "§f▸ §7Comprar itens especiais",
                "",
                "§aClique para abrir!"));
        lojaItem.setItemMeta(lojaMeta);
        gui.setItem(13, lojaItem);

        // Missões (slot 14)
        ItemStack missionItem = createSkullItem("a5a0b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1");
        ItemMeta missionMeta = missionItem.getItemMeta();
        missionMeta.setDisplayName("§e§lMissões de Pesca");
        missionMeta.setLore(Arrays.asList(
                "§7Complete missões para ganhar",
                "§7recompensas especiais.",
                "",
                "§f▸ §7Missões diárias",
                "§f▸ §7Recompensas únicas",
                "",
                "§aClique para ver!"));
        missionItem.setItemMeta(missionMeta);
        gui.setItem(14, missionItem);

        // Botão de Teleporte (slot 15) - muda baseado no mundo atual
        String currentWorld = player.getWorld().getName();
        String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();

        ItemStack tpItem;
        ItemMeta tpMeta;

        if (currentWorld.equalsIgnoreCase(pescariaWorld)) {
            // Jogador está no mundo de pesca - mostrar botão de sair
            tpItem = createSkullItem("19bf3292e126a105b54eba713aa1b152d541a1d8938829c56364d178ed22bf");
            tpMeta = tpItem.getItemMeta();
            tpMeta.setDisplayName("§c§lSair da Área de Pesca");
            tpMeta.setLore(Arrays.asList(
                    "§7Clique para voltar ao",
                    "§7spawn do servidor.",
                    "",
                    "§f▸ §7Teleporte instantâneo",
                    "§f▸ §7Itens serão mantidos",
                    "",
                    "§cClique para sair!"));
        } else {
            // Jogador não está no mundo de pesca - mostrar botão de entrar
            tpItem = createSkullItem("c9c8881e42915a9d29bb61a16fb26d059913204d265df5b439b3d792acd56");
            tpMeta = tpItem.getItemMeta();
            tpMeta.setDisplayName("§b§lIr para Área de Pesca");
            tpMeta.setLore(Arrays.asList(
                    "§7Teleporte para o mundo",
                    "§7exclusivo de pesca.",
                    "",
                    "§f▸ §7Mundo dedicado à pesca",
                    "§f▸ §7Missões e recompensas",
                    "§f▸ §7Encantamentos especiais",
                    "",
                    "§aClique para ir!"));
        }

        tpItem.setItemMeta(tpMeta);
        gui.setItem(15, tpItem);

        // Informações do Jogador (slot 16)
        ItemStack infoItem = createSkullItem(player.getName());
        ItemMeta infoMeta = infoItem.getItemMeta();
        infoMeta.setDisplayName("§e§lSuas Informações");

        // Obter cash
        String cashBalance = "0";
        if (plugin.getSCashIntegration() != null && plugin.getSCashIntegration().isEnabled()) {
            cashBalance = String.format("%.0f", plugin.getSCashIntegration().getBalance(player));
        } else if (plugin.getBetterEconomyIntegration() != null && plugin.getBetterEconomyIntegration().isEnabled()) {
            cashBalance = String.format("%.0f", plugin.getBetterEconomyIntegration().getBalance(player));
        }

        infoMeta.setLore(Arrays.asList(
                "§7Suas estatísticas de pesca:",
                "",
                "§f▸ §7Peixes: §e✧" + fishes,
                "§f▸ §7Coins: §a$" + String.format("%.0f", coins),
                "§f▸ §7Cash: §6✪" + cashBalance,
                "",
                "§f▸ §7Missões Concluídas: §b" + plugin.getPlayerManager().getCompletedMissions(uuid),
                "§f▸ §7Nível da Vara: §d" + calculateRodLevel(uuid),
                "",
                "§7Continue pescando para",
                "§7melhorar suas estatísticas!"));
        infoItem.setItemMeta(infoMeta);
        gui.setItem(16, infoItem);

        player.openInventory(gui);
    }

    public void openMissionsMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 27, "§e§lMissões de Pesca");

        UUID uuid = player.getUniqueId();
        List<Mission> missions = plugin.getMissionManager().getMissions();
        Mission currentMission = plugin.getPlayerManager().getCurrentMission(uuid);
        int completedMissions = plugin.getPlayerManager().getCompletedMissions(uuid);

        // Slots para missões: 10, 11, 12, 13, 14, 15, 16
        int[] missionSlots = { 10, 11, 12, 13, 14, 15, 16 };

        for (int i = 0; i < Math.min(missions.size(), missionSlots.length); i++) {
            Mission mission = missions.get(i);
            ItemStack missionItem;
            ItemMeta meta;

            // Obter primeira recompensa como exemplo
            String recompensaTexto = "Sem recompensas";
            if (!mission.getRecompensas().isEmpty()) {
                String primeiraRecompensa = mission.getRecompensas().keySet().iterator().next();
                recompensaTexto = primeiraRecompensa;
            }

            if (i < completedMissions) {
                // Missão completada
                missionItem = new ItemStack(Material.EMERALD);
                meta = missionItem.getItemMeta();
                meta.setDisplayName("§aMissão #" + (i + 1));
                meta.setLore(Arrays.asList(
                        "§fPara completar esta missão basta",
                        "§fpescar §e" + mission.getPeixesToComplete() + " §fpeixes.",
                        "",
                        "§fRecompensas:",
                        "§f- §e" + recompensaTexto,
                        "",
                        "§fStatus: §aCompleta",
                        "§a100% §a■■■■■■■■■■"));
            } else if (mission.equals(currentMission)) {
                // Missão atual
                missionItem = new ItemStack(Material.RAW_FISH);
                meta = missionItem.getItemMeta();
                meta.setDisplayName("§eMissão #" + (i + 1));

                int progress = plugin.getPlayerManager().getMissionProgress(uuid);
                int total = mission.getPeixesToComplete();
                double percentage = (double) progress / total * 100;

                meta.setLore(Arrays.asList(
                        "§fPara completar esta missão basta",
                        "§fpescar §e" + mission.getPeixesToComplete() + " §fpeixes.",
                        "",
                        "§fRecompensas:",
                        "§f- §e" + recompensaTexto,
                        "",
                        "§fStatus: §eEm progresso",
                        String.format("§e%.1f%% §e■■■■■■■■■■", percentage)));
            } else {
                // Missão bloqueada
                missionItem = new ItemStack(Material.PAPER);
                meta = missionItem.getItemMeta();
                meta.setDisplayName("§cMissão #" + (i + 1));
                meta.setLore(Arrays.asList(
                        "§fPara completar esta missão basta",
                        "§fpescar §e" + mission.getPeixesToComplete() + " §fpeixes.",
                        "",
                        "§fRecompensas:",
                        "§f- §e" + recompensaTexto,
                        "",
                        "§fStatus: §cNão liberada",
                        "§c0% §c■■■■■■■■■■"));
            }

            missionItem.setItemMeta(meta);
            gui.setItem(missionSlots[i], missionItem);
        }

        // Item para voltar
        ItemStack backItem = new ItemStack(Material.ARROW);
        ItemMeta backMeta = backItem.getItemMeta();
        backMeta.setDisplayName("§cVoltar");
        backItem.setItemMeta(backMeta);
        gui.setItem(22, backItem);

        player.openInventory(gui);
    }

    private ItemStack createSkullItem(String texture) {
        // Para Minecraft 1.8.8, usar materiais específicos baseados na função
        if (texture.contains("enchant")
                || texture.equals("b2f79016cad84d1ae21609c4813782598e387961be13c15682752f126dce7a")) {
            return new ItemStack(Material.ENCHANTMENT_TABLE);
        } else if (texture.contains("top")
                || texture.equals("3ed1aba73f639f4bc42bd48196c715197be2712c3b962c97ebf9e9ed8efa025")) {
            return new ItemStack(Material.BOOK_AND_QUILL);
        } else if (texture.contains("boost")
                || texture.equals("d5c6dc2bbf51c36cfc7714585a6a5683ef2b14d47d8ff714654a893f5da622")) {
            return new ItemStack(Material.EXP_BOTTLE);
        } else if (texture.contains("loja")
                || texture.equals("6cc2c3702491935fec1b7757c48a0c6615b7c3833b5c8cc5b9e5b7c2b5c8cc5")) {
            return new ItemStack(Material.SIGN);
        } else if (texture.contains("miss")
                || texture.equals("a5a0b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1b5c9b1")) {
            return new ItemStack(Material.PAPER);
        } else if (texture.contains("sair")
                || texture.equals("19bf3292e126a105b54eba713aa1b152d541a1d8938829c56364d178ed22bf")) {
            return new ItemStack(Material.BARRIER);
        } else if (texture.contains("ir")
                || texture.equals("c9c8881e42915a9d29bb61a16fb26d059913204d265df5b439b3d792acd56")) {
            return new ItemStack(Material.ENDER_PEARL);
        } else {
            // Para nome de jogador, usar cabeça de jogador
            ItemStack skull = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
            if (texture.length() <= 16) { // Nome de jogador válido
                org.bukkit.inventory.meta.SkullMeta skullMeta = (org.bukkit.inventory.meta.SkullMeta) skull
                        .getItemMeta();
                skullMeta.setOwner(texture);
                skull.setItemMeta(skullMeta);
            }
            return skull;
        }
    }

    private int calculateRodLevel(UUID uuid) {
        int xp = plugin.getConfigManager().getDataConfig().getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10;
    }
}
