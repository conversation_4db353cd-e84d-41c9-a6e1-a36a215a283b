package com.atlasplugins.pescaria.listeners;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.gui.EnchantmentGUI;
import com.atlasplugins.pescaria.gui.PescaGUI;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class G<PERSON>Listener implements Listener {

    private final Pescaria plugin;
    private final PescaGUI pescaGUI;
    private final EnchantmentGUI enchantmentGUI;

    public GUIListener(Pescaria plugin) {
        this.plugin = plugin;
        this.pescaGUI = new PescaGUI(plugin);
        this.enchantmentGUI = new EnchantmentGUI(plugin);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player))
            return;

        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();

        if (title.equals("§b§lPESCARIA §8- §fMenu Principal")) {
            event.setCancelled(true);

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || !clickedItem.hasItemMeta())
                return;

            String displayName = clickedItem.getItemMeta().getDisplayName();

            if (displayName.equals("§e§lMissões de Pesca")) {
                pescaGUI.openMissionsMenu(player);
            } else if (displayName.equals("§b§lIr para Área de Pesca")) {
                player.closeInventory();
                plugin.getServer().dispatchCommand(player, "pesca tp");
            } else if (displayName.equals("§c§lSair da Área de Pesca")) {
                player.closeInventory();
                plugin.getServer().dispatchCommand(player, "pesca voltar");
            } else if (displayName.equals("§d§lEncantamentos da Vara")) {
                enchantmentGUI.openEnchantmentMenu(player);
            } else if (displayName.equals("§a§lLoja de Peixes")) {
                player.closeInventory();
                plugin.getServer().dispatchCommand(player, "pesca loja");
            }
        } else if (title.equals("§ePescaria - Missões")) {
            event.setCancelled(true);

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || !clickedItem.hasItemMeta())
                return;

            String displayName = clickedItem.getItemMeta().getDisplayName();

            if (displayName.equals("§cVoltar")) {
                pescaGUI.openMainMenu(player);
            }
        } else if (title.equals("§dEncantamentos da Vara")) {
            event.setCancelled(true);

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || !clickedItem.hasItemMeta())
                return;

            String displayName = clickedItem.getItemMeta().getDisplayName();

            if (displayName.equals("§cVoltar")) {
                pescaGUI.openMainMenu(player);
            } else {
                // Tentar fazer upgrade do encantamento
                enchantmentGUI.handleEnchantmentClick(player, displayName);
            }
        } else if (title.equals("§e§lMissões de Pesca")) {
            event.setCancelled(true);

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || !clickedItem.hasItemMeta())
                return;

            String displayName = clickedItem.getItemMeta().getDisplayName();

            if (displayName.equals("§cVoltar")) {
                pescaGUI.openMainMenu(player);
            } else if (displayName.startsWith("§e§l⚡")) {
                // Missão pronta para resgatar
                handleMissionClaim(player, displayName);
            }
        } else if (title.equals("§a§lLoja de Pesca")) {
            event.setCancelled(true);

            ItemStack clickedItem = event.getCurrentItem();
            if (clickedItem == null || !clickedItem.hasItemMeta())
                return;

            String displayName = clickedItem.getItemMeta().getDisplayName();

            if (displayName.equals("§cVoltar")) {
                pescaGUI.openMainMenu(player);
            } else if (displayName.startsWith("§a§lCoins")) {
                handleSellFishForCoins(player, event.isRightClick());
            } else if (displayName.startsWith("§6§lCash")) {
                handleSellFishForCash(player, event.isRightClick());
            } else if (displayName.startsWith("§b§lDiamante")) {
                // Comprar diamante
                handleBuyDiamond(player);
            }
        }
    }

    private void handleSellFishForCoins(Player player, boolean sellAll) {
        java.util.UUID uuid = player.getUniqueId();
        int playerFishes = plugin.getPlayerManager().getPlayerFishes(uuid);

        if (playerFishes <= 0) {
            player.sendMessage("§c§lPESCARIA §fVocê não tem peixes para vender!");
            return;
        }

        int fishesToSell = sellAll ? playerFishes : Math.min(64, playerFishes);
        double pricePerFish = plugin.getConfigManager().getFishPriceCoins();
        double totalPrice = fishesToSell * pricePerFish;

        // Remover peixes
        plugin.getPlayerManager().setPlayerFishes(uuid, playerFishes - fishesToSell);

        // Adicionar dinheiro
        plugin.getEconomy().depositPlayer(player, totalPrice);

        player.sendMessage("§a§lPESCARIA §fVocê vendeu §e" + fishesToSell + " §fpeixes por §a$" + totalPrice + "§f!");
        player.closeInventory();

        // Reabrir loja para atualizar valores
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getServer().dispatchCommand(player, "pesca loja");
        }, 5L);
    }

    private void handleSellFishForCash(Player player, boolean sellAll) {
        java.util.UUID uuid = player.getUniqueId();
        int playerFishes = plugin.getPlayerManager().getPlayerFishes(uuid);

        if (playerFishes <= 0) {
            player.sendMessage("§c§lPESCARIA §fVocê não tem peixes para vender!");
            return;
        }

        int fishesToSell = sellAll ? playerFishes : Math.min(64, playerFishes);
        double pricePerFish = plugin.getConfigManager().getFishPriceCash();
        double totalPrice = fishesToSell * pricePerFish;

        // Tentar vender por sCash primeiro, depois BetterEconomy, depois coins
        boolean sold = false;
        String cashType = "";

        if (plugin.getSCashIntegration() != null && plugin.getSCashIntegration().isEnabled()) {
            try {
                sold = plugin.getSCashIntegration().addBalance(player, totalPrice);
                if (sold) {
                    cashType = "sCash";
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao vender por sCash: " + e.getMessage());
            }
        }

        if (!sold && plugin.getBetterEconomyIntegration() != null && plugin.getBetterEconomyIntegration().isEnabled()) {
            try {
                sold = plugin.getBetterEconomyIntegration().addBalance(player, totalPrice);
                if (sold) {
                    cashType = "BetterEconomy";
                }
            } catch (Exception e) {
                plugin.getLogger().warning("Erro ao vender por BetterEconomy: " + e.getMessage());
            }
        }

        if (!sold) {
            // Fallback para coins normais
            try {
                plugin.getEconomy().depositPlayer(player, totalPrice);
                sold = true;
                cashType = "Coins";
            } catch (Exception e) {
                player.sendMessage("§c§lPESCARIA §fSistema de cash não disponível!");
                return;
            }
        }

        if (sold) {
            // Remover peixes apenas se a venda foi bem-sucedida
            plugin.getPlayerManager().setPlayerFishes(uuid, playerFishes - fishesToSell);
            player.sendMessage("§a§lPESCARIA §fVocê vendeu §e" + fishesToSell + " §fpeixes por §6✪" + totalPrice
                    + " §fde " + cashType + "!");
        } else {
            player.sendMessage("§c§lPESCARIA §fErro ao processar venda!");
            return;
        }

        player.closeInventory();

        // Reabrir loja para atualizar valores
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            plugin.getServer().dispatchCommand(player, "pesca loja");
        }, 5L);
    }

    private void handleMissionClaim(Player player, String displayName) {
        // Simular resgate de missão
        String missionName = displayName.replace("§e§l⚡ ", "");

        // Dar recompensa simples
        plugin.getEconomy().depositPlayer(player, 1000);
        plugin.getPlayerManager().addPlayerFish(player.getUniqueId());
        plugin.getPlayerManager().addPlayerFish(player.getUniqueId());
        plugin.getPlayerManager().addPlayerFish(player.getUniqueId());

        player.sendMessage("§a§lPESCARIA §fVocê resgatou a missão §e" + missionName + "§f!");
        player.sendMessage("§a§lRECOMPENSA §f+1000 coins, +3 peixes!");

        // Reabrir menu para atualizar
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            pescaGUI.openMissionsMenu(player);
        }, 5L);
    }

    private void handleBuyDiamond(Player player) {
        java.util.UUID uuid = player.getUniqueId();
        double balance = plugin.getEconomy().getBalance(player);
        double price = 18000; // 18K

        if (balance < price) {
            player.sendMessage("§c§lPESCARIA §fVocê precisa de §e" + price + " coins §fpara comprar este diamante!");
            return;
        }

        // Retirar dinheiro
        plugin.getEconomy().withdrawPlayer(player, price);

        // Dar diamante
        org.bukkit.inventory.ItemStack diamond = new org.bukkit.inventory.ItemStack(org.bukkit.Material.DIAMOND);
        org.bukkit.inventory.meta.ItemMeta meta = diamond.getItemMeta();
        meta.setDisplayName("§b§lDiamante Especial");
        meta.setLore(java.util.Arrays.asList(
                "§7Um diamante muito bonito",
                "§7comprado na loja de pesca!"));
        diamond.setItemMeta(meta);

        player.getInventory().addItem(diamond);
        player.sendMessage("§a§lPESCARIA §fVocê comprou um §bDiamante Especial §fpor §e" + price + " coins§f!");

        player.closeInventory();
    }

    public PescaGUI getPescaGUI() {
        return pescaGUI;
    }
}
