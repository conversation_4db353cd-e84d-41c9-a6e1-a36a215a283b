package com.stoneplugins.stonecactos.data;

import org.bukkit.Material;

public class BatteryItem {
    
    private final String id;
    private final Material material;
    private final short data;
    private final String name;
    private final double batteryValue;
    private final String[] lore;
    
    public BatteryItem(String id, Material material, short data, String name, double batteryValue, String[] lore) {
        this.id = id;
        this.material = material;
        this.data = data;
        this.name = name;
        this.batteryValue = batteryValue;
        this.lore = lore;
    }
    
    public String getId() {
        return id;
    }
    
    public Material getMaterial() {
        return material;
    }
    
    public short getData() {
        return data;
    }
    
    public String getName() {
        return name;
    }
    
    public double getBatteryValue() {
        return batteryValue;
    }
    
    public String[] getLore() {
        return lore;
    }
}