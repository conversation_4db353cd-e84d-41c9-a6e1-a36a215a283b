# This is the main configuration file for PaperSpigot.
# As you can see, there's tons to configure. Some options may impact gameplay, so use
# with caution, and make sure you know what each option does before configuring.
# 
# If you need help with the configuration or have any questions related to PaperSpigot,
# join us at the IRC.
# 
# IRC: #paperspigot @ irc.spi.gt ( http://irc.spi.gt/iris/?channels=PaperSpigot )

config-version: 9
effect-modifiers:
  strength: 1.3
  weakness: -0.5
stackable-buckets:
  lava: false
  water: false
  milk: false
data-value-allowed-items: []
settings:
  baby-zombie-movement-speed: 0.5
  limit-player-interactions: true
warnWhenSettingExcessiveVelocity: true
world-settings:
  default:
    verbose: true
    all-chunks-are-slime-chunks: false
    allow-undead-horse-leashing: false
    water-over-lava-flow-speed: 5
    tnt-entity-height-nerf: 0
    disable-explosion-knockback: false
    fix-cannons: false
    disable-thunder: false
    tick-next-tick-list-cap: 10000
    tick-next-tick-list-cap-ignores-redstone: false
    game-mechanics:
      disable-end-credits: false
      boats-drop-boats: false
      disable-player-crits: false
      disable-chest-cat-detection: false
    container-update-tick-rate: 1
    keep-spawn-loaded: true
    disable-mood-sounds: false
    falling-blocks-collide-with-signs: false
    portal-search-radius: 128
    disable-ice-and-snow: false
    generator-settings:
      canyon: true
      caves: true
      dungeon: true
      fortress: true
      mineshaft: true
      monument: true
      stronghold: true
      temple: true
      village: true
      flat-bedrock: false
    allow-block-location-tab-completion: true
    use-async-lighting: false
    remove-invalid-mob-spawner-tile-entities: true
    player-blocking-damage-multiplier: 0.5
    tnt-explosion-volume: 4.0
    optimize-explosions: false
    mob-spawner-tick-rate: 1
    max-growth-height:
      cactus: 3
      reeds: 3
    falling-block-height-nerf: 0
    nether-ceiling-void-damage: false
    use-hopper-check: false
    disable-teleportation-suffocation-check: false
    cache-chunk-maps: false
    fishing-time-range:
      MinimumTicks: 100
      MaximumTicks: 900
    squid-spawn-height:
      minimum: 45.0
      maximum: 63.0
    despawn-ranges:
      soft: 32
      hard: 128
    player-exhaustion:
      block-break: 0.02500000037252903
      swimming: 0.014999999664723873
    remove-unloaded:
      enderpearls: true
      tnt-entities: true
      falling-blocks: true
    fast-drain:
      lava: false
      water: false
    load-chunks:
      enderpearls: false
      tnt-entities: false
      falling-blocks: false
    lava-flow-speed:
      normal: 30
      nether: 10
