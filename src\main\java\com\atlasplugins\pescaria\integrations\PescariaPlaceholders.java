package com.atlasplugins.pescaria.integrations;

import com.atlasplugins.pescaria.Pescaria;
import com.atlasplugins.pescaria.enchants.CustomEnchantments;
import org.bukkit.entity.Player;

import java.util.UUID;

public class PescariaPlaceholders {

    private final Pescaria plugin;

    public PescariaPlaceholders(Pescaria plugin) {
        this.plugin = plugin;
    }

    public String getIdentifier() {
        return "pescaria";
    }

    public String getAuthor() {
        return "AtlasPlugins";
    }

    public String getVersion() {
        return "1.0.0";
    }

    public boolean persist() {
        return true;
    }

    public String onPlaceholderRequest(Player player, String params) {
        if (player == null)
            return "";

        UUID uuid = player.getUniqueId();

        switch (params.toLowerCase()) {
            // Placeholders básicos
            case "peixes":
                return String.valueOf(plugin.getPlayerManager().getPlayerFishes(uuid));

            case "peixes_formatado":
                int fishes = plugin.getPlayerManager().getPlayerFishes(uuid);
                return plugin.getConfigManager().formatNumber(fishes);

            // Placeholders de missões
            case "missao_atual":
                return plugin.getPlayerManager().getCurrentMission(uuid) != null
                        ? plugin.getPlayerManager().getCurrentMission(uuid).getName()
                        : "Nenhuma";

            case "missao_progresso":
                return String.valueOf(plugin.getPlayerManager().getMissionProgress(uuid));

            case "missao_total":
                return plugin.getPlayerManager().getCurrentMission(uuid) != null
                        ? String.valueOf(plugin.getPlayerManager().getCurrentMission(uuid).getPeixesToComplete())
                        : "0";

            case "missao_progresso_barra":
                return plugin.getPlayerManager().getMissionProgressBar(uuid);

            case "missoes_concluidas":
                return String.valueOf(plugin.getPlayerManager().getCompletedMissions(uuid));

            // Placeholders da vara
            case "vara_nivel":
                int xp = plugin.getConfigManager().getDataConfig()
                        .getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
                return String.valueOf(calculateLevel(xp));

            case "vara_xp":
                return String.valueOf(plugin.getConfigManager().getDataConfig()
                        .getInt("Players." + uuid.toString() + ".FishingRodXP", 0));

            case "vara_xp_proximo":
                int currentXP = plugin.getConfigManager().getDataConfig()
                        .getInt("Players." + uuid.toString() + ".FishingRodXP", 0);
                int level = calculateLevel(currentXP);
                return String.valueOf(getXPForNextLevel(level));

            // Placeholders de economia
            case "economia_saldo":
                if (plugin.getEconomyIntegration() != null) {
                    return plugin.getEconomyIntegration()
                            .formatMoney(plugin.getEconomyIntegration().getBalance(player));
                }
                return "0";

            case "economia_tipo":
                if (plugin.getEconomyIntegration() != null) {
                    return plugin.getEconomyIntegration().getEconomyName();
                }
                return "Nenhuma";

            // Placeholder genérico para cash
            case "cash":
                // Tentar sCash primeiro
                try {
                    SCashIntegration sCash = new SCashIntegration(plugin);
                    return sCash.getBalanceFormatted(player);
                } catch (Exception e) {
                    // Se falhar, tentar BetterEconomy
                    try {
                        BetterEconomyIntegration betterEco = new BetterEconomyIntegration(plugin);
                        return betterEco.getBalanceFormatted(player);
                    } catch (Exception e2) {
                        return "0";
                    }
                }

                // Placeholders do sCash
            case "scash_saldo":
                SCashIntegration sCash = new SCashIntegration(plugin);
                return sCash.getBalanceFormatted(player);

            case "scash_saldo_simbolo":
                SCashIntegration sCashSymbol = new SCashIntegration(plugin);
                return sCashSymbol.getBalanceWithSymbol(player);

            case "scash_simbolo":
                SCashIntegration sCashSym = new SCashIntegration(plugin);
                return sCashSym.getCurrencySymbol();

            // Placeholders do BetterEconomy
            case "bettereconomy_saldo":
                BetterEconomyIntegration betterEco = new BetterEconomyIntegration(plugin);
                return betterEco.getBalanceFormatted(player);

            case "bettereconomy_saldo_simbolo":
                BetterEconomyIntegration betterEcoSymbol = new BetterEconomyIntegration(plugin);
                return betterEcoSymbol.getBalanceWithSymbol(player);

            case "bettereconomy_ranking":
                BetterEconomyIntegration betterEcoRank = new BetterEconomyIntegration(plugin);
                return betterEcoRank.getPlayerRankFormatted(player);

            case "bettereconomy_simbolo":
                BetterEconomyIntegration betterEcoSym = new BetterEconomyIntegration(plugin);
                return betterEcoSym.getCurrencySymbol();

            case "bettereconomy_moeda":
                BetterEconomyIntegration betterEcoName = new BetterEconomyIntegration(plugin);
                return betterEcoName.getCurrencyName();

            // Placeholders de mundo
            case "mundo_pesca":
                return plugin.getConfigManager().getPescariaWorldName();

            case "esta_no_mundo_pesca":
                String currentWorld = player.getWorld().getName();
                String pescariaWorld = plugin.getConfigManager().getPescariaWorldName();
                return String.valueOf(currentWorld.equalsIgnoreCase(pescariaWorld));

            // Placeholders de status
            case "esta_pescando":
                // TODO: Implementar verificação se está pescando
                return "false";

            case "booster_ativo":
                // TODO: Implementar sistema de boosters
                return "Nenhum";

            case "booster_tempo":
                // TODO: Implementar sistema de boosters
                return "0";

            // Placeholders de ranking
            case "top_posicao":
                // TODO: Implementar sistema de ranking
                return "N/A";

            default:
                // Verificar se é um placeholder de encantamento
                if (params.startsWith("encantamento_")) {
                    String enchantName = params.substring("encantamento_".length());
                    return getEnchantmentLevel(player, enchantName);
                }

                // Verificar se é um placeholder de top
                if (params.startsWith("top_")) {
                    return handleTopPlaceholder(params);
                }

                return null;
        }
    }

    private String getEnchantmentLevel(Player player, String enchantName) {
        try {
            CustomEnchantments enchants = new CustomEnchantments(plugin);
            enchants.loadPlayerEnchants(player.getUniqueId());

            CustomEnchantments.EnchantType type = null;

            switch (enchantName.toLowerCase()) {
                case "sorte":
                    type = CustomEnchantments.EnchantType.SORTE;
                    break;
                case "velocidade":
                    type = CustomEnchantments.EnchantType.VELOCIDADE;
                    break;
                case "fortuna":
                    type = CustomEnchantments.EnchantType.FORTUNA;
                    break;
                case "experiencia":
                    type = CustomEnchantments.EnchantType.EXPERIENCIA;
                    break;
                case "magnetismo":
                    type = CustomEnchantments.EnchantType.MAGNETISMO;
                    break;
                case "resistencia":
                    type = CustomEnchantments.EnchantType.RESISTENCIA;
                    break;
            }

            if (type != null) {
                return String.valueOf(enchants.getEnchantLevel(player.getUniqueId(), type));
            }
        } catch (Exception e) {
            // Ignorar erros
        }
        return "0";
    }

    private String handleTopPlaceholder(String params) {
        // TODO: Implementar sistema de ranking
        return "N/A";
    }

    private int calculateLevel(int xp) {
        if (xp < 250)
            return 1;
        if (xp < 500)
            return 2;
        if (xp < 1000)
            return 3;
        if (xp < 2000)
            return 4;
        if (xp < 4000)
            return 5;
        if (xp < 8000)
            return 6;
        if (xp < 16000)
            return 7;
        if (xp < 32000)
            return 8;
        if (xp < 64000)
            return 9;
        return 10;
    }

    private int getXPForNextLevel(int currentLevel) {
        switch (currentLevel) {
            case 1:
                return 250;
            case 2:
                return 500;
            case 3:
                return 1000;
            case 4:
                return 2000;
            case 5:
                return 4000;
            case 6:
                return 8000;
            case 7:
                return 16000;
            case 8:
                return 32000;
            case 9:
                return 64000;
            default:
                return 64000;
        }
    }
}
