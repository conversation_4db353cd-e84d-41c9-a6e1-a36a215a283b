package com.stoneplugins.stonetags.data;

import java.util.UUID;

public class PlayerData {

    private final UUID playerId;
    private String selectedTagId;
    private String selectedSuffixId;
    private String customSuffix;
    private String customName;
    private long lastUpdated;

    public PlayerData(UUID playerId) {
        this.playerId = playerId;
        this.selectedTagId = null;
        this.selectedSuffixId = null;
        this.customSuffix = null;
        this.customName = null;
        this.lastUpdated = System.currentTimeMillis();
    }

    public PlayerData(UUID playerId, String selectedTagId, String selectedSuffixId, String customSuffix,
            String customName, long lastUpdated) {
        this.playerId = playerId;
        this.selectedTagId = selectedTagId;
        this.selectedSuffixId = selectedSuffixId;
        this.customSuffix = customSuffix;
        this.customName = customName;
        this.lastUpdated = lastUpdated;
    }

    // Getters
    public UUID getPlayerId() {
        return playerId;
    }

    public String getSelectedTagId() {
        return selectedTagId;
    }

    public String getSelectedSuffixId() {
        return selectedSuffixId;
    }

    public String getCustomSuffix() {
        return customSuffix;
    }

    public String getCustomName() {
        return customName;
    }

    public long getLastUpdated() {
        return lastUpdated;
    }

    // Setters
    public void setSelectedTagId(String selectedTagId) {
        this.selectedTagId = selectedTagId;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void setSelectedSuffixId(String selectedSuffixId) {
        this.selectedSuffixId = selectedSuffixId;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void setCustomSuffix(String customSuffix) {
        this.customSuffix = customSuffix;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void setCustomName(String customName) {
        this.customName = customName;
        this.lastUpdated = System.currentTimeMillis();
    }

    // Métodos utilitários
    public boolean hasSelectedTag() {
        return selectedTagId != null && !selectedTagId.isEmpty();
    }

    public boolean hasSelectedSuffix() {
        return selectedSuffixId != null && !selectedSuffixId.isEmpty();
    }

    public boolean hasCustomSuffix() {
        return customSuffix != null && !customSuffix.isEmpty();
    }

    public boolean hasCustomName() {
        return customName != null && !customName.isEmpty();
    }

    public void removeTag() {
        this.selectedTagId = null;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void removeSuffix() {
        this.selectedSuffixId = null;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void removeCustomSuffix() {
        this.customSuffix = null;
        this.lastUpdated = System.currentTimeMillis();
    }

    public void removeCustomName() {
        this.customName = null;
        this.lastUpdated = System.currentTimeMillis();
    }

    @Override
    public String toString() {
        return "PlayerData{" +
                "playerId=" + playerId +
                ", selectedTagId='" + selectedTagId + '\'' +
                ", customSuffix='" + customSuffix + '\'' +
                ", customName='" + customName + '\'' +
                ", lastUpdated=" + lastUpdated +
                '}';
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null || getClass() != obj.getClass())
            return false;
        PlayerData that = (PlayerData) obj;
        return playerId.equals(that.playerId);
    }

    @Override
    public int hashCode() {
        return playerId.hashCode();
    }
}
