package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;
import org.bukkit.scheduler.BukkitTask;

public class ProductionManager {
    
    private final StoneCactos plugin;
    private BukkitTask productionTask;
    private BukkitTask constructionTask;
    private boolean running = false;
    
    public ProductionManager(StoneCactos plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Inicia o sistema de produção
     */
    public void startProduction() {
        if (running) {
            return;
        }
        
        running = true;
        
        // Task de produção de cactos (a cada 5 segundos)
        productionTask = new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    plugin.getGeneratorManager().processProduction();
                } catch (Exception e) {
                    plugin.getLogger().severe("Erro no sistema de produção: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }.runTaskTimer(plugin, 100L, 100L); // 5 segundos
        
        // Task de construção de torres (a cada 2 segundos)
        constructionTask = new BukkitRunnable() {
            @Override
            public void run() {
                try {
                    plugin.getGeneratorManager().processConstruction();
                } catch (Exception e) {
                    plugin.getLogger().severe("Erro no sistema de construção: " + e.getMessage());
                    e.printStackTrace();
                }
            }
        }.runTaskTimer(plugin, 40L, 40L); // 2 segundos
        
        plugin.getLogger().info("Sistema de produção iniciado!");
    }
    
    /**
     * Para o sistema de produção
     */
    public void stopProduction() {
        if (!running) {
            return;
        }
        
        running = false;
        
        if (productionTask != null) {
            productionTask.cancel();
            productionTask = null;
        }
        
        if (constructionTask != null) {
            constructionTask.cancel();
            constructionTask = null;
        }
        
        plugin.getLogger().info("Sistema de produção parado!");
    }
    
    /**
     * Reinicia o sistema de produção
     */
    public void restartProduction() {
        stopProduction();
        startProduction();
    }
    
    /**
     * Desliga o sistema de produção
     */
    public void shutdown() {
        stopProduction();
        plugin.getLogger().info("Sistema de produção desligado!");
    }
    
    /**
     * Verifica se o sistema está rodando
     */
    public boolean isRunning() {
        return running;
    }
    
    /**
     * Obtém informações sobre o sistema de produção
     */
    public String getProductionInfo() {
        if (running) {
            return "§aSistema de produção: §fAtivo";
        } else {
            return "§cSistema de produção: §fInativo";
        }
    }
}
