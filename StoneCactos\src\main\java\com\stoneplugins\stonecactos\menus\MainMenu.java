package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class MainMenu extends BaseMenu {

    private final CactusGenerator generator;

    public MainMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("main"));
        int size = plugin.getConfigManager().getMenuSize("main");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.main.glassPaneSlots");
        fillGlassSlots(glassSlots);

        // Item de depósito
        setupDepositItem();

        // Item de armazém
        setupStorageItem();

        // Item de plantação
        setupPlantationItem();

        // Item de recompensas
        setupRewardsItem();

        // Item de bateria
        setupBatteryItem();

        // Item de booster
        setupBoosterItem();

        // Item de amigos
        setupFriendsItem();
    }

    private void setupDepositItem() {
        int slot = getConfigSlot("main", "deposit");
        String name = plugin.getConfigManager().getMenuItemName("main", "deposit");
        List<String> lore = plugin.getConfigManager().getMenuItemLore("main", "deposit");

        // Substituir placeholders
        lore = replacePlaceholders(lore,
                "%towers%", String.valueOf(generator.getTowers()),
                "%deposit%", "0", // TODO: Implementar sistema de depósito
                "%queue%", String.valueOf(generator.getConstructionQueue()),
                "%constructionTime%", String.valueOf(generator.getConstructionTime() / 1000) + "s",
                "%quantityPerConstruction%", String.valueOf(generator.getQuantityPerConstruction()),
                "%capacity%", String.valueOf(generator.getCapacity()));

        ItemStack item = createConfigItem("main", "deposit");
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            // Colorizar a lore antes de definir
            List<String> colorizedLore = new ArrayList<>();
            for (String line : lore) {
                colorizedLore.add(plugin.getMessageUtils().colorize(line));
            }
            meta.setLore(colorizedLore);
            item.setItemMeta(meta);
        }
        setItem(slot, item);
    }

    private void setupStorageItem() {
        int slot = getConfigSlot("main", "storage");
        ItemStack item = createConfigItem("main", "storage");
        setItem(slot, item);
    }

    private void setupPlantationItem() {
        int slot = getConfigSlot("main", "plantation");
        String name = plugin.getConfigManager().getMenuItemName("main", "plantation");
        List<String> lore = plugin.getConfigManager().getMenuItemLore("main", "plantation");

        // Substituir placeholders
        String boosterInfo = "§cNenhum";
        if (generator.hasActiveBooster()) {
            boosterInfo = "§a" + generator.getActiveBooster().getMultiplier() + "x §7(" +
                    generator.getActiveBooster().getTimeRemainingFormatted() + ")";
        }

        lore = replacePlaceholders(lore,
                "%booster%", boosterInfo,
                "%towers%", String.valueOf(generator.getTowers()),
                "%capacity%", String.valueOf(generator.getCapacity()),
                "%battery%", generator.getBatteryDisplay());

        ItemStack item = createConfigItem("main", "plantation");
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            // Colorizar a lore antes de definir
            List<String> colorizedLore = new ArrayList<>();
            for (String line : lore) {
                colorizedLore.add(plugin.getMessageUtils().colorize(line));
            }
            meta.setLore(colorizedLore);
            item.setItemMeta(meta);
        }
        setItem(slot, item);
    }

    private void setupRewardsItem() {
        int slot = getConfigSlot("main", "rewards");
        ItemStack item = createConfigItem("main", "rewards");
        setItem(slot, item);
    }

    private void setupBatteryItem() {
        int slot = getConfigSlot("main", "battery");
        String name = plugin.getConfigManager().getMenuItemName("main", "battery");
        List<String> lore = plugin.getConfigManager().getMenuItemLore("main", "battery");

        // Substituir placeholders
        String status = generator.isEnergySaving() ? "§aAtivado" : "§cDesativado";

        lore = replacePlaceholders(lore,
                "%status%", status);

        ItemStack item = createConfigItem("main", "battery");
        ItemMeta meta = item.getItemMeta();
        if (meta != null) {
            // Colorizar a lore antes de definir
            List<String> colorizedLore = new ArrayList<>();
            for (String line : lore) {
                colorizedLore.add(plugin.getMessageUtils().colorize(line));
            }
            meta.setLore(colorizedLore);
            item.setItemMeta(meta);
        }
        setItem(slot, item);
    }

    private void setupBoosterItem() {
        int slot = getConfigSlot("main", "booster");
        ItemStack item = createConfigItem("main", "booster");
        setItem(slot, item);
    }

    private void setupFriendsItem() {
        int slot = getConfigSlot("main", "friends");
        ItemStack item = createConfigItem("main", "friends");
        setItem(slot, item);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        // Slots dos itens
        int depositSlot = getConfigSlot("main", "deposit");
        int storageSlot = getConfigSlot("main", "storage");
        int plantationSlot = getConfigSlot("main", "plantation");
        int rewardsSlot = getConfigSlot("main", "rewards");
        int batterySlot = getConfigSlot("main", "battery");
        int boosterSlot = getConfigSlot("main", "booster");
        int friendsSlot = getConfigSlot("main", "friends");

        if (slot == depositSlot) {
            handleDepositClick(rightClick);
        } else if (slot == storageSlot) {
            handleStorageClick();
        } else if (slot == plantationSlot) {
            handlePlantationClick(rightClick, shiftClick);
        } else if (slot == rewardsSlot) {
            handleRewardsClick();
        } else if (slot == batterySlot) {
            handleBatteryClick();
        } else if (slot == boosterSlot) {
            handleBoosterClick();
        } else if (slot == friendsSlot) {
            handleFriendsClick(rightClick);
        }
    }

    private void handleDepositClick(boolean rightClick) {
        if (rightClick) {
            // Evoluir desenvolvimento
            plugin.getMenuManager().openUpgradeMenu(player, generator);
        } else {
            // Depositar torre
            plugin.getMenuManager().openDepositMenu(player, generator);
        }
    }

    private void handleStorageClick() {
        plugin.getMenuManager().openWarehouseMenu(player, generator);
    }

    private void handlePlantationClick(boolean rightClick, boolean shiftClick) {
        // Apenas mostrar informações detalhadas
        showPlantationInfo();
    }

    private void handleRewardsClick() {
        // new RewardsMenu(plugin, player).open();
        player.sendMessage("§cMenu de recompensas temporariamente desabilitado!");
    }

    private void handleBatteryClick() {
        // Alternar modo de economia de energia
        if (generator.isOwner(player) || generator.hasPermission(player.getUniqueId(), "manage")) {
            generator.setEnergySaving(!generator.isEnergySaving());
            plugin.getGeneratorManager().saveGenerator(generator);

            String status = generator.isEnergySaving() ? "ativado" : "desativado";
            player.sendMessage("§eModo de economia de energia " + status + "!");

            update();
        } else {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
        }
    }

    private void handleBoosterClick() {
        plugin.getMenuManager().openBoostersMenu(player, generator);
    }

    private void handleFriendsClick(boolean rightClick) {
        if (rightClick) {
            // Adicionar amigo
            if (generator.isOwner(player)) {
                player.sendMessage("§eDigite o nome do jogador no chat para adicionar como amigo:");
                player.sendMessage("§7Digite 'cancelar' para cancelar.");
                close();
                // TODO: Implementar sistema de input de chat
            } else {
                player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            }
        } else {
            // Abrir menu de amigos
            plugin.getMenuManager().openFriendsMenu(player, generator);
        }
    }

    private void showPlantationInfo() {
        player.sendMessage("§e§l=== INFORMAÇÕES DA PLANTAÇÃO ===");
        player.sendMessage("§fTorres: §e" + generator.getTowers() + "/" + generator.getCapacity());
        player.sendMessage("§fCactos armazenados: §a" + generator.getStoredCactus());
        player.sendMessage("§fBateria: " + generator.getBatteryDisplay());
        player.sendMessage("§fFila de construção: §b" + generator.getConstructionQueue());
        player.sendMessage("§fTempo de construção: §7" + (generator.getConstructionTime() / 1000) + "s");
        player.sendMessage("§fQuantidade por construção: §7" + generator.getQuantityPerConstruction());
        player.sendMessage("§fModo economia: " + (generator.isEnergySaving() ? "§aAtivado" : "§cDesativado"));

        if (generator.hasActiveBooster()) {
            player.sendMessage("§fBooster: §a" + generator.getActiveBooster().getMultiplier() + "x §7(" +
                    generator.getActiveBooster().getTimeRemainingFormatted() + ")");
        } else {
            player.sendMessage("§fBooster: §cNenhum");
        }
    }
}
