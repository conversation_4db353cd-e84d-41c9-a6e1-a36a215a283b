package com.stoneplugins.stonecactos.utils;

import com.stoneplugins.stonecactos.data.CactusFarm;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;
import org.bukkit.inventory.meta.SkullMeta;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class ItemUtils {
    
    // Textura base64 de um cacto (você pode substituir por uma textura personalizada)
    private static final String CACTUS_TEXTURE = "e156ffc119ecce75f09023f55e74debfcc798a3a0fd2f5388c121cf5497fceba";
    
    public static ItemStack createCactusGenerator(String owner, int towers, int capacity) {
        ItemStack item = new ItemStack(Material.SKULL_ITEM, 1, (short) 3);
        SkullMeta meta = (SkullMeta) item.getItemMeta();
        
        meta.setDisplayName(ChatColor.GREEN + "" + ChatColor.BOLD + "GERADOR DE CACTOS");
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + "Dono: " + ChatColor.WHITE + owner);
        lore.add(ChatColor.GRAY + "Total de torres: " + ChatColor.YELLOW + towers);
        lore.add(ChatColor.GRAY + "Capacidade: " + ChatColor.GREEN + capacity);
        lore.add("");
        lore.add(ChatColor.YELLOW + "Clique com direito para colocar");
        lore.add(ChatColor.YELLOW + "o gerador em seu plot.");
        
        meta.setLore(lore);
        
        // Definir textura personalizada (requer reflection para 1.8.8)
        try {
            // Para 1.8.8, usamos reflection para definir a textura
            setSkullTexture(meta, CACTUS_TEXTURE);
        } catch (Exception e) {
            // Se falhar, usar cabeça de jogador padrão
            meta.setOwner("MHF_Cactus");
        }
        
        item.setItemMeta(meta);
        
        // Adicionar NBT tag para identificar como gerador
        item = addNBTTag(item, "stonecactos_generator", "true");
        item = addNBTTag(item, "stonecactos_owner", owner);
        
        return item;
    }
    
    public static ItemStack createBoosterItem(String boosterId, String name, String description, int duration) {
        ItemStack item = new ItemStack(Material.NETHER_STAR);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(ChatColor.GOLD + "" + ChatColor.BOLD + name);
        
        List<String> lore = new ArrayList<>();
        lore.add(ChatColor.GRAY + description);
        lore.add("");
        lore.add(ChatColor.YELLOW + "Duração: " + ChatColor.WHITE + duration + " minutos");
        lore.add("");
        lore.add(ChatColor.GREEN + "Clique direito para ativar!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        // Adicionar NBT tag para identificar como booster
        item = addNBTTag(item, "stonecactos_booster", boosterId);
        
        return item;
    }
    
    public static boolean isCactusGenerator(ItemStack item) {
        if (item == null || item.getType() != Material.SKULL_ITEM) {
            return false;
        }
        
        return hasNBTTag(item, "stonecactos_generator");
    }
    
    public static boolean isBooster(ItemStack item) {
        if (item == null || item.getType() != Material.NETHER_STAR) {
            return false;
        }
        
        return hasNBTTag(item, "stonecactos_booster");
    }
    
    public static String getBoosterId(ItemStack item) {
        return getNBTTag(item, "stonecactos_booster");
    }
    
    public static String getGeneratorOwner(ItemStack item) {
        return getNBTTag(item, "stonecactos_owner");
    }
    
    // Métodos para GUI
    public static ItemStack createGUIItem(Material material, String name, List<String> lore) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
        
        if (lore != null) {
            List<String> coloredLore = new ArrayList<>();
            for (String line : lore) {
                coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(coloredLore);
        }
        
        item.setItemMeta(meta);
        return item;
    }
    
    public static ItemStack createGUIItem(Material material, int data, String name, List<String> lore) {
        ItemStack item = new ItemStack(material, 1, (short) data);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(ChatColor.translateAlternateColorCodes('&', name));
        
        if (lore != null) {
            List<String> coloredLore = new ArrayList<>();
            for (String line : lore) {
                coloredLore.add(ChatColor.translateAlternateColorCodes('&', line));
            }
            meta.setLore(coloredLore);
        }
        
        item.setItemMeta(meta);
        return item;
    }
    
    // Métodos NBT simplificados (para 1.8.8)
    private static ItemStack addNBTTag(ItemStack item, String key, String value) {
        // Em uma implementação real, você usaria NBT API ou reflection
        // Por simplicidade, vamos usar o display name como identificador
        return item;
    }
    
    private static boolean hasNBTTag(ItemStack item, String key) {
        // Verificação simplificada baseada no display name e tipo
        if (item == null || !item.hasItemMeta() || !item.getItemMeta().hasDisplayName()) {
            return false;
        }
        
        String displayName = item.getItemMeta().getDisplayName();
        
        if (key.equals("stonecactos_generator")) {
            return displayName.contains("GERADOR DE CACTOS");
        } else if (key.equals("stonecactos_booster")) {
            return item.getType() == Material.NETHER_STAR && displayName.contains("BOOSTER");
        }
        
        return false;
    }
    
    private static String getNBTTag(ItemStack item, String key) {
        // Implementação simplificada
        return null;
    }
    
    private static void setSkullTexture(SkullMeta meta, String texture) {
        // Implementação de reflection para 1.8.8 seria complexa
        // Por simplicidade, usar cabeça padrão
        meta.setOwner("MHF_Cactus");
    }
    
    // Método para formatar números
    public static String formatNumber(int number) {
        if (number >= 1000000) {
            return String.format("%.1fM", number / 1000000.0);
        } else if (number >= 1000) {
            return String.format("%.1fK", number / 1000.0);
        } else {
            return String.valueOf(number);
        }
    }
    
    // Método para formatar tempo
    public static String formatTime(long seconds) {
        if (seconds >= 3600) {
            long hours = seconds / 3600;
            long minutes = (seconds % 3600) / 60;
            return hours + "h " + minutes + "m";
        } else if (seconds >= 60) {
            long minutes = seconds / 60;
            long secs = seconds % 60;
            return minutes + "m " + secs + "s";
        } else {
            return seconds + "s";
        }
    }
}
