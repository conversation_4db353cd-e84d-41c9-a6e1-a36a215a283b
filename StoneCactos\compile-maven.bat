@echo off
echo ========================================
echo    Compilando StoneCactos com Maven
echo ========================================

REM Mudar para o diretório do projeto
cd /d "%~dp0"

REM Verificar se Maven está disponível
set "MAVEN_CMD="

REM Tentar encontrar Maven no PATH
where mvn >nul 2>&1
if %errorlevel% equ 0 (
    set "MAVEN_CMD=mvn"
    echo Maven encontrado no PATH
    goto :compile
)

REM Tentar Maven wrapper se existir
if exist "mvnw.cmd" (
    set "MAVEN_CMD=mvnw.cmd"
    echo Usando Maven Wrapper
    goto :compile
)

REM Tentar locais comuns do Maven
if exist "C:\Program Files\Apache\maven\bin\mvn.cmd" (
    set "MAVEN_CMD=C:\Program Files\Apache\maven\bin\mvn.cmd"
    echo Maven encontrado em Program Files
    goto :compile
)

if exist "C:\apache-maven-3.9.6\bin\mvn.cmd" (
    set "MAVEN_CMD=C:\apache-maven-3.9.6\bin\mvn.cmd"
    echo Maven encontrado em C:\apache-maven-3.9.6
    goto :compile
)

if exist "C:\tools\apache-maven\bin\mvn.cmd" (
    set "MAVEN_CMD=C:\tools\apache-maven\bin\mvn.cmd"
    echo Maven encontrado em C:\tools
    goto :compile
)

REM Se não encontrou Maven, usar compilação manual
echo AVISO: Maven não encontrado! Usando compilação manual...
echo.
call compile_fixed.bat
goto :end

:compile
echo Executando: %MAVEN_CMD% clean package
echo.

REM Executar Maven
"%MAVEN_CMD%" clean package

if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo    COMPILACAO MAVEN CONCLUIDA!
    echo ========================================
    
    if exist "target\stonecactos-1.0.jar" (
        echo Arquivo gerado: target\stonecactos-1.0.jar
        for %%I in (target\stonecactos-1.0.jar) do echo Tamanho: %%~zI bytes
        
        if exist "..\servidor\plugins\stonecactos-1.0.jar" (
            echo Plugin copiado para o servidor automaticamente!
            echo Localização: ..\servidor\plugins\stonecactos-1.0.jar
        ) else (
            echo AVISO: Plugin não foi copiado automaticamente.
            echo Copiando manualmente...
            copy "target\stonecactos-1.0.jar" "..\servidor\plugins\stonecactos-1.0.jar" >nul
            if exist "..\servidor\plugins\stonecactos-1.0.jar" (
                echo Plugin copiado manualmente com sucesso!
            ) else (
                echo ERRO: Falha ao copiar plugin para o servidor.
            )
        )
    ) else (
        echo ERRO: Arquivo JAR não foi gerado!
    )
    echo ========================================
) else (
    echo.
    echo ERRO: Falha na compilação Maven!
    echo Tentando compilação manual como fallback...
    echo.
    call compile_fixed.bat
)

:end
pause
