package com.stoneplugins.stonetags.gui;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.data.Suffix;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.List;

public class SuffixMenu implements Listener {
    
    private final StoneTags plugin;
    private final Player player;
    private final int page;
    private final int itemsPerPage = 45; // 5 linhas de 9 slots
    
    public SuffixMenu(StoneTags plugin, Player player, int page) {
        this.plugin = plugin;
        this.player = player;
        this.page = page;
        
        // Registrar listener
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }
    
    public void open() {
        List<Suffix> availableSuffixes = plugin.getSuffixManager().getAvailableSuffixes(player);
        
        // Calcular paginação
        int totalPages = (int) Math.ceil((double) availableSuffixes.size() / itemsPerPage);
        if (totalPages == 0) totalPages = 1;
        
        String title = plugin.getMessageUtils().colorize("&8Sufixos - Página " + page + "/" + totalPages);
        Inventory inventory = Bukkit.createInventory(null, 54, title);
        
        // Adicionar sufixos
        int startIndex = (page - 1) * itemsPerPage;
        int endIndex = Math.min(startIndex + itemsPerPage, availableSuffixes.size());
        
        for (int i = startIndex; i < endIndex; i++) {
            Suffix suffix = availableSuffixes.get(i);
            ItemStack item = createSuffixItem(suffix);
            inventory.setItem(i - startIndex, item);
        }
        
        // Adicionar botões de navegação
        if (page > 1) {
            ItemStack previousPage = createNavigationItem(Material.ARROW, "&aPágina Anterior", "&7Clique para ir para a página " + (page - 1));
            inventory.setItem(45, previousPage);
        }
        
        if (page < totalPages) {
            ItemStack nextPage = createNavigationItem(Material.ARROW, "&aPróxima Página", "&7Clique para ir para a página " + (page + 1));
            inventory.setItem(53, nextPage);
        }
        
        // Botão de remover suffix
        ItemStack removeSuffix = createNavigationItem(Material.BARRIER, "&cRemover Suffix", "&7Clique para remover seu suffix atual");
        inventory.setItem(49, removeSuffix);
        
        // Botão de fechar
        ItemStack close = createNavigationItem(Material.REDSTONE, "&cFechar", "&7Clique para fechar o menu");
        inventory.setItem(50, close);
        
        player.openInventory(inventory);
    }
    
    private ItemStack createSuffixItem(Suffix suffix) {
        ItemStack item = new ItemStack(Material.NAME_TAG);
        ItemMeta meta = item.getItemMeta();
        
        // Nome do item
        String suffixDisplay = plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
        meta.setDisplayName(plugin.getMessageUtils().colorize("&f" + suffix.getName() + " " + suffixDisplay));
        
        // Lore
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add(plugin.getMessageUtils().colorize("&7Suffix: " + suffixDisplay));
        lore.add(plugin.getMessageUtils().colorize("&7Prioridade: &f" + suffix.getPriority()));
        
        if (suffix.requiresPermission()) {
            boolean hasPermission = player.hasPermission(suffix.getPermission());
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: " + (hasPermission ? "&aVocê tem" : "&cVocê não tem")));
        } else {
            lore.add(plugin.getMessageUtils().colorize("&7Permissão: &aNenhuma necessária"));
        }
        
        if (suffix.isAnimated()) {
            lore.add(plugin.getMessageUtils().colorize("&7Tipo: &dAnimado"));
        }
        
        if (suffix.isInvisible()) {
            lore.add(plugin.getMessageUtils().colorize("&7Efeito: &8Invisível"));
        }
        
        // Verificar se está usando este suffix
        Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
        if (currentSuffix != null && currentSuffix.getId().equals(suffix.getId())) {
            lore.add("");
            lore.add(plugin.getMessageUtils().colorize("&a✓ Suffix atual"));
        } else {
            lore.add("");
            lore.add(plugin.getMessageUtils().colorize("&eClique para usar este suffix"));
        }
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }
    
    private ItemStack createNavigationItem(Material material, String name, String description) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName(plugin.getMessageUtils().colorize(name));
        
        List<String> lore = new ArrayList<>();
        lore.add(plugin.getMessageUtils().colorize(description));
        meta.setLore(lore);
        
        item.setItemMeta(meta);
        return item;
    }
    
    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player clicker = (Player) event.getWhoClicked();
        
        if (!clicker.equals(player)) return;
        if (!event.getView().getTitle().contains("Sufixos - Página")) return;
        
        event.setCancelled(true);
        
        ItemStack clickedItem = event.getCurrentItem();
        if (clickedItem == null || clickedItem.getType() == Material.AIR) return;
        
        int slot = event.getSlot();
        
        // Navegação
        if (slot == 45 && page > 1) {
            // Página anterior
            player.closeInventory();
            new SuffixMenu(plugin, player, page - 1).open();
            return;
        }
        
        if (slot == 53) {
            // Próxima página
            List<Suffix> availableSuffixes = plugin.getSuffixManager().getAvailableSuffixes(player);
            int totalPages = (int) Math.ceil((double) availableSuffixes.size() / itemsPerPage);
            if (page < totalPages) {
                player.closeInventory();
                new SuffixMenu(plugin, player, page + 1).open();
            }
            return;
        }
        
        if (slot == 49) {
            // Remover suffix
            if (plugin.getPlayerDataManager().hasSelectedSuffix(player)) {
                plugin.getPlayerDataManager().removeSelectedSuffix(player);
                player.sendMessage(plugin.getMessageUtils().colorize("&aSeu suffix foi removido!"));
            } else {
                player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não está usando nenhum suffix!"));
            }
            player.closeInventory();
            return;
        }
        
        if (slot == 50) {
            // Fechar menu
            player.closeInventory();
            return;
        }
        
        // Selecionar suffix
        if (slot < itemsPerPage) {
            List<Suffix> availableSuffixes = plugin.getSuffixManager().getAvailableSuffixes(player);
            int startIndex = (page - 1) * itemsPerPage;
            int suffixIndex = startIndex + slot;
            
            if (suffixIndex < availableSuffixes.size()) {
                Suffix suffix = availableSuffixes.get(suffixIndex);
                
                // Verificar se pode usar
                if (!plugin.getSuffixManager().canUseSuffix(player, suffix)) {
                    player.sendMessage(plugin.getMessageUtils().colorize("&cVocê não tem permissão para usar este suffix!"));
                    return;
                }
                
                // Verificar se já está usando
                Suffix currentSuffix = plugin.getPlayerDataManager().getSelectedSuffix(player);
                if (currentSuffix != null && currentSuffix.getId().equals(suffix.getId())) {
                    player.sendMessage(plugin.getMessageUtils().colorize("&cVocê já está usando este suffix!"));
                    return;
                }
                
                // Definir suffix
                plugin.getPlayerDataManager().setSelectedSuffix(player, suffix.getId());
                
                String suffixDisplay = plugin.getMessageUtils().colorize(suffix.getDisplaySuffix());
                player.sendMessage(plugin.getMessageUtils().colorize("&aSuffix alterado para: " + suffixDisplay));
                
                player.closeInventory();
            }
        }
    }
}
