package com.atlasplugins.pescaria;

import com.atlasplugins.pescaria.commands.PescaCommand;
import com.atlasplugins.pescaria.config.ConfigManager;
import com.atlasplugins.pescaria.integrations.BetterEconomyIntegration;
import com.atlasplugins.pescaria.integrations.EconomyIntegration;
import com.atlasplugins.pescaria.integrations.PescariaPlaceholders;
import com.atlasplugins.pescaria.integrations.SCashIntegration;
import com.atlasplugins.pescaria.integrations.SimpleScoreIntegration;
import com.atlasplugins.pescaria.listeners.FishingListener;
import com.atlasplugins.pescaria.listeners.GUIListener;
import com.atlasplugins.pescaria.listeners.WorldChangeListener;
import com.atlasplugins.pescaria.managers.MissionManager;
import com.atlasplugins.pescaria.managers.PlayerManager;
import com.atlasplugins.pescaria.scoreboard.ScoreboardManager;
import net.milkbowl.vault.economy.Economy;
import org.bukkit.Bukkit;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.java.JavaPlugin;

public final class Pescaria extends JavaPlugin {

    private static Pescaria instance;
    private ConfigManager configManager;
    private PlayerManager playerManager;
    private MissionManager missionManager;
    private ScoreboardManager scoreboardManager;
    private Economy economy;
    private EconomyIntegration economyIntegration;
    private SimpleScoreIntegration simpleScoreIntegration;
    private SCashIntegration sCashIntegration;
    private BetterEconomyIntegration betterEconomyIntegration;
    private PescariaPlaceholders placeholders;

    @Override
    public void onEnable() {
        // Plugin startup logic
        instance = this;

        // Inicializar gerenciadores
        configManager = new ConfigManager(this);
        configManager.setupFiles();

        // Verificar dependências
        if (!setupEconomy()) {
            getLogger().severe("Vault não encontrado! Desabilitando plugin...");
            getServer().getPluginManager().disablePlugin(this);
            return;
        }

        // Inicializar gerenciadores que dependem de configurações
        missionManager = new MissionManager(this);
        playerManager = new PlayerManager(this);
        scoreboardManager = new ScoreboardManager(this);

        // Registrar comandos
        getCommand("pesca").setExecutor(new PescaCommand(this));

        // Registrar eventos
        getServer().getPluginManager().registerEvents(new FishingListener(this), this);
        getServer().getPluginManager().registerEvents(new GUIListener(this), this);
        getServer().getPluginManager().registerEvents(new WorldChangeListener(this), this);

        // Configurar integrações
        setupIntegrations();

        // Task de scoreboard desabilitado para evitar spam
        // startScoreboardTask();

        getLogger().info("Plugin de Pescaria ativado com sucesso!");
    }

    @Override
    public void onDisable() {
        // Plugin shutdown logic
        playerManager.saveAllPlayers();
        getLogger().info("Plugin de Pescaria desativado com sucesso!");
    }

    private boolean setupEconomy() {
        if (getServer().getPluginManager().getPlugin("Vault") == null) {
            return false;
        }
        RegisteredServiceProvider<Economy> rsp = getServer().getServicesManager().getRegistration(Economy.class);
        if (rsp == null) {
            return false;
        }
        economy = rsp.getProvider();
        return economy != null;
    }

    private void setupPlaceholderAPI() {
        if (getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            placeholders = new PescariaPlaceholders(this);
            getLogger().info("PlaceholderAPI integração ativada!");
            getLogger().info("Placeholders disponíveis: %pescaria_peixes%, %pescaria_vara_nivel%, etc.");
        }
    }

    private void setupIntegrations() {
        // Configurar integração com economia
        economyIntegration = new EconomyIntegration(this);

        // Configurar integração com SimpleScore
        simpleScoreIntegration = new SimpleScoreIntegration(this);

        // Configurar integração com sCash
        sCashIntegration = new SCashIntegration(this);

        // Configurar integração com BetterEconomy
        betterEconomyIntegration = new BetterEconomyIntegration(this);

        // Registrar PlaceholderAPI
        setupPlaceholderAPI();

        getLogger().info("Integrações configuradas:");
        getLogger().info("- Economia: " + economyIntegration.getEconomyName());
        getLogger().info("- SimpleScore: " + (simpleScoreIntegration.hasSimpleScore() ? "Ativado" : "Não encontrado"));
        getLogger().info("- sCash: " + (sCashIntegration.isEnabled() ? "Ativado" : "Não encontrado"));
        getLogger().info("- BetterEconomy: " + (betterEconomyIntegration.isEnabled() ? "Ativado" : "Não encontrado"));
        getLogger().info("- PlaceholderAPI: " + (placeholders != null ? "Ativado" : "Não encontrado"));
    }

    public static Pescaria getInstance() {
        return instance;
    }

    public ConfigManager getConfigManager() {
        return configManager;
    }

    public PlayerManager getPlayerManager() {
        return playerManager;
    }

    public MissionManager getMissionManager() {
        return missionManager;
    }

    public ScoreboardManager getScoreboardManager() {
        return scoreboardManager;
    }

    public Economy getEconomy() {
        return economy;
    }

    public EconomyIntegration getEconomyIntegration() {
        return economyIntegration;
    }

    public SimpleScoreIntegration getSimpleScoreIntegration() {
        return simpleScoreIntegration;
    }

    public SCashIntegration getSCashIntegration() {
        return sCashIntegration;
    }

    public BetterEconomyIntegration getBetterEconomyIntegration() {
        return betterEconomyIntegration;
    }

    public PescariaPlaceholders getPlaceholders() {
        return placeholders;
    }

    private void startScoreboardTask() {
        // Task que roda a cada 30 segundos para verificar scoreboards perdidos
        getServer().getScheduler().runTaskTimer(this, () -> {
            for (org.bukkit.entity.Player player : getServer().getOnlinePlayers()) {
                String currentWorld = player.getWorld().getName();
                String pescariaWorld = configManager.getPescariaWorldName();

                if (currentWorld.equalsIgnoreCase(pescariaWorld)) {
                    org.bukkit.scoreboard.Scoreboard currentBoard = player.getScoreboard();
                    if (currentBoard == null
                            || currentBoard == org.bukkit.Bukkit.getScoreboardManager().getMainScoreboard()
                            || currentBoard.getObjective("pescaria") == null) {
                        getLogger().info("VERIFICAÇÃO PERIÓDICA: Scoreboard perdido para " + player.getName()
                                + " - reaplicando");
                        scoreboardManager.updateScoreboard(player);
                    }
                }
            }
        }, 600L, 600L); // 30 segundos inicial, depois a cada 30 segundos
    }
}