package com.stoneplugins.stonecactos.generators;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class CactusGeneratorManager {

    private final StoneCactos plugin;
    private final Map<Location, CactusGenerator> generators;
    private final Map<UUID, Set<CactusGenerator>> playerGenerators;
    private int nextId;

    public CactusGeneratorManager(StoneCactos plugin) {
        this.plugin = plugin;
        this.generators = new ConcurrentHashMap<>();
        this.playerGenerators = new ConcurrentHashMap<>();
        this.nextId = 1;

        loadAllGenerators();
    }

    /**
     * Obtém o gerador de um jogador
     * 
     * @param playerUuid UUID do jogador
     * @return O primeiro gerador encontrado do jogador, ou null se não tiver nenhum
     */
    public CactusGenerator getGenerator(UUID playerUuid) {
        Set<CactusGenerator> generators = playerGenerators.get(playerUuid);
        if (generators == null || generators.isEmpty()) {
            return null;
        }
        return generators.iterator().next();
    }

    /**
     * Carrega todos os geradores do banco de dados
     */
    private void loadAllGenerators() {
        plugin.getLogger().info("Carregando geradores de cactos...");

        if (plugin.getDatabaseManager() != null && plugin.getDatabaseManager().isConnected()) {
            List<CactusGenerator> loadedGenerators = plugin.getDatabaseManager().loadAllGenerators();
            for (CactusGenerator generator : loadedGenerators) {
                generators.put(generator.getLocation(), generator);
                playerGenerators.computeIfAbsent(generator.getOwnerUuid(), k -> new HashSet<>()).add(generator);
                if (generator.getId() >= nextId) {
                    nextId = generator.getId() + 1;
                }
            }
        }

        plugin.getLogger().info("Carregados " + generators.size() + " geradores de cactos!");
    }

    /**
     * Cria um novo gerador de cactos
     */
    public CactusGenerator createGenerator(UUID ownerUuid, Location location) {
        // Verificar se já existe um gerador nesta localização
        if (hasGeneratorAt(location)) {
            return null;
        }

        // Criar novo gerador
        CactusGenerator generator = new CactusGenerator(nextId++, ownerUuid, location.clone());

        // Adicionar aos mapas
        generators.put(location, generator);
        playerGenerators.computeIfAbsent(ownerUuid, k -> new HashSet<>()).add(generator);

        // Salvar no banco de dados
        saveGenerator(generator);

        // Criar holograma
        plugin.getHologramManager().createHologram(generator);

        plugin.getLogger().info("Gerador de cactos criado para " + ownerUuid + " em " + locationToString(location));

        return generator;
    }

    /**
     * Remove um gerador de cactos
     */
    public boolean removeGenerator(Location location) {
        CactusGenerator generator = generators.remove(location);

        if (generator != null) {
            // Remover do mapa de jogadores
            Set<CactusGenerator> playerGens = playerGenerators.get(generator.getOwnerUuid());
            if (playerGens != null) {
                playerGens.remove(generator);
                if (playerGens.isEmpty()) {
                    playerGenerators.remove(generator.getOwnerUuid());
                }
            }

            // Remover do banco de dados
            // plugin.getDatabaseManager().deleteFarm(String.valueOf(generator.getId()));

            // Remover holograma
            plugin.getHologramManager().removeHologram(generator);

            plugin.getLogger().info("Gerador de cactos removido de " + locationToString(location));

            return true;
        }

        return false;
    }

    /**
     * Obtém um gerador pela localização
     */
    public CactusGenerator getGenerator(Location location) {
        return generators.get(location);
    }

    /**
     * Obtém todos os geradores de um jogador
     */
    public Set<CactusGenerator> getPlayerGenerators(UUID playerUuid) {
        return playerGenerators.getOrDefault(playerUuid, new HashSet<>());
    }

    /**
     * Obtém todos os geradores
     */
    public Collection<CactusGenerator> getAllGenerators() {
        return generators.values();
    }

    /**
     * Verifica se existe um gerador na localização
     */
    public boolean hasGeneratorAt(Location location) {
        return generators.containsKey(location);
    }

    /**
     * Verifica se um jogador tem geradores
     */
    public boolean hasGenerators(UUID playerUuid) {
        Set<CactusGenerator> gens = playerGenerators.get(playerUuid);
        return gens != null && !gens.isEmpty();
    }

    /**
     * Conta quantos geradores um jogador possui
     */
    public int getGeneratorCount(UUID playerUuid) {
        Set<CactusGenerator> gens = playerGenerators.get(playerUuid);
        return gens != null ? gens.size() : 0;
    }

    /**
     * Salva um gerador no banco de dados
     */
    public void saveGenerator(CactusGenerator generator) {
        if (plugin.getDatabaseManager() != null && plugin.getDatabaseManager().isConnected()) {
            plugin.getDatabaseManager().saveGenerator(generator);
        }
    }

    /**
     * Salva todos os geradores
     */
    public void saveAllGenerators() {
        plugin.getLogger().info("Salvando " + generators.size() + " geradores de cactos...");

        for (CactusGenerator generator : generators.values()) {
            saveGenerator(generator);
        }

        plugin.getLogger().info("Todos os geradores foram salvos!");
    }

    /**
     * Processa a construção de torres para todos os geradores
     */
    public void processConstruction() {
        for (CactusGenerator generator : generators.values()) {
            if (generator.canConstruct()) {
                generator.addTowers(generator.getQuantityPerConstruction());
                saveGenerator(generator);

                // Atualizar holograma
                plugin.getHologramManager().updateHologram(generator);
            }
        }
    }

    /**
     * Processa a produção de cactos para todos os geradores
     */
    public void processProduction() {
        for (CactusGenerator generator : generators.values()) {
            if (generator.canProduce()) {
                // Verificar se há jogador no plot
                if (isPlayerInPlot(generator)) {
                    int production = calculateProduction(generator);
                    generator.addCactus(production);
                    saveGenerator(generator);

                    // Atualizar estatísticas do jogador
                    // plugin.getDatabaseManager().updatePlayerStats(
                    // generator.getOwnerUuid(), production, 0, 0.0, 0, 0);

                    // Atualizar holograma
                    plugin.getHologramManager().updateHologram(generator);
                }
            }

            // Verificar se o booster expirou
            generator.expireBooster();
        }
    }

    /**
     * Calcula a produção de cactos de um gerador
     */
    private int calculateProduction(CactusGenerator generator) {
        int baseProduction = generator.getTowers();

        // Aplicar modo economia de energia
        if (generator.isEnergySaving() && generator.needsEnergyMode()) {
            baseProduction /= 2;
        }

        // Aplicar booster
        if (generator.hasActiveBooster()) {
            baseProduction = (int) (baseProduction * generator.getActiveBooster().getMultiplier());
        }

        return Math.max(1, baseProduction);
    }

    /**
     * Verifica se há um jogador no plot do gerador
     */
    private boolean isPlayerInPlot(CactusGenerator generator) {
        return plugin.getPlotSquaredIntegration().isPlayerInPlot(generator.getLocation(), generator.getOwnerUuid());
    }

    /**
     * Obtém o gerador mais próximo de uma localização
     */
    public CactusGenerator getNearestGenerator(Location location, double maxDistance) {
        CactusGenerator nearest = null;
        double nearestDistance = maxDistance;

        for (CactusGenerator generator : generators.values()) {
            if (generator.getLocation().getWorld().equals(location.getWorld())) {
                double distance = generator.getLocation().distance(location);
                if (distance < nearestDistance) {
                    nearest = generator;
                    nearestDistance = distance;
                }
            }
        }

        return nearest;
    }

    /**
     * Obtém geradores em uma área
     */
    public List<CactusGenerator> getGeneratorsInArea(Location center, double radius) {
        List<CactusGenerator> result = new ArrayList<>();

        for (CactusGenerator generator : generators.values()) {
            if (generator.getLocation().getWorld().equals(center.getWorld())) {
                double distance = generator.getLocation().distance(center);
                if (distance <= radius) {
                    result.add(generator);
                }
            }
        }

        return result;
    }

    /**
     * Verifica se um jogador pode acessar um gerador
     */
    public boolean canAccess(Player player, CactusGenerator generator) {
        UUID playerUuid = player.getUniqueId();

        // Verificar se é o dono
        if (generator.getOwnerUuid().equals(playerUuid)) {
            return true;
        }

        // Verificar se é amigo
        return generator.isFriend(player);
    }

    /**
     * Obtém estatísticas gerais dos geradores
     */
    public Map<String, Object> getStatistics() {
        Map<String, Object> stats = new HashMap<>();

        int totalGenerators = generators.size();
        int totalTowers = generators.values().stream().mapToInt(CactusGenerator::getTowers).sum();
        int totalStoredCactus = generators.values().stream().mapToInt(CactusGenerator::getStoredCactus).sum();
        double averageBattery = generators.values().stream().mapToDouble(CactusGenerator::getBattery).average()
                .orElse(0.0);

        stats.put("totalGenerators", totalGenerators);
        stats.put("totalTowers", totalTowers);
        stats.put("totalStoredCactus", totalStoredCactus);
        stats.put("averageBattery", averageBattery);
        stats.put("uniquePlayers", playerGenerators.size());

        return stats;
    }

    // Métodos utilitários
    private String locationToString(Location location) {
        return String.format("%s:%.1f,%.1f,%.1f",
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ());
    }
}
