package com.stoneplugins.stonecactos.data;

public class Booster {
    
    private String id;
    private String name;
    private String description;
    private int duration; // em minutos
    private double multiplier;
    private String type; // "PRODUCTION", "CONSTRUCTION", "BATTERY"
    
    public Booster(String id, String name, String description, int duration, double multiplier, String type) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.duration = duration;
        this.multiplier = multiplier;
        this.type = type;
    }
    
    // Getters e Setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public int getDuration() {
        return duration;
    }
    
    public void setDuration(int duration) {
        this.duration = duration;
    }
    
    public double getMultiplier() {
        return multiplier;
    }
    
    public void setMultiplier(double multiplier) {
        this.multiplier = multiplier;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
}
