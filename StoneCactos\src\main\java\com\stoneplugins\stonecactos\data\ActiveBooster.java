package com.stoneplugins.stonecactos.data;

public class ActiveBooster {
    
    private String boosterType;
    private double multiplier;
    private long startTime;
    private long duration; // em milissegundos
    
    public ActiveBooster(String boosterType, double multiplier, long duration) {
        this.boosterType = boosterType;
        this.multiplier = multiplier;
        this.startTime = System.currentTimeMillis();
        this.duration = duration;
    }
    
    public ActiveBooster(String boosterType, double multiplier, long startTime, long duration) {
        this.boosterType = boosterType;
        this.multiplier = multiplier;
        this.startTime = startTime;
        this.duration = duration;
    }
    
    // Getters e Setters
    public String getBoosterType() {
        return boosterType;
    }
    
    public void setBoosterType(String boosterType) {
        this.boosterType = boosterType;
    }
    
    public double getMultiplier() {
        return multiplier;
    }
    
    public void setMultiplier(double multiplier) {
        this.multiplier = multiplier;
    }
    
    public long getStartTime() {
        return startTime;
    }
    
    public void setStartTime(long startTime) {
        this.startTime = startTime;
    }
    
    public long getDuration() {
        return duration;
    }
    
    public void setDuration(long duration) {
        this.duration = duration;
    }
    
    // Métodos utilitários
    public boolean isActive() {
        return System.currentTimeMillis() < (startTime + duration);
    }
    
    public long getTimeRemaining() {
        long remaining = (startTime + duration) - System.currentTimeMillis();
        return Math.max(0, remaining);
    }
    
    public String getTimeRemainingFormatted() {
        long remaining = getTimeRemaining();
        
        if (remaining <= 0) {
            return "Expirado";
        }
        
        long hours = remaining / (1000 * 60 * 60);
        long minutes = (remaining % (1000 * 60 * 60)) / (1000 * 60);
        long seconds = (remaining % (1000 * 60)) / 1000;
        
        if (hours > 0) {
            return String.format("%dh %dm %ds", hours, minutes, seconds);
        } else if (minutes > 0) {
            return String.format("%dm %ds", minutes, seconds);
        } else {
            return String.format("%ds", seconds);
        }
    }
    
    public double getProgressPercentage() {
        long elapsed = System.currentTimeMillis() - startTime;
        return Math.min(100.0, (elapsed * 100.0) / duration);
    }
    
    @Override
    public String toString() {
        return "ActiveBooster{" +
                "boosterType='" + boosterType + '\'' +
                ", multiplier=" + multiplier +
                ", timeRemaining=" + getTimeRemainingFormatted() +
                ", active=" + isActive() +
                '}';
    }
}
