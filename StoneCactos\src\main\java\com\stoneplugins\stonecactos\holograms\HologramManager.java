package com.stoneplugins.stonecactos.holograms;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class HologramManager {

    private final StoneCactos plugin;
    private final Map<CactusGenerator, Object> holograms;
    private boolean holographicDisplaysEnabled = false;
    private boolean decentHologramsEnabled = false;

    public HologramManager(StoneCactos plugin) {
        this.plugin = plugin;
        this.holograms = new HashMap<>();

        checkHologramPlugins();
    }

    /**
     * Verifica quais plugins de holograma estão disponíveis
     */
    private void checkHologramPlugins() {
        if (Bukkit.getPluginManager().getPlugin("HolographicDisplays") != null) {
            holographicDisplaysEnabled = true;
            plugin.getLogger().info("HolographicDisplays detectado!");
        }

        if (Bukkit.getPluginManager().getPlugin("DecentHolograms") != null) {
            decentHologramsEnabled = true;
            plugin.getLogger().info("DecentHolograms detectado!");
        }

        if (!holographicDisplaysEnabled && !decentHologramsEnabled) {
            plugin.getLogger().warning("Nenhum plugin de holograma encontrado! Hologramas não funcionarão.");
        }
    }

    /**
     * Cria um holograma para um gerador
     */
    public void createHologram(CactusGenerator generator) {
        if (!isHologramPluginAvailable()) {
            return;
        }

        Location location = generator.getLocation().clone().add(0.5, plugin.getConfigManager().getCactusHeight(), 0.5);
        List<String> lines = getHologramLines(generator);

        try {
            if (holographicDisplaysEnabled) {
                createHolographicDisplaysHologram(generator, location, lines);
            } else if (decentHologramsEnabled) {
                createDecentHologramsHologram(generator, location, lines);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao criar holograma: " + e.getMessage());
        }
    }

    /**
     * Atualiza um holograma existente
     */
    public void updateHologram(CactusGenerator generator) {
        if (!isHologramPluginAvailable() || !holograms.containsKey(generator)) {
            return;
        }

        List<String> lines = getHologramLines(generator);

        try {
            if (holographicDisplaysEnabled) {
                updateHolographicDisplaysHologram(generator, lines);
            } else if (decentHologramsEnabled) {
                updateDecentHologramsHologram(generator, lines);
            }
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao atualizar holograma: " + e.getMessage());
        }
    }

    /**
     * Remove um holograma
     */
    public void removeHologram(CactusGenerator generator) {
        if (!isHologramPluginAvailable() || !holograms.containsKey(generator)) {
            return;
        }

        try {
            if (holographicDisplaysEnabled) {
                removeHolographicDisplaysHologram(generator);
            } else if (decentHologramsEnabled) {
                removeDecentHologramsHologram(generator);
            }

            holograms.remove(generator);
        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao remover holograma: " + e.getMessage());
        }
    }

    /**
     * Remove todos os hologramas
     */
    public void removeAllHolograms() {
        for (CactusGenerator generator : holograms.keySet()) {
            removeHologram(generator);
        }
        holograms.clear();
    }

    /**
     * Obtém as linhas do holograma para um gerador
     */
    private List<String> getHologramLines(CactusGenerator generator) {
        List<String> lines = plugin.getConfigManager().getCactusHologram();

        // Obter nome do dono
        String ownerName = "Desconhecido";
        Player owner = Bukkit.getPlayer(generator.getOwnerUuid());
        if (owner != null) {
            ownerName = owner.getName();
        }

        // Substituir placeholders
        for (int i = 0; i < lines.size(); i++) {
            String line = lines.get(i)
                    .replace("&", "§")
                    .replace("%owner%", ownerName)
                    .replace("%towers%", String.valueOf(generator.getTowers()))
                    .replace("%capacity%", String.valueOf(generator.getCapacity()))
                    .replace("%stored_cactus%", String.valueOf(generator.getStoredCactus()))
                    .replace("%battery%", generator.getBatteryDisplay())
                    .replace("%queue%", String.valueOf(generator.getConstructionQueue()));

            // Adicionar informações do booster se ativo
            if (generator.hasActiveBooster()) {
                line = line.replace("%booster%", "§a" + generator.getActiveBooster().getMultiplier() + "x §7(" +
                        generator.getActiveBooster().getTimeRemainingFormatted() + ")");
            } else {
                line = line.replace("%booster%", "§cNenhum");
            }

            lines.set(i, line);
        }

        return lines;
    }

    /**
     * Cria holograma usando HolographicDisplays
     */
    private void createHolographicDisplaysHologram(CactusGenerator generator, Location location, List<String> lines) {
        try {
            // Usar reflection para criar holograma do HolographicDisplays
            Class<?> hologramsAPIClass = Class.forName("me.filoghost.holographicdisplays.api.HologramsAPI");
            Class<?> hologramClass = Class.forName("me.filoghost.holographicdisplays.api.hologram.Hologram");

            // Obter instância da API
            Object hologramsAPI = hologramsAPIClass.getMethod("get", org.bukkit.plugin.Plugin.class)
                    .invoke(null, plugin);

            // Criar holograma
            Object hologram = hologramsAPIClass.getMethod("createHologram", Location.class)
                    .invoke(hologramsAPI, location);

            // Adicionar linhas
            Object textLines = hologramClass.getMethod("getLines").invoke(hologram);
            Class<?> hologramLinesClass = textLines.getClass();

            for (String line : lines) {
                hologramLinesClass.getMethod("appendText", String.class)
                        .invoke(textLines, line);
            }

            holograms.put(generator, hologram);
            plugin.getLogger().info("Holograma HolographicDisplays criado para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao criar holograma HolographicDisplays: " + e.getMessage());
            // Fallback para método simples
            holograms.put(generator, "holographic_displays_hologram_" + generator.getId());
        }
    }

    /**
     * Atualiza holograma do HolographicDisplays
     */
    private void updateHolographicDisplaysHologram(CactusGenerator generator, List<String> lines) {
        try {
            Object hologram = holograms.get(generator);
            if (hologram == null || hologram instanceof String) {
                return;
            }

            Class<?> hologramClass = hologram.getClass();

            // Limpar linhas existentes
            Object textLines = hologramClass.getMethod("getLines").invoke(hologram);
            Class<?> hologramLinesClass = textLines.getClass();
            hologramLinesClass.getMethod("clear").invoke(textLines);

            // Adicionar novas linhas
            for (String line : lines) {
                hologramLinesClass.getMethod("appendText", String.class)
                        .invoke(textLines, line);
            }

            plugin.getLogger().info("Holograma HolographicDisplays atualizado para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao atualizar holograma HolographicDisplays: " + e.getMessage());
        }
    }

    /**
     * Remove holograma do HolographicDisplays
     */
    private void removeHolographicDisplaysHologram(CactusGenerator generator) {
        try {
            Object hologram = holograms.get(generator);
            if (hologram == null || hologram instanceof String) {
                return;
            }

            Class<?> hologramClass = hologram.getClass();
            hologramClass.getMethod("delete").invoke(hologram);

            plugin.getLogger().info("Holograma HolographicDisplays removido para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao remover holograma HolographicDisplays: " + e.getMessage());
        }
    }

    /**
     * Cria holograma usando DecentHolograms
     */
    private void createDecentHologramsHologram(CactusGenerator generator, Location location, List<String> lines) {
        try {
            // Usar DHAPI para criar holograma
            Class<?> dhapiClass = Class.forName("eu.decentsoftware.holograms.api.DHAPI");

            String hologramName = "stonecactos_" + generator.getId();

            // Criar holograma
            Object hologram = dhapiClass.getMethod("createHologram", String.class, Location.class, java.util.List.class)
                    .invoke(null, hologramName, location, lines);

            holograms.put(generator, hologram);
            plugin.getLogger().info("Holograma DecentHolograms criado para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao criar holograma DecentHolograms: " + e.getMessage());
            // Fallback para método simples
            holograms.put(generator, "decent_holograms_hologram_" + generator.getId());
        }
    }

    /**
     * Atualiza holograma do DecentHolograms
     */
    private void updateDecentHologramsHologram(CactusGenerator generator, List<String> lines) {
        try {
            Class<?> dhapiClass = Class.forName("eu.decentsoftware.holograms.api.DHAPI");
            String hologramName = "stonecactos_" + generator.getId();

            // Atualizar linhas do holograma
            dhapiClass.getMethod("setHologramLines", String.class, java.util.List.class)
                    .invoke(null, hologramName, lines);

            plugin.getLogger().info("Holograma DecentHolograms atualizado para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao atualizar holograma DecentHolograms: " + e.getMessage());
        }
    }

    /**
     * Remove holograma do DecentHolograms
     */
    private void removeDecentHologramsHologram(CactusGenerator generator) {
        try {
            Class<?> dhapiClass = Class.forName("eu.decentsoftware.holograms.api.DHAPI");
            String hologramName = "stonecactos_" + generator.getId();

            // Remover holograma
            dhapiClass.getMethod("removeHologram", String.class)
                    .invoke(null, hologramName);

            plugin.getLogger().info("Holograma DecentHolograms removido para gerador " + generator.getId());

        } catch (Exception e) {
            plugin.getLogger().warning("Erro ao remover holograma DecentHolograms: " + e.getMessage());
        }
    }

    /**
     * Verifica se algum plugin de holograma está disponível
     */
    public boolean isHologramPluginAvailable() {
        return holographicDisplaysEnabled || decentHologramsEnabled;
    }

    /**
     * Obtém informações sobre os plugins de holograma
     */
    public String getHologramInfo() {
        if (holographicDisplaysEnabled && decentHologramsEnabled) {
            return "§aHolographicDisplays e DecentHolograms disponíveis";
        } else if (holographicDisplaysEnabled) {
            return "§aHolographicDisplays disponível";
        } else if (decentHologramsEnabled) {
            return "§aDecentHolograms disponível";
        } else {
            return "§cNenhum plugin de holograma disponível";
        }
    }

    /**
     * Recarrega todos os hologramas
     */
    public void reloadHolograms() {
        plugin.getLogger().info("Recarregando hologramas...");

        // Remover todos os hologramas existentes
        removeAllHolograms();

        // Recriar hologramas para todos os geradores
        for (CactusGenerator generator : plugin.getGeneratorManager().getAllGenerators()) {
            createHologram(generator);
        }

        plugin.getLogger().info("Hologramas recarregados!");
    }
}
