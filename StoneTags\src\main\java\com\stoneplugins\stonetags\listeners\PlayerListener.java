package com.stoneplugins.stonetags.listeners;

import com.stoneplugins.stonetags.StoneTags;
import com.stoneplugins.stonetags.gui.TagsMenu;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

public class PlayerListener implements Listener {

    private final StoneTags plugin;

    public PlayerListener(StoneTags plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();

        plugin.getLogger().info("=== DEBUG: Player Join - " + player.getName() + " ===");

        // Carregar dados do jogador
        plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId());

        // Executar após 2 segundos para garantir que tudo foi carregado
        plugin.getServer().getScheduler().runTaskLater(plugin, () -> {
            if (player.isOnline()) {
                plugin.getLogger().info("Atualizando display para " + player.getName());

                // Se configurado para atualizar automaticamente, definir tag de maior
                // prioridade
                if (plugin.getConfigManager().autoUpdateOnPermissionChange()) {
                    if (!plugin.getPlayerDataManager().hasSelectedTag(player)) {
                        plugin.getLogger().info("Definindo tag de maior prioridade para " + player.getName());
                        plugin.getPlayerDataManager().setHighestPriorityTag(player);
                    }
                }

                // Atualizar display do jogador
                plugin.getPlayerDataManager().updatePlayerDisplay(player);
                plugin.getLogger().info("Display atualizado para " + player.getName());
            }
        }, 40L); // 2 segundos
    }

    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();

        // Salvar e descarregar dados do jogador
        plugin.getPlayerDataManager().unloadPlayerData(player.getUniqueId());
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) {
            return;
        }

        Player player = (Player) event.getWhoClicked();
        String title = event.getInventory().getTitle();
        String menuTitle = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle());

        // Verificar se é o menu de tags
        if (!title.equals(menuTitle)) {
            return;
        }

        event.setCancelled(true);

        int slot = event.getSlot();
        if (slot < 0)
            return;

        // Criar instância do menu para lidar com o clique
        TagsMenu menu = new TagsMenu(plugin, player, getCurrentPage(player));
        menu.handleClick(slot);
    }

    /**
     * Obtém a página atual do menu (implementação simples)
     * Em uma implementação mais avançada, isso seria armazenado por jogador
     */
    private int getCurrentPage(Player player) {
        // Por simplicidade, sempre retornar página 1
        // Em uma implementação real, você armazenaria isso em um Map<UUID, Integer>
        return 1;
    }
}
