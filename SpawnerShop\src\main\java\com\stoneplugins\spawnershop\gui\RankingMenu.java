package com.stoneplugins.spawnershop.gui;

import com.stoneplugins.spawnershop.SpawnerShop;
import com.stoneplugins.spawnershop.data.PlayerData;
import com.stoneplugins.spawnershop.utils.ItemUtils;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class RankingMenu {

    private final SpawnerShop plugin;
    private final Player player;
    private boolean orderByLimit = false; // false = por compras, true = por limite
    private final List<PlayerData> topPlayers;

    public RankingMenu(SpawnerShop plugin, Player player) {
        this(plugin, player, false);
    }

    public RankingMenu(SpawnerShop plugin, Player player, boolean orderByLimit) {
        this.plugin = plugin;
        this.player = player;
        this.orderByLimit = orderByLimit;
        this.topPlayers = plugin.getDatabaseManager().getTopPlayers(10, orderByLimit);
    }

    public void open() {
        String title = plugin.getConfigManager().getMenuName("ranking");
        int size = plugin.getConfigManager().getMenuSize("ranking");

        Inventory inventory = Bukkit.createInventory(null, size, plugin.getMessageUtils().colorize(title));

        // Preencher com vidro cinza
        ItemStack glass = ItemUtils.createGlassPane(7, " ");
        for (int i = 0; i < size; i++) {
            inventory.setItem(i, glass);
        }

        // Adicionar jogadores do ranking
        addRankingPlayers(inventory);

        // Adicionar botão de ordenação
        addOrderButton(inventory);

        // Adicionar botão de voltar
        addBackButton(inventory);

        player.openInventory(inventory);
    }

    private void addRankingPlayers(Inventory inventory) {
        String slotsConfig = plugin.getConfigManager().getRankingSlots();
        String[] slots = slotsConfig.split(",");

        for (int i = 0; i < Math.min(topPlayers.size(), slots.length); i++) {
            PlayerData playerData = topPlayers.get(i);
            int slot = Integer.parseInt(slots[i].trim());

            ItemStack item = createRankingItem(playerData, i + 1);
            inventory.setItem(slot, item);
        }
    }

    private ItemStack createRankingItem(PlayerData playerData, int position) {
        String name = plugin.getConfigManager().getRankingPlayerName();
        List<String> lore = new ArrayList<>(plugin.getConfigManager().getRankingPlayerLore());

        // Substituir placeholders
        String groups = getPlayerGroups(playerData.getName());

        name = name.replace("{pos}", String.valueOf(position));
        name = name.replace("{jogador}", playerData.getName());
        name = name.replace("{grupo}", groups);

        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);
            line = line.replace("{pos}", String.valueOf(position));
            line = line.replace("{jogador}", playerData.getName());
            line = line.replace("{compras}", String.valueOf(playerData.getPurchases()));
            line = line.replace("{limite}", String.valueOf(playerData.getPurchaseLimit()));
            lore.set(i, line);
        }

        // Criar cabeça do jogador
        ItemStack item = ItemUtils.createSkull(playerData.getName(), name, lore);

        // Adicionar glow para os 3 primeiros
        if (position <= 3) {
            item = ItemUtils.addGlow(item);
        }

        return item;
    }

    private String getPlayerGroups(String playerName) {
        Player onlinePlayer = Bukkit.getPlayer(playerName);
        if (onlinePlayer != null) {
            return plugin.getPlayerDataManager().getPlayerDiscountGroups(onlinePlayer);
        }
        return "Offline";
    }

    private void addOrderButton(Inventory inventory) {
        int slot = 4; // Slot fixo para o botão de ordenação
        String currentOrder = orderByLimit ? "Limite de Compra" : "Geradores Comprados";
        String nextOrder = orderByLimit ? "Geradores Comprados" : "Limite de Compra";

        ItemStack orderItem = ItemUtils.createItem(Material.REDSTONE_COMPARATOR, 1, (short) 0,
                "&e&l⚙ ORDENAÇÃO",
                Arrays.asList(
                        "",
                        "&7Ordenação atual: &a" + currentOrder,
                        "&7Próxima ordenação: &b" + nextOrder,
                        "",
                        "&e&l⚡ Clique para alternar!",
                        ""));
        inventory.setItem(slot, orderItem);
    }

    private void addBackButton(Inventory inventory) {
        int backSlot = plugin.getConfigManager().getRankingBackArrow();

        ItemStack backButton = ItemUtils.createItem(Material.ARROW, 1, (short) 0,
                "&c&l← VOLTAR", Arrays.asList("", "&7Clique para voltar ao", "&7menu principal da loja.", ""));

        inventory.setItem(backSlot, backButton);
    }

    public void handleClick(int slot) {
        if (slot == plugin.getConfigManager().getRankingBackArrow()) {
            player.closeInventory();
            new MainMenu(plugin, player).open();
        } else if (slot == 4) { // Botão de ordenação
            player.closeInventory();
            new RankingMenu(plugin, player, !orderByLimit).open();
        }
    }
}
