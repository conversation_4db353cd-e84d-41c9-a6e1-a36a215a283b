package com.stoneplugins.stonecactos.managers;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.entity.Player;

public class UpgradeManager {

    private final StoneCactos plugin;

    public UpgradeManager(StoneCactos plugin) {
        this.plugin = plugin;
    }

    /**
     * Processa o upgrade de capacidade de torres
     */
    public boolean upgradeCapacity(Player player, CactusGenerator generator) {
        int currentLevel = getCurrentCapacityLevel(generator.getCapacity());
        int nextLevel = currentLevel + 1;

        // Verificar se já atingiu o nível máximo
        if (nextLevel > 3) {
            player.sendMessage(plugin.getConfigManager().getMessage("reachedTheMaximumLevelOfEvolution"));
            return false;
        }

        // Verificar se o jogador pode pagar
        // if (!plugin.getEconomyManager().buyUpgrade(player, "capacityTowers",
        // nextLevel)) {
        // return false;
        // }

        // Aplicar upgrade
        int newCapacity = plugin.getConfigManager().getUpgradeCapacity(nextLevel);
        generator.setCapacity(newCapacity);

        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);

        return true;
    }

    /**
     * Processa o upgrade de quantidade por construção
     */
    public boolean upgradeQuantityPerConstruction(Player player, CactusGenerator generator) {
        int currentLevel = getCurrentQuantityLevel(generator.getQuantityPerConstruction());
        int nextLevel = currentLevel + 1;

        // Verificar se já atingiu o nível máximo
        if (nextLevel > 3) {
            player.sendMessage(plugin.getConfigManager().getMessage("reachedTheMaximumLevelOfEvolution"));
            return false;
        }

        // Verificar se o jogador pode pagar
        // if (!plugin.getEconomyManager().buyUpgrade(player, "quantityPerConstruction",
        // nextLevel)) {
        // return false;
        // }

        // Aplicar upgrade
        int newQuantity = plugin.getConfigManager().getUpgradeQuantity(nextLevel);
        generator.setQuantityPerConstruction(newQuantity);

        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);

        return true;
    }

    /**
     * Processa o upgrade de tempo de construção
     */
    public boolean upgradeConstructionTime(Player player, CactusGenerator generator) {
        int currentLevel = getCurrentTimeLevel(generator.getConstructionTime());
        int nextLevel = currentLevel + 1;

        // Verificar se já atingiu o nível máximo
        if (nextLevel > 3) {
            player.sendMessage(plugin.getConfigManager().getMessage("reachedTheMaximumLevelOfEvolution"));
            return false;
        }

        // Verificar se o jogador pode pagar
        // if (!plugin.getEconomyManager().buyUpgrade(player, "constructionTime",
        // nextLevel)) {
        // return false;
        // }

        // Aplicar upgrade
        long newTime = plugin.getConfigManager().getUpgradeTime(nextLevel);
        generator.setConstructionTime(newTime);

        // Salvar no banco de dados
        plugin.getGeneratorManager().saveGenerator(generator);

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);

        return true;
    }

    /**
     * Obtém o nível atual de capacidade baseado no valor
     */
    private int getCurrentCapacityLevel(int capacity) {
        if (capacity >= 400)
            return 3;
        if (capacity >= 300)
            return 2;
        if (capacity >= 200)
            return 1;
        return 0;
    }

    /**
     * Obtém o nível atual de quantidade baseado no valor
     */
    private int getCurrentQuantityLevel(int quantity) {
        if (quantity >= 15)
            return 3;
        if (quantity >= 10)
            return 2;
        if (quantity >= 5)
            return 1;
        return 0;
    }

    /**
     * Obtém o nível atual de tempo baseado no valor
     */
    private int getCurrentTimeLevel(long time) {
        if (time <= 10000)
            return 3;
        if (time <= 20000)
            return 2;
        if (time <= 30000)
            return 1;
        return 0;
    }

    /**
     * Obtém informações de upgrade de capacidade
     */
    public String getCapacityUpgradeInfo(CactusGenerator generator) {
        int currentLevel = getCurrentCapacityLevel(generator.getCapacity());
        int nextLevel = currentLevel + 1;

        if (nextLevel > 3) {
            return "§cNível máximo atingido";
        }

        double price = plugin.getConfigManager().getUpgradePrice("capacityTowers", nextLevel);
        int newCapacity = plugin.getConfigManager().getUpgradeCapacity(nextLevel);

        return String.format("§eNível %d → %d\n§fCapacidade: %d → %d\n§fPreço: %s",
                currentLevel, nextLevel, generator.getCapacity(), newCapacity,
                String.format("$%.2f", price)); // plugin.getEconomyManager().formatMoney(price)
    }

    /**
     * Obtém informações de upgrade de quantidade
     */
    public String getQuantityUpgradeInfo(CactusGenerator generator) {
        int currentLevel = getCurrentQuantityLevel(generator.getQuantityPerConstruction());
        int nextLevel = currentLevel + 1;

        if (nextLevel > 3) {
            return "§cNível máximo atingido";
        }

        double price = plugin.getConfigManager().getUpgradePrice("quantityPerConstruction", nextLevel);
        int newQuantity = plugin.getConfigManager().getUpgradeQuantity(nextLevel);

        return String.format("§eNível %d → %d\n§fQuantidade: %d → %d\n§fPreço: %s",
                currentLevel, nextLevel, generator.getQuantityPerConstruction(), newQuantity,
                String.format("$%.2f", price)); // plugin.getEconomyManager().formatMoney(price)
    }

    /**
     * Obtém informações de upgrade de tempo
     */
    public String getTimeUpgradeInfo(CactusGenerator generator) {
        int currentLevel = getCurrentTimeLevel(generator.getConstructionTime());
        int nextLevel = currentLevel + 1;

        if (nextLevel > 3) {
            return "§cNível máximo atingido";
        }

        double price = plugin.getConfigManager().getUpgradePrice("constructionTime", nextLevel);
        long newTime = plugin.getConfigManager().getUpgradeTime(nextLevel);

        return String.format("§eNível %d → %d\n§fTempo: %.1fs → %.1fs\n§fPreço: %s",
                currentLevel, nextLevel, generator.getConstructionTime() / 1000.0, newTime / 1000.0,
                String.format("$%.2f", price)); // plugin.getEconomyManager().formatMoney(price)
    }
}
