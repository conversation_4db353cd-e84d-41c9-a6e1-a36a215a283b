package com.stoneplugins.stonecactos.menus;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class DepositMenu extends BaseMenu {

    private final CactusGenerator generator;
    private int currentPage = 0;
    private final int itemsPerPage = 21; // Slots configurados no menu

    public DepositMenu(StoneCactos plugin, Player player, CactusGenerator generator) {
        super(plugin, player);
        this.generator = generator;
    }

    @Override
    protected void createInventory() {
        String title = plugin.getMessageUtils().colorize(plugin.getConfigManager().getMenuTitle("deposit"));
        int size = plugin.getConfigManager().getMenuSize("deposit");

        inventory = Bukkit.createInventory(null, size, title);
    }

    @Override
    protected void setupItems() {
        // Limpar inventário
        inventory.clear();

        // Preencher com vidro
        String glassSlots = plugin.getConfigManager().getMenus().getString("Menus.deposit.glassPaneSlots");
        if (glassSlots != null) {
            fillGlassSlots(glassSlots);
        }

        // Configurar slots de depósito
        setupDepositSlots();

        // Configurar navegação
        setupNavigation();

        // Botão de voltar
        setupBackButton();

        // Botão de depositar
        setupDepositButton();
    }

    private void setupDepositSlots() {
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.deposit.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");

        // Permitir que o jogador coloque itens nos slots
        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                // Os slots ficam vazios para o jogador colocar itens
                // O sistema de depósito será implementado no InventoryListener
            } catch (NumberFormatException e) {
                // Ignorar slots inválidos
            }
        }
    }

    private void setupDepositButton() {
        int slot = plugin.getConfigManager().getMenus().getInt("Menus.deposit.depositItem.slot", 40);
        String name = plugin.getConfigManager().getMenus().getString("Menus.deposit.depositItem.name",
                "&aDepositar Torre");
        java.util.List<String> lore = plugin.getConfigManager().getMenus()
                .getStringList("Menus.deposit.depositItem.lore");

        ItemStack depositButton = createItem(Material.EMERALD, name, lore.toArray(new String[0]));
        setItem(slot, depositButton);
    }

    private void setupNavigation() {
        // Botão página anterior
        if (currentPage > 0) {
            int prevSlot = plugin.getConfigManager().getMenus().getInt("Menus.deposit.previusPage", 36);
            ItemStack prevButton = createItem(Material.ARROW, "§ePágina Anterior",
                    "§7Clique para ir para a página " + currentPage);
            setItem(prevSlot, prevButton);
        }

        // Botão próxima página (implementar se necessário)
        int nextSlot = plugin.getConfigManager().getMenus().getInt("Menus.deposit.nextPage", 44);
        // Por enquanto, não implementamos múltiplas páginas
    }

    @Override
    protected void setupBackButton() {
        int backSlot = plugin.getConfigManager().getMenus().getInt("Menus.deposit.backToMainInventory.slot", 49);
        ItemStack backButton = createItem(Material.ARROW, "&c⬅ Voltar", "&7Clique para voltar ao menu principal");
        setItem(backSlot, backButton);
    }

    @Override
    public void handleClick(int slot, boolean rightClick, boolean shiftClick) {
        // O InventoryListener agora trata todos os cliques baseado no nome do item
        // Este método pode ficar vazio ou ser removido
    }

    private void handleDepositClick() {
        // Verificar permissão
        if (!generator.isOwner(player) && !generator.hasPermission(player.getUniqueId(), "add_towers")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            return;
        }

        // Contar torres no inventário do menu
        int towersToDeposit = countTowersInMenu();

        if (towersToDeposit <= 0) {
            player.sendMessage("§cNenhuma torre encontrada para depositar!");
            return;
        }

        // Verificar capacidade
        if (generator.getTowers() + generator.getConstructionQueue() + towersToDeposit > generator.getCapacity()) {
            int available = generator.getCapacity() - generator.getTowers() - generator.getConstructionQueue();
            player.sendMessage("§cCapacidade insuficiente! Disponível: " + available + " torres");
            return;
        }

        // Depositar torres
        generator.addToQueue(towersToDeposit);
        plugin.getGeneratorManager().saveGenerator(generator);

        // Remover itens do menu
        removeTowersFromMenu();

        // Atualizar holograma
        plugin.getHologramManager().updateHologram(generator);

        player.sendMessage("§a" + towersToDeposit + " torres adicionadas à fila de construção!");

        // Voltar ao menu principal
        new MainMenu(plugin, player, generator).open();
    }

    private void handleDepositSlotClick(int slot) {
        // Verificar se é um slot válido de depósito
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.deposit.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");
        boolean isDepositSlot = false;

        for (String slotStr : slots) {
            try {
                if (Integer.parseInt(slotStr.trim()) == slot) {
                    isDepositSlot = true;
                    break;
                }
            } catch (NumberFormatException e) {
                // Ignorar
            }
        }

        if (isDepositSlot) {
            // Permitir interação normal do jogador com o slot
            // A lógica de validação será implementada no InventoryListener
        }
    }

    @Override
    protected void handleBackClick() {
        // Devolver itens ao jogador antes de fechar
        returnItemsToPlayer();
        // O InventoryListener já vai tratar a abertura do menu principal
        player.closeInventory();
    }

    private int countTowersInMenu() {
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.deposit.slots");
        if (slotsConfig == null) {
            return 0;
        }

        String[] slots = slotsConfig.split(",");
        int count = 0;

        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                ItemStack item = inventory.getItem(slot);

                if (item != null && isTowerItem(item)) {
                    count += item.getAmount();
                }
            } catch (NumberFormatException e) {
                // Ignorar slots inválidos
            }
        }

        return count;
    }

    private void removeTowersFromMenu() {
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.deposit.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");

        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                ItemStack item = inventory.getItem(slot);

                if (item != null && isTowerItem(item)) {
                    inventory.setItem(slot, null);
                }
            } catch (NumberFormatException e) {
                // Ignorar slots inválidos
            }
        }
    }

    private void returnItemsToPlayer() {
        String slotsConfig = plugin.getConfigManager().getMenus().getString("Menus.deposit.slots");
        if (slotsConfig == null) {
            return;
        }

        String[] slots = slotsConfig.split(",");

        for (String slotStr : slots) {
            try {
                int slot = Integer.parseInt(slotStr.trim());
                ItemStack item = inventory.getItem(slot);

                if (item != null) {
                    // Tentar adicionar ao inventário do jogador
                    if (player.getInventory().firstEmpty() != -1) {
                        player.getInventory().addItem(item);
                    } else {
                        // Inventário cheio, dropar no chão
                        player.getWorld().dropItemNaturally(player.getLocation(), item);
                    }

                    inventory.setItem(slot, null);
                }
            } catch (NumberFormatException e) {
                // Ignorar slots inválidos
            }
        }
    }

    private boolean isTowerItem(ItemStack item) {
        // Verificar se o item é uma "torre" (por exemplo, blocos de cactus ou itens
        // específicos)
        // Por simplicidade, vamos considerar qualquer item como torre
        // Em uma implementação real, você definiria quais itens são válidos
        return item.getType() == Material.CACTUS ||
                item.getType() == Material.valueOf("STAINED_CLAY") ||
                item.getType() == Material.STONE;
    }

    /**
     * Define a página atual
     */
    public void setPage(int page) {
        this.currentPage = Math.max(0, page);
    }

    /**
     * Obtém a página atual
     */
    public int getCurrentPage() {
        return currentPage;
    }
}
