package com.stoneplugins.stonecactos.listeners;

import com.stoneplugins.stonecactos.StoneCactos;
import com.stoneplugins.stonecactos.data.CactusGenerator;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.inventory.ItemStack;

public class BlockListener implements Listener {

    private final StoneCactos plugin;

    public BlockListener(StoneCactos plugin) {
        this.plugin = plugin;
    }

    @EventHandler
    public void onBlockPlace(BlockPlaceEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlockPlaced();
        ItemStack item = event.getItemInHand();

        // Verificar se é um gerador de cactos
        boolean isGenerator = plugin.getItemManager().isCactusGenerator(item);

        if (!isGenerator) {
            return;
        }

        // Verificar permissão
        if (!player.hasPermission("stonecactos.place")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Verificar se está em um plot válido (integração com PlotSquared)
        if (!plugin.getPlotSquaredIntegration().canBuild(player, block.getLocation())) {
            player.sendMessage(plugin.getConfigManager().getMessage("outOfThePlot"));
            event.setCancelled(true);
            return;
        }

        // Verificar se já existe um gerador nesta localização
        if (plugin.getGeneratorManager().hasGeneratorAt(block.getLocation())) {
            // Tentar stackar o gerador
            CactusGenerator existingGenerator = plugin.getGeneratorManager().getGenerator(block.getLocation());
            if (existingGenerator != null && existingGenerator.isOwner(player)) {
                // Cancelar o evento para não colocar o bloco
                event.setCancelled(true);
                
                // Stackar o gerador baseado na quantidade do item
                int stackAmount = item.getAmount();
                existingGenerator.addToQueue(stackAmount);
                plugin.getGeneratorManager().saveGenerator(existingGenerator);
                
                player.sendMessage("§a[StoneCactos] §fGerador stackado! +" + stackAmount + " torres adicionadas à fila de construção.");
                
                // Atualizar holograma
                plugin.getHologramManager().updateHologram(existingGenerator);
                
                // Consumir o item da mão do jogador
                if (item.getAmount() > 1) {
                    item.setAmount(item.getAmount() - stackAmount);
                } else {
                    player.getInventory().setItemInHand(null);
                }
                
                return;
            } else {
                player.sendMessage(plugin.getConfigManager().getMessage("plotAlreadyHasAGenerator"));
                event.setCancelled(true);
                return;
            }
        }

        // DEIXAR O EVENTO CONTINUAR (para colocar a skull primeiro)
        // Depois substituir por cacto usando scheduler
        plugin.getServer().getScheduler().runTask(plugin, new Runnable() {
            @Override
            public void run() {
                // Substituir a skull por cacto
                block.setType(Material.CACTUS);

                // Remover qualquer item dropado (skull)
                block.getWorld().getEntitiesByClass(org.bukkit.entity.Item.class).forEach(item -> {
                    if (item.getLocation().distance(block.getLocation()) < 2.0) {
                        // Verificar se é uma skull (gerador)
                        if (plugin.getItemManager().isCactusGenerator(item.getItemStack())) {
                            item.remove();
                        }
                    }
                });
            }
        });

        // Criar o gerador
        CactusGenerator generator = plugin.getGeneratorManager().createGenerator(
                player.getUniqueId(), block.getLocation());

        if (generator != null) {
            // Atualizar estatísticas do jogador
            // plugin.getDatabaseManager().updatePlayerStats(
            // player.getUniqueId(), 0, 0, 0.0, 1, 0);

            plugin.getLogger().info("Gerador de cactos colocado por " + player.getName() +
                    " em " + locationToString(block.getLocation()));
        } else {
            event.setCancelled(true);
        }
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        Player player = event.getPlayer();
        Block block = event.getBlock();
        Location location = block.getLocation();

        // Verificar se há um gerador nesta localização
        CactusGenerator generator = plugin.getGeneratorManager().getGenerator(location);
        if (generator != null) {
            // É um gerador - processar quebra normal
            processGeneratorBreak(event, player, block, location, generator);
            return;
        }

        // Verificar se é o bloco de baixo de um gerador (proteção)
        Location aboveLocation = location.clone().add(0, 1, 0);
        CactusGenerator generatorAbove = plugin.getGeneratorManager().getGenerator(aboveLocation);
        if (generatorAbove != null && aboveLocation.getBlock().getType() == Material.CACTUS) {
            // Bloco de baixo de um gerador - proteger
            player.sendMessage("§c[StoneCactos] §fVocê não pode quebrar o bloco de baixo de um gerador!");
            event.setCancelled(true);
            return;
        }
    }

    private void processGeneratorBreak(BlockBreakEvent event, Player player, Block block, Location location,
            CactusGenerator generator) {

        // Verificar se o jogador pode quebrar (dono ou amigo com permissão)
        if (!generator.isOwner(player) && !generator.hasPermission(player.getUniqueId(), "remove_towers")) {
            player.sendMessage(plugin.getConfigManager().getMessage("noPermission"));
            event.setCancelled(true);
            return;
        }

        // Cancelar o evento para evitar drop do cacto normal
        event.setCancelled(true);

        // Remover o gerador
        if (plugin.getGeneratorManager().removeGenerator(location)) {
            // Dropar item do gerador
            ItemStack generatorItem = plugin.getItemManager().createCactusGenerator(
                    player.getName(), generator.getTowers(), generator.getCapacity());

            block.getWorld().dropItemNaturally(location, generatorItem);

            // Dropar cactos armazenados
            if (generator.getStoredCactus() > 0) {
                ItemStack cactusItem = new ItemStack(Material.CACTUS, generator.getStoredCactus());
                block.getWorld().dropItemNaturally(location, cactusItem);
            }

            // Remover o bloco manualmente
            block.setType(Material.AIR);

            player.sendMessage(plugin.getConfigManager().getMessage("cactusRemoved"));

            plugin.getLogger().info("Gerador de cactos removido por " + player.getName() +
                    " em " + locationToString(location));
        }
    }

    /**
     * Detecta clique em blocos de cacto (geradores)
     */
    @EventHandler
    public void onBlockInteract(PlayerInteractEvent event) {
        if (event.getAction() != Action.RIGHT_CLICK_BLOCK) {
            return;
        }

        Block clickedBlock = event.getClickedBlock();
        if (clickedBlock == null || clickedBlock.getType() != Material.CACTUS) {
            return;
        }

        Player player = event.getPlayer();
        Location location = clickedBlock.getLocation();

        // Verificar se existe um gerador nesta localização
        if (plugin.getGeneratorManager().hasGeneratorAt(location)) {

            // Cancelar o evento para não quebrar o cacto
            event.setCancelled(true);

            // Abrir menu do gerador
            CactusGenerator generator = plugin.getGeneratorManager().getGenerator(location);
            if (generator != null) {
                // Verificar se o jogador pode acessar
                if (generator.isOwner(player)) {
                    // Dono tem acesso total
                    plugin.getMenuManager().openMainMenu(player, generator);
                    player.sendMessage("§a[StoneCactos] §fMenu do gerador aberto!");
                } else if (plugin.getFriendsManager().canAccess(generator, player)) {
                    // Amigo - verificar permissões configuradas
                    boolean canAccessMainMenu = plugin.getConfigManager().getConfig().getBoolean("Geral.friendPermissions.canAccessMainMenu", false);
                    boolean canAccessWarehouse = plugin.getConfigManager().getConfig().getBoolean("Geral.friendPermissions.canAccessWarehouse", true);
                    
                    if (canAccessMainMenu) {
                        plugin.getMenuManager().openMainMenu(player, generator);
                        player.sendMessage("§a[StoneCactos] §fMenu do gerador aberto!");
                    } else if (canAccessWarehouse) {
                        plugin.getMenuManager().openWarehouseMenu(player, generator);
                        player.sendMessage("§a[StoneCactos] §fArmazém aberto! (Acesso de amigo)");
                    } else {
                        player.sendMessage("§c[StoneCactos] §fVocê não tem permissão para acessar este gerador!");
                    }
                } else {
                    player.sendMessage("§c[StoneCactos] §fVocê não tem permissão para acessar este gerador!");
                }
            }
        }
    }

    /**
     * Converte uma localização em string
     */
    private String locationToString(Location location) {
        return String.format("%s:%.1f,%.1f,%.1f",
                location.getWorld().getName(),
                location.getX(),
                location.getY(),
                location.getZ());
    }
}
